package com.spup.config;

import com.spup.user.dto.Supplier;
import com.spup.core.config.BaseAppointmentConfig;
import com.spup.user.javaConfig.SupplierConfig;
import com.spup.user.config.TurnstilesProperties;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

@SpringBootTest
public class ConfigurationPropertiesTest {

    @Resource
    private TurnstilesProperties turnstilesProperties;

    @Resource
    private SupplierConfig supplierConfig;

    @Resource
    private BaseAppointmentConfig appointmentConfig;

    @Test
    public void testConfigurationProperties() {
        System.out.println("=== Testing Configuration Properties ===");

        // Test TurnstilesProperties
        System.out.println("TurnstilesProperties suppliers:");
        List<TurnstilesProperties.Supplier> suppliers1 = turnstilesProperties.getSuppliers();
        if (suppliers1 != null) {
            for (TurnstilesProperties.Supplier supplier : suppliers1) {
                System.out.println("  - Name: " + supplier.getName());
                System.out.println("    SignKey: " + supplier.getSignKey());
            }
        } else {
            System.out.println("  No suppliers found in TurnstilesProperties");
        }

        // Test SupplierConfig
        System.out.println("\nSupplierConfig suppliers:");
        List<Supplier> suppliers2 = supplierConfig.getSuppliers();
        if (suppliers2 != null) {
            for (Supplier supplier : suppliers2) {
                System.out.println("  - Name: " + supplier.getName());
                System.out.println("    SignKey: " + supplier.getSignKey());
            }
        } else {
            System.out.println("  No suppliers found in SupplierConfig");
        }

        // Test AppointmentConfig
        System.out.println("\nAppointmentConfig:");
        if (appointmentConfig.getConfig("all") != null) {
            System.out.println("  Found 'all' configuration");
            if (appointmentConfig.getConfig("all").getWeekdayList() != null) {
                System.out.println("  Weekdays: " + appointmentConfig.getConfig("all").getWeekdayList());
            }
        } else {
            System.out.println("  No 'all' configuration found");
        }
    }
}
