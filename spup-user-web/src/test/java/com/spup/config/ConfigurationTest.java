package com.spup.config;

import com.spup.user.controller.TurnstilesCheckoutController;
import com.spup.user.dto.Supplier;
import com.spup.core.config.BaseAppointmentConfig;
import com.spup.core.config.AppointmentConfigDetail;
import com.spup.user.javaConfig.SupplierConfig;
import com.spup.user.service.IAppointmentService;
import com.spup.user.config.TurnstilesProperties;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;

import javax.annotation.Resource;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@SpringBootTest
public class ConfigurationTest {

    @Resource
    private BaseAppointmentConfig appointmentConfig;

    @Resource
    private TurnstilesProperties turnstilesProperties;

    @Resource
    private SupplierConfig supplierConfig;

    @Resource
    private IAppointmentService appointmentService;

    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private TurnstilesCheckoutController turnstilesCheckoutController;

    @Test
    public void testAppointmentConfigLoaded() {
        System.out.println("=== Testing Appointment Configuration ===");

        // Check if appointmentConfig is not null
        assert appointmentConfig != null : "AppointmentConfig is null";

        // We can't directly access the config map, so we'll test individual configs

        // Test specific configurations
        testSpecificAppointmentConfig("all");
        testSpecificAppointmentConfig("team");
        testSpecificAppointmentConfig("fypd");
        testSpecificAppointmentConfig("L20230906T20230915");
        testSpecificAppointmentConfig("L20240716T20241231");
    }

    private void testSpecificAppointmentConfig(String configKey) {
        System.out.println("\nTesting config: " + configKey);
        AppointmentConfigDetail config = appointmentConfig.getConfig(configKey);

        if (config != null) {
            System.out.println("  Weekdays:");
            List<DayOfWeek> weekdays = config.getWeekdayList();
            if (weekdays != null && !weekdays.isEmpty()) {
                weekdays.forEach(day -> System.out.println("    - " + day));
            } else {
                System.out.println("    No weekdays configured");
            }

            System.out.println("  Holidays:");
            List<LocalDate> holidays = config.getHolidayList();
            if (holidays != null && !holidays.isEmpty()) {
                holidays.forEach(date -> System.out.println("    - " + date));
            } else {
                System.out.println("    No holidays configured");
            }
        } else {
            System.out.println("  Configuration not found");
        }
    }

    @Test
    public void testTurnstilesConfigLoaded() {
        System.out.println("=== Testing Turnstiles Configuration ===");

        // Check if turnstilesProperties is not null
        assert turnstilesProperties != null : "TurnstilesProperties is null";

        // Get suppliers
        List<TurnstilesProperties.Supplier> suppliers = turnstilesProperties.getSuppliers();
        assert suppliers != null : "Turnstiles suppliers list is null";

        // Print suppliers
        System.out.println("Configured turnstile suppliers:");
        suppliers.forEach(supplier -> {
            System.out.println("  - Name: " + supplier.getName());
            System.out.println("    SignKey: " + supplier.getSignKey());
        });
    }

    @Test
    public void testSupplierConfigLoaded() {
        System.out.println("=== Testing SupplierConfig Configuration ===");

        // Check if supplierConfig is not null
        assert supplierConfig != null : "SupplierConfig is null";

        // Get suppliers
        List<com.spup.user.dto.Supplier> suppliers = supplierConfig.getSuppliers();
        assert suppliers != null : "Supplier suppliers list is null";

        // Print suppliers
        System.out.println("Configured suppliers from SupplierConfig:");
        suppliers.forEach(supplier -> {
            System.out.println("  - Name: " + supplier.getName());
            System.out.println("    SignKey: " + supplier.getSignKey());
        });
    }

    @Test
    public void testAppointmentServiceWithConfig() {
        System.out.println("=== Testing AppointmentService with Configuration ===");

        // Test isAvailableByHoliday method with different dates and exhibition IDs
        String[] exhibitionIds = {"all", "team", "fypd"};
        LocalDate today = LocalDate.now();

        for (String exhibitionId : exhibitionIds) {
            boolean isAvailable = appointmentService.isAvailableByHoliday(exhibitionId, today);
            System.out.println("Exhibition ID: " + exhibitionId + ", Date: " + today + ", Is Available: " + isAvailable);

            // Test with next Monday
            LocalDate nextMonday = today.plusDays(1);
            while (nextMonday.getDayOfWeek() != DayOfWeek.MONDAY) {
                nextMonday = nextMonday.plusDays(1);
            }

            isAvailable = appointmentService.isAvailableByHoliday(exhibitionId, nextMonday);
            System.out.println("Exhibition ID: " + exhibitionId + ", Date: " + nextMonday + " (Monday), Is Available: " + isAvailable);
        }
    }

    @Test
    public void testActiveProfile() {
        System.out.println("=== Testing Active Profile ===");

        String[] activeProfiles = applicationContext.getEnvironment().getActiveProfiles();
        System.out.println("Active profiles:");
        if (activeProfiles.length == 0) {
            System.out.println("  No active profiles");
        } else {
            for (String profile : activeProfiles) {
                System.out.println("  - " + profile);
            }
        }

        String[] defaultProfiles = applicationContext.getEnvironment().getDefaultProfiles();
        System.out.println("Default profiles:");
        for (String profile : defaultProfiles) {
            System.out.println("  - " + profile);
        }
    }

    @Test
    public void testTurnstilesCheckoutController() {
        System.out.println("=== Testing TurnstilesCheckoutController with Configuration ===");

        // Check if controller is not null
        assert turnstilesCheckoutController != null : "TurnstilesCheckoutController is null";

        // Test with supplier names from configuration
        if (supplierConfig != null && supplierConfig.getSuppliers() != null) {
            for (Supplier supplier : supplierConfig.getSuppliers()) {
                String supplierName = supplier.getName();
                System.out.println("Testing supplier: " + supplierName);

                // Use reflection to access the private getSupplier method
                try {
                    java.lang.reflect.Method getSupplierMethod = TurnstilesCheckoutController.class.getDeclaredMethod("getSupplier", String.class);
                    getSupplierMethod.setAccessible(true);

                    @SuppressWarnings("unchecked")
                    Optional<Supplier> result = (Optional<Supplier>) getSupplierMethod.invoke(turnstilesCheckoutController, supplierName);

                    if (result.isPresent()) {
                        System.out.println("  Found supplier: " + result.get().getName());
                        System.out.println("  SignKey: " + result.get().getSignKey());
                    } else {
                        System.out.println("  Supplier not found in controller");
                    }
                } catch (Exception e) {
                    System.out.println("  Error testing supplier: " + e.getMessage());
                    e.printStackTrace();
                }
            }
        }

        // Test with a non-existent supplier
        try {
            java.lang.reflect.Method getSupplierMethod = TurnstilesCheckoutController.class.getDeclaredMethod("getSupplier", String.class);
            getSupplierMethod.setAccessible(true);

            @SuppressWarnings("unchecked")
            Optional<Supplier> result = (Optional<Supplier>) getSupplierMethod.invoke(turnstilesCheckoutController, "nonexistent");

            if (result.isPresent()) {
                System.out.println("  Unexpectedly found nonexistent supplier");
            } else {
                System.out.println("  Correctly did not find nonexistent supplier");
            }
        } catch (Exception e) {
            System.out.println("  Error testing nonexistent supplier: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
