package com.spup.user.init;

import com.spup.user.service.UserCounterService;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


@Component
@Order(value = 1)
public class ApplicationInit implements CommandLineRunner {

    @Resource
    UserCounterService userCounterService;

    @Override
    public void run(String... arg0) throws Exception {
        try {
            userCounterService.initMap();
            userCounterService.loadHasBook();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}
