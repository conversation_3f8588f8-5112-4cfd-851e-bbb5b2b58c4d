package com.spup.user.controller;

import com.spup.commons.api.CommonResult;
import com.spup.core.controller.BaseController;
import com.spup.data.entity.authority.AppCustomer;
import com.spup.data.entity.authority.AppCustomerContacts;
import com.spup.user.dto.AppCustomerContactsRequest;
import com.spup.user.dto.AppCustomerRequest;
import com.spup.user.service.IAppCustomerContactsService;
import com.spup.user.service.IAppCustomerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

@Tag(name = "客户管理")
@RestController
@RequestMapping("/customer")
@Slf4j
public class AppCustomerController extends BaseController {

    @Autowired
    private IAppCustomerService iAppCustomerService;

    @Autowired
    private IAppCustomerContactsService iAppCustomerContactsService;

    @Operation(summary = "获取小程序用户信息")
    @GetMapping(value = "/get")
    public CommonResult<?> get() throws ParseException {
        logRequest("获取小程序用户信息");
        String unionid = requireAuthentication();

        AppCustomer info = iAppCustomerService.get(unionid);
        if (info == null) {
            return failed("此用户不存在");
        } else {
            return success(info);
        }
    }

    @Operation(summary = "添加联系人")
    @PostMapping(value = "/addContacts")
    public CommonResult<?> addContacts(@RequestBody AppCustomerContactsRequest contacts) {
        logRequest("添加联系人", contacts);
        String unionid = requireAuthentication();

        AppCustomerContacts appCustomerContacts = new AppCustomerContacts();

        BeanUtils.copyProperties(contacts, appCustomerContacts);
        appCustomerContacts.setOwerUnionid(unionid);
        iAppCustomerContactsService.save(appCustomerContacts);
        return success(appCustomerContacts);
    }

    @Operation(summary = "删除联系人")
    @GetMapping(value = "/deleteContacts/{contactId}")
    public CommonResult<?> deleteContacts(@PathVariable Long contactId) {
        logRequest("删除联系人", contactId);
        return success(iAppCustomerContactsService.delete(contactId));
    }

    @Operation(summary = "修改联系人")
    @PostMapping(value = "/modifyContacts")
    public CommonResult<?> modifyContacts(@RequestBody AppCustomerContactsRequest contacts) {
        logRequest("修改联系人", contacts);

        Optional<AppCustomerContacts> contactsOpt = iAppCustomerContactsService.findById(contacts.getId());
        if (!contactsOpt.isPresent()) {
            return failed("联系人不存在");
        }
        AppCustomerContacts appCustomerContacts = contactsOpt.get();
        appCustomerContacts.setIdcardCategory(contacts.getIdcardCategory());
        appCustomerContacts.setName(contacts.getName());
        appCustomerContacts.setIdcardNo(contacts.getIdcardNo());
        appCustomerContacts.setPhone(contacts.getPhone());
        appCustomerContacts.setRemark(contacts.getRemark());
        return success(iAppCustomerContactsService.modify(appCustomerContacts));
    }

    @Operation(summary = "获取联系人")
    @GetMapping(value = "/getContacts")
    public CommonResult<?> getContacts() {
        logRequest("获取联系人");
        String unionid = requireAuthentication();

        return success(iAppCustomerContactsService.getListByOwner(unionid));
    }

    @Operation(summary = "获取可预约人")
    @GetMapping(value = "/getAvaildContacts")
    public CommonResult<?> getAvaildContacts() {
        logRequest("获取可预约人");
        String unionid = requireAuthentication();
        String yyyyMMdd = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        return success(iAppCustomerContactsService.getListByOwner(yyyyMMdd, unionid));
    }

    @Operation(summary = "完善资料")
    @PostMapping(value = "/saveCustomer")
    public CommonResult<?> saveCustomer(@RequestBody AppCustomerRequest customerRequest) {
        logRequest("完善资料", customerRequest);
        String unionid = requireAuthentication();

        AppCustomer appCustomer = new AppCustomer();

        BeanUtils.copyProperties(customerRequest, appCustomer);
        appCustomer.setUnionid(unionid);

        return iAppCustomerService.update(appCustomer);
    }
}
