package com.spup.user.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.spup.commons.api.CommonResult;
import com.spup.user.service.UserCounterService;
import com.spup.data.dao.appointment.AppBatchSetDao;
import com.spup.data.dao.appointment.AppBatchSetDetailDao;
import com.spup.data.dao.authority.AppManageUserDao;
import com.spup.data.entity.appointment.AppBatch;
import com.spup.data.entity.appointment.AppBatchSet;
import com.spup.data.entity.appointment.AppBatchSetDetail;
import com.spup.data.entity.authority.AppManageUser;
import com.spup.user.dto.BatchSetDetailVo;
import com.spup.enums.BatchCategoryEnum;
import com.spup.enums.BatchSetStatusEnum;
import com.spup.enums.BatchStatusEnum;
import com.spup.user.service.appointment.IAppBatchService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Tag(name = "展项预约(临时场次)")
@RestController
@RequestMapping("/tempBatch")
public class TempBatchItemOrderController {
    @Resource
    private IAppBatchService iAppBatchService;
    @Resource
    private AppBatchSetDao batchSetDao;
    @Resource
    private AppBatchSetDetailDao batchSetDetailDao;
    @Resource
    private AppManageUserDao muDao;
    @Resource
    private ObjectMapper objectMapper;
    @Resource
    private UserCounterService counter;

    @Operation(summary = "获取用户角色")
    @PostMapping(value = "/user/role")
    public CommonResult<ObjectNode> userRole( HttpServletRequest request) {
        String unionid = (String)request.getSession().getAttribute("unionid");
        Optional<AppManageUser> manageUserOpt = muDao.findByUnionid(unionid);
        ObjectNode result = objectMapper.createObjectNode();
        result.put("menuCode","");
        result.put("role",0);
        if(manageUserOpt.isPresent()){
            AppManageUser appManageUser = manageUserOpt.get();
            result.put("menuCode",appManageUser.getMenuCode());
            result.put("role",appManageUser.getRoleCode());
        }
        return CommonResult.succeeded(result);
    }

    @Operation(summary = "开启场次时刻列表")
    @PostMapping(value = "/time/list")
    public CommonResult<List<BatchSetDetailVo>> timeList()  {
        List<BatchSetDetailVo> batchSetDetailVos = new ArrayList<>();
        Optional<AppBatchSet> batchSetOpt = batchSetDao.getByBatchCategoryAndStatus(BatchCategoryEnum.TEMP_ITEM_FYPD.getCode(), BatchSetStatusEnum.RUNNING.getCode());
        if(!batchSetOpt.isPresent()){
            return CommonResult.succeeded(batchSetDetailVos);
        }
        LocalDate now = LocalDate.now();
        List<AppBatch> batchList = iAppBatchService.getListByDate(BatchCategoryEnum.TEMP_ITEM_FYPD.getCode()
                , now.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        AppBatchSet set = batchSetOpt.get();
        List<AppBatchSetDetail> detailList = batchSetDetailDao.findByBatchSetId(set.getId());
        detailList.sort(Comparator.comparing(AppBatchSetDetail::getBatchStartTime));
        batchSetDetailVos =
                detailList.stream()
                        .map(detail -> {
                            BatchSetDetailVo vo = new BatchSetDetailVo();
                            vo.setId(detail.getId());
                            vo.setName(detail.getBatchStartTime());
                            vo.setOpened(opened(batchList,detail.getBatchStartTime(),detail.getBatchEndTime()));
                            return vo;
                        }).collect(Collectors.toList());
        return CommonResult.succeeded(batchSetDetailVos);
    }

    private int opened(List<AppBatch> detailList, String batchStartTime,String batchEndTime) {
        String hhmm = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HHmm"));
        if(batchEndTime.compareTo(hhmm)<0){
            return 2;
        }
        return detailList.stream().anyMatch(batch -> batch.getBatchStartTime().equals(batchStartTime))?1:0;
    }

    @Operation(summary = "开启场次")
    @PostMapping(value = "/open")
    public CommonResult<?> open(@RequestBody List<BatchSetDetailVo> batchSetDetailVos, HttpServletRequest request) throws ParseException {
        String unionid = (String)request.getSession().getAttribute("unionid");
        LocalDateTime now = LocalDateTime.now();
        LocalTime nowTime = now.toLocalTime();
        LocalTime midDayTime = LocalTime.of( 13, 0, 0);
        String nowTimeStr = nowTime.format(DateTimeFormatter.ofPattern("HHmm"));
        String midDayTime_hhmm = midDayTime.format(DateTimeFormatter.ofPattern("HHmm"));


        for (BatchSetDetailVo vo : batchSetDetailVos) {
            AppBatch batch = createBatchEntity(vo);
            if(batch == null){
                continue;
            }
            if(nowTime.isBefore(midDayTime) && batch.getBatchStartTime().compareTo(midDayTime_hhmm)<0 //当前上午，场次上午
                    || nowTime.isAfter(midDayTime) && batch.getBatchStartTime().compareTo(midDayTime_hhmm)>=0) { //当前下午，场次下午
                if(batch.getBatchEndTime().compareTo(nowTimeStr)>0) {
                    batch.setBatchStatus(BatchStatusEnum.RUNNING.getCode());
                }
            }
            batch.setCreateBy(unionid);
            iAppBatchService.save(batch);
        }
        counter.loadTempBatch();
        return CommonResult.succeeded("");
    }

    private AppBatch createBatchEntity(BatchSetDetailVo vo) {
        LocalDate now = LocalDate.now();
        String yyyyMMdd = now.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        Optional<AppBatchSetDetail> detailOpt = batchSetDetailDao.findById(vo.getId());
        if(!detailOpt.isPresent()){
            return null;
        }
        AppBatchSetDetail detail = detailOpt.get();
        String batchNo = yyyyMMdd + detail.getBatchStartTime();
        AppBatch batch = iAppBatchService.getByNo(batchNo, BatchCategoryEnum.TEMP_ITEM_FYPD.getCode());

        if (batch == null){
            batch = new AppBatch();
            batch.setBatchNo(batchNo);
            batch.setBatchDate(yyyyMMdd);
            batch.setBatchStartTime(detail.getBatchStartTime());
            batch.setBatchEndTime(detail.getBatchEndTime());
            batch.setTicketTotal(detail.getBatchTicketTotal());
            batch.setTicketRemaining(detail.getBatchTicketTotal());
            batch.setBatchStatus(BatchStatusEnum.CLOSED.getCode());
            batch.setBatchCategory(BatchCategoryEnum.TEMP_ITEM_FYPD.getCode());
        }
        return batch;
    }
}
