package com.spup.user.controller;

import com.spup.commons.api.CommonResult;
import com.spup.core.service.IAppAppointmentInstructionsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;

@Tag(name = "参观须知等更新")
@RestController
@RequestMapping(value = "/instructions")
public class AppInstructionsController {
    @Resource
    private IAppAppointmentInstructionsService iAppAppointmentInstructionsService;

    @Operation(summary = "查询")
    @GetMapping(value="/get")
    public CommonResult<?> get () throws UnsupportedEncodingException {
        return CommonResult.succeeded(iAppAppointmentInstructionsService.get());
    }
}
