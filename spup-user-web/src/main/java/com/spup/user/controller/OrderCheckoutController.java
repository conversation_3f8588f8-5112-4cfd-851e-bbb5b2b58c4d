package com.spup.user.controller;

import com.spup.commons.api.CommonResult;
import com.spup.core.factory.OrderServiceFactory;
import com.spup.user.dto.OrderRequest;
import com.spup.enums.OrderCategoryEnum;
import com.spup.user.service.IOrderService;
import com.spup.user.service.appointment.IAppAppointmentTeamOrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;

@Tag(name = "核销")
@RestController
@RequestMapping("/check")
public class OrderCheckoutController {

    @Resource
    private OrderServiceFactory orderServiceFactory;

    @Resource
    private IAppAppointmentTeamOrderService iAppAppointmentTeamOrderService;

    @Operation(summary = "核销")
    @PostMapping(value = "/checkout")
    public CommonResult<?> checkout(@RequestBody OrderRequest orderRequest, HttpServletRequest request) {
        String unionid = (String) request.getSession().getAttribute("unionid");

        OrderCategoryEnum _enum = OrderCategoryEnum.getEnum(orderRequest.getOrderCategory());

        IOrderService service = (IOrderService) orderServiceFactory.getOrderService(_enum);

        return service.checkout(orderRequest, unionid);
    }

    @Operation(summary = "获取预约记录明细")
    @GetMapping(value = "/get/{orderCategory}/{subOrderNo}")
    public CommonResult<?> get(@PathVariable Byte orderCategory, @PathVariable String subOrderNo)
            throws ParseException {

        OrderCategoryEnum _enum = OrderCategoryEnum.getEnum(orderCategory);

        IOrderService service = (IOrderService) orderServiceFactory.getOrderService(_enum);

        return service.getSuborderDetail(subOrderNo);
    }

    @Operation(summary = "获取当日预约来访团队")
    @GetMapping(value = "/getTeamOrder")
    public CommonResult<?> getTeamOrder() {

        return iAppAppointmentTeamOrderService.getTodayTeamOrder();
    }

}
