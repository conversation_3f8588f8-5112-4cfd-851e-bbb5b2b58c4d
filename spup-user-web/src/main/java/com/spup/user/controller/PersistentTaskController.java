package com.spup.user.controller;

import java.time.Instant;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.TaskScheduler;
// import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


import com.spup.data.entity.OneTimeTask;
import com.spup.data.entity.OneTimeTask.TaskTypeEnum;
import com.spup.data.entity.appointment.AppBatch;
import com.spup.data.repository.OneTimeTaskRepository;
import com.spup.user.service.appointment.IAppBatchService;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/api/tasks")
@RequiredArgsConstructor
public class PersistentTaskController {

    private final TaskScheduler taskScheduler;
    private final OneTimeTaskRepository taskRepository;
    private final Map<String, ScheduledFuture<?>> taskMap = new ConcurrentHashMap<>();

    @Resource
    private IAppBatchService batchService;

    // @PreAuthorize("hasRole('TASK_ADMIN')")
    @PostMapping("/one-time")
    public ResponseEntity<?> scheduleOneTimeTask(
            @Valid @RequestBody ScheduleRequest request) {

        log.info("entering one time controller");

        if (request.getExecuteTime().isBefore(Instant.now())) {
            log.info("request: {}", request.getExecuteTime());
            log.info("Instant -> {}", Instant.now());
            log.info("执行时间不能早于当前时间");
            return ResponseEntity.badRequest().body("执行时间不能早于当前时间");
        }

        OneTimeTask task = new OneTimeTask();
        task.setId(UUID.randomUUID().toString());
        // taskCode: A unique identifier for the task type
        task.setTaskType(request.getTaskType());
        // taskClass: The fully qualified name of the class to be executed
        task.setTaskClass(request.getTaskClass());
        // taskData: JSON-formatted data required for task execution
        task.setExecuteTime(request.getExecuteTime());
        task.setCreatedAt(Instant.now());
        task.setStatus(OneTimeTask.TaskStatusEnum.PENDING);
        // Set the JSON string directly
        task.setTaskData(request.getTaskData());

        taskRepository.save(task);

        ScheduledFuture<?> future = taskScheduler.schedule(
                () -> executeTask(task.getId()),
                task.getExecuteTime());

        taskMap.put(task.getId(), future);

        return ResponseEntity.ok(new TaskResponse(task.getId(), task.getExecuteTime()));
    }

    @DeleteMapping("/{taskId}")
    public ResponseEntity<?> cancelTask(@PathVariable String taskId) {
        Optional<OneTimeTask> taskOpt = taskRepository.findById(taskId);
        if (!taskOpt.isPresent()) {
            return ResponseEntity.notFound().build();
        }

        OneTimeTask task = taskOpt.get();
        if (task.getStatus() != OneTimeTask.TaskStatusEnum.PENDING) {
            return ResponseEntity.badRequest().body("任务已执行，无法取消");
        }

        ScheduledFuture<?> future = taskMap.get(taskId);
        if (future != null) {
            future.cancel(true);
        }

        task.setStatus(OneTimeTask.TaskStatusEnum.CANCELLED);
        taskRepository.save(task);
        taskMap.remove(taskId);

        return ResponseEntity.noContent().build();
    }

    private void executeTask(String taskId) {
        OneTimeTask task = taskRepository.findById(taskId)
                .orElseThrow(() -> new RuntimeException("Task not found"));

        if (task.getStatus() != OneTimeTask.TaskStatusEnum.PENDING) {
            return;
        }

        task.setStatus(OneTimeTask.TaskStatusEnum.EXECUTING);
        taskRepository.save(task);

        String today = null;
        if (task.getTaskType() != null) {
            today = task.getExecuteTime().atZone(null).toLocalDate()
                    .format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        }

        try {
            List<AppBatch> existBatch = batchService.getListByDate((byte) 4, today);

            // delete exist batch from database;
            for (AppBatch batch : existBatch) {
                batch.setDeleted((byte) 1);
                batchService.save(batch);
            }

            // create new batch
            // TODO
            LocalDate startDate = LocalDate.parse(today, DateTimeFormatter.ofPattern("yyyyMMdd"));
            batchService.initItem(startDate, startDate);
            task.setStatus(OneTimeTask.TaskStatusEnum.COMPLETED);
        } catch (Exception e) {
            task.setStatus(OneTimeTask.TaskStatusEnum.FAILED);
            throw new RuntimeException("Task execution failed", e);
        } finally {
            taskRepository.save(task);
            taskMap.remove(taskId);
        }
    }

    @Getter
    @Setter
    public static class ScheduleRequest {
        @NotNull
        private Instant executeTime;

        @NotNull
        private TaskTypeEnum taskType;

        private String taskData;
        private String taskClass;

        public Instant getExecuteTime() {
            return executeTime;
        }

        public TaskTypeEnum getTaskType() {
            return taskType;
        }

        public String getTaskData() {
            return taskData;
        }

        public String getTaskClass() {
            return taskClass;
        }
    }

    @Getter
    @Setter
    public static class TaskResponse {
        @NotBlank
        private String taskId;
        @NotNull
        private Instant executeTime;

        public TaskResponse() {
        }

        public TaskResponse(String taskId, Instant executeTime) {
            this.taskId = taskId;
            this.executeTime = executeTime;
        }
    }
}
