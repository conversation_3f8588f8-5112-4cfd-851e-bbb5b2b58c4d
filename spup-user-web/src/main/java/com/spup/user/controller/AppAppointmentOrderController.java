package com.spup.user.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.spup.commons.api.CommonResult;
import com.spup.data.entity.appointment.BlackList;
import com.spup.core.dto.AppAppointmentOrderRequest;
import com.spup.user.service.BlackListService;
import com.spup.user.service.appointment.IAppAppointmentOrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;

@Tag(name = "订单")
@RestController
@RequestMapping("/order")
public class AppAppointmentOrderController {
    @Resource
    private IAppAppointmentOrderService iAppAppointmentOrderService;
    @Resource
    private BlackListService blackListService;

    @Operation(summary = "下单预约")
    @PostMapping(value = "/createOrder")
    public CommonResult<?> createOrder(@RequestBody AppAppointmentOrderRequest orderRequest, HttpServletRequest request)
            throws JsonProcessingException {
        String unionid = (String) request.getSession().getAttribute("unionid");
        if (blackListService.isInBlackList(unionid, BlackList.CategoryEnum.APPOINTMENT)) {
            return CommonResult.failed("禁止预约中...");
        }
        return iAppAppointmentOrderService.save(orderRequest, unionid);
    }

    @Operation(summary = "预约列表")
    @GetMapping(value = "/list")
    public CommonResult<?> list(HttpServletRequest request) throws ParseException {
        String unionid = (String) request.getSession().getAttribute("unionid");

        return iAppAppointmentOrderService.getList(unionid);
    }

    @Operation(summary = "取消预约")
    @GetMapping(value = "/cancel/{orderNo}")
    public CommonResult<?> cancel(@PathVariable String orderNo, HttpServletRequest request) throws ParseException {
        String unionid = (String) request.getSession().getAttribute("unionid");

        return iAppAppointmentOrderService.cancel(orderNo, unionid);
    }

    @Operation(summary = "删除预约记录")
    @GetMapping(value = "/delete/{orderNo}")
    public CommonResult<?> delete(@PathVariable String orderNo) throws ParseException {
        return iAppAppointmentOrderService.delete(orderNo);
    }

}
