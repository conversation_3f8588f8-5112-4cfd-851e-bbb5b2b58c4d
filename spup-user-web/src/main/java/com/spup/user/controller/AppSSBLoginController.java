package com.spup.user.controller;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.spup.commons.api.CommonResult;
import com.spup.core.ssbapi.AccessToken;
import com.spup.core.ssbapi.SSBApi;
import com.spup.commons.utils.JWTUtil;
import com.spup.data.entity.authority.AppCustomer;
import com.spup.user.service.IAppCustomerService;
import com.spup.user.service.IAppOperateLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.util.Calendar;
import java.util.HashMap;

@Tag(name = "随申办登录")
@RestController
@RequestMapping("/ssb")
public class AppSSBLoginController {
    // JWTUtil is now a static utility class - no injection needed
    @Resource
    private SSBApi ssbApi;
    @Resource
    private IAppCustomerService iAppCustomerService;
    @Resource
    private IAppOperateLogService iAppOperateLogService;
    @Resource
    private ObjectMapper objectMapper;

    @Operation(summary = "换取登录信息")
    @GetMapping(value="/jscode2session/{code}")
    public CommonResult<?> jscode2session (@PathVariable String code, HttpServletRequest req) throws UnsupportedEncodingException, JsonProcessingException, JsonMappingException {
        //智能导览小程序

        ObjectNode retunObj = objectMapper.createObjectNode();
        AccessToken tokenFromApi = ssbApi.getTokenFromApi(code);
        String userId = ssbApi.getBasicUser(tokenFromApi.getUser(),tokenFromApi.getAccessToken());

        String unionid = "ssb-"+userId;
        String openid = "ssb-"+userId;

        //持久化客户信息
        AppCustomer customer = iAppCustomerService.save(unionid,openid);

        retunObj.put("unionid", unionid);
        retunObj.put("openid", openid);
        retunObj.put("isNew", customer!=null?1:0);
        //token, jwt
        retunObj.put("token", JWTUtil.getToken(unionid, openid));
        try {
            iAppOperateLogService.saveLog(req);
        } catch (Exception e){
            e.printStackTrace();
        }

        return CommonResult.succeeded(retunObj);
    }

    public static void main(String[] args) throws UnsupportedEncodingException {
        // 指定token过期时间为10秒
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, 30);

        String token = JWT.create()
                .withHeader(new HashMap<>())  // Header
                .withClaim("unionid", "ojqzL0y-kslTWHDe7XGSKgo88Tc8")  // Payload
                .withClaim("openid", "o4a585ZnGo6BGCmliLlTNR3o4b_c")
                .withExpiresAt(calendar.getTime())  // 过期时间
                .sign(Algorithm.HMAC384("<EMAIL>"));  // 签名用的secret
        //eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyTmFtZSI6ImJhb2JhbyIsImV4cCI6MTY2OTQ1MzE0NCwidXNlcklkIjoyMX0.fFUYCtnXrgNItx6hoJd9mxGFr778xwftNkjBiTcxzSw
        System.out.println(token);

        //String token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyTmFtZSI6ImJhb2JhbyIsImV4cCI6MTY2OTQ1NTc0MSwidXNlcklkIjoyMX0.VMGtwfCPpAFSpGf-vyEUsCj9kOXLIaTCIuLv592Zwvk";

        JWTVerifier jwtVerifier = JWT.require(Algorithm.HMAC384("<EMAIL>")).build();
        // 解析指定的token
        DecodedJWT decodedJWT = jwtVerifier.verify(token);
        // 获取解析后的token中的payload信息
        Claim userId = decodedJWT.getClaim("unionid");
        Claim userName = decodedJWT.getClaim("openid");
        System.out.println(userId.asString());
        System.out.println(userName.asString());
        // 输出超时时间
        System.out.println(decodedJWT.getExpiresAt());

    }

}
