package com.spup.user.controller;

import java.text.ParseException;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.spup.commons.api.CommonResult;
import com.spup.core.dto.AppAppointmentTeamOrderRequest;
import com.spup.data.entity.appointment.BlackList;
import com.spup.user.service.BlackListService;
import com.spup.user.service.appointment.IAppAppointmentTeamOrderService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = "团体预约")
@RestController
@RequestMapping("/teamOrder")
public class AppAppointmentTeamOrderController {
    @Resource
    private IAppAppointmentTeamOrderService iAppAppointmentTeamOrderService;
    @Resource
    private BlackListService blackListService;

    @Operation(summary = "预约")
    @PostMapping(value = "/createOrder")
    public CommonResult<?> createOrder(@RequestBody AppAppointmentTeamOrderRequest orderRequest , HttpServletRequest request) {
        String unionid = (String)request.getSession().getAttribute("unionid");
        if(blackListService.isInBlackList(unionid, BlackList.CategoryEnum.APPOINTMENT)){
            return CommonResult.failed("禁止预约中...");
        }
        return iAppAppointmentTeamOrderService.save(orderRequest,unionid);
    }

    @Operation(summary = "预约列表")
    @GetMapping(value = "/list")
    public CommonResult<?> list(HttpServletRequest request) throws ParseException {
        String unionid = (String)request.getSession().getAttribute("unionid");

        return iAppAppointmentTeamOrderService.getList(unionid);
    }

    @Operation(summary = "取消预约")
    @GetMapping(value = "/cancel/{orderNo}")
    public CommonResult<?> cancel(@PathVariable String orderNo , HttpServletRequest request) throws ParseException {
        String unionid = (String)request.getSession().getAttribute("unionid");

        return iAppAppointmentTeamOrderService.cancel(orderNo,unionid);
    }

    @Operation(summary = "删除预约记录")
    @GetMapping(value = "/delete/{orderNo}")
    public CommonResult<?> delete(@PathVariable String orderNo , HttpServletRequest request) throws ParseException {
        String unionid = (String)request.getSession().getAttribute("unionid");
        return iAppAppointmentTeamOrderService.delete(orderNo,unionid);
    }


    @Operation(summary = "查询所有预约")
    @GetMapping(value = "/allList")
    public CommonResult<?> allList(HttpServletRequest request) {
        String unionid = (String)request.getSession().getAttribute("unionid");

        return iAppAppointmentTeamOrderService.getList(unionid);
    }
}
