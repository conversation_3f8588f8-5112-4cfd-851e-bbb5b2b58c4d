package com.spup.user.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.spup.commons.api.CommonResult;
import com.spup.core.dto.AppAppointmentOrderTemporaryExhibitionRequest;
import com.spup.user.service.appointment.IAppAppointmentOrderTemporaryExhibitionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;

@Tag(name = "特展订单")
@RestController
@RequestMapping("/exhibitionOrder")
public class AppAppointmentOrderTemporaryExhibitionController {
    @Autowired
    private IAppAppointmentOrderTemporaryExhibitionService iAppAppointmentOrderTemporaryExhibitionService;

    @Operation(summary = "下单预约")
    @PostMapping(value = "/createOrder")
    public CommonResult<?> createOrder(@RequestBody AppAppointmentOrderTemporaryExhibitionRequest orderRequest , HttpServletRequest request) throws  JsonProcessingException {
        String unionid = (String)request.getSession().getAttribute("unionid");

        return iAppAppointmentOrderTemporaryExhibitionService.save(orderRequest,unionid);
    }

    @Operation(summary = "预约列表")
    @GetMapping(value = "/list")
    public CommonResult<?> list(HttpServletRequest request) throws ParseException {
        String unionid = (String)request.getSession().getAttribute("unionid");

        return iAppAppointmentOrderTemporaryExhibitionService.getList(unionid);
    }

    @Operation(summary = "取消预约")
    @GetMapping(value = "/cancel/{orderNo}")
    public CommonResult<?> cancel(@PathVariable String orderNo , HttpServletRequest request ) throws ParseException {
        String unionid = (String)request.getSession().getAttribute("unionid");

        return iAppAppointmentOrderTemporaryExhibitionService.cancel(orderNo,unionid);
    }

    @Operation(summary = "删除预约记录")
    @GetMapping(value = "/delete/{orderNo}")
    public CommonResult<?> delete(@PathVariable String orderNo ) throws ParseException {

        return iAppAppointmentOrderTemporaryExhibitionService.delete(orderNo);
    }



}
