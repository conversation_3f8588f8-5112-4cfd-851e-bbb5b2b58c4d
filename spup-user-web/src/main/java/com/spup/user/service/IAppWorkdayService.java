package com.spup.user.service;

import com.spup.data.entity.appointment.AppWorkday;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Optional;

public interface IAppWorkdayService {
    List<AppWorkday> getListByDate(String startDate, String endDate);
    Optional<AppWorkday> getByDate(String date);

    boolean isWorkDay(String day);
    int insert(Date day);

    LocalDate getDate(LocalDate date,int plusDays,byte dayType);

    int getDayStatus(AppWorkday workday, String exhibitionNo);
}
