package com.spup.user.service.appointment.impl;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Optional;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.spup.commons.api.CommonResult;
import com.spup.commons.utils.DateTimeUtil;
import com.spup.commons.utils.NumberGenerator;
import com.spup.core.dto.AppAppointmentTeamOrderRequest;
import com.spup.core.dto.AppAppointmentTeamOrderResponse;
import com.spup.data.dao.appointment.AppAppointmentTeamOrderDao;
import com.spup.data.entity.appointment.AppAppointmentTeamOrder;
import com.spup.data.entity.appointment.AppBatch;
import com.spup.data.entity.appointment.AppTemporaryExhibition;
import com.spup.enums.OrderCategoryEnum;
import com.spup.enums.OrderStatusEnum;
import com.spup.enums.TeamOrderStatusEnum;
import com.spup.user.dto.AppTeamOrderListRequest;
import com.spup.user.dto.OrderRequest;
import com.spup.user.service.appointment.IAppAppointmentTeamOrderService;
import com.spup.user.service.appointment.IAppBatchService;
import com.spup.user.service.appointment.IAppTemporaryExhibitionService;

@Service
public class AppAppointmentTeamOrderServiceImpl implements IAppAppointmentTeamOrderService {
    @Resource
    private AppAppointmentTeamOrderDao appAppointmentTeamOrderDao;


    @Autowired
    private IAppBatchService iAppBatchService;

    @Resource
    private IAppTemporaryExhibitionService iAppTemporaryExhibitionService;

    private String lock = "lock";

    @Override
    @Transactional
    public  CommonResult<?> save(AppAppointmentTeamOrderRequest orderRequest, String unionid) {
        System.out.println(DateTimeUtil.getSysTime());
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        String batchNo = orderRequest.getBatchNo();
        //数据校验
        if(!StringUtils.hasLength(batchNo)){
            return CommonResult.failed("场次为空");
        }
        synchronized(lock) {
            AppBatch batch = iAppBatchService.getByNo(batchNo, orderRequest.getCategory());
            if (batch == null) {
                return CommonResult.failed("场次不正确");
            }
            if (batch.getTicketRemaining() < 1) {
                return CommonResult.failed("名额不足");
            }

            /**
             * 数据处理
             */
            iAppBatchService.updateRemaining(batchNo, orderRequest.getCategory(), -1);

/*
            DecimalFormat df = new DecimalFormat("00");
*/

            LocalDateTime now = LocalDateTime.now();
            String orderNo = NumberGenerator.getOrderNo();
            AppAppointmentTeamOrder order = new AppAppointmentTeamOrder();
            order.setOrderNo(orderNo);
            order.setBatchNo(batch.getBatchNo());
            order.setBatchDate(batch.getBatchDate());
            order.setBatchStartTime(batch.getBatchStartTime());
            order.setBatchEndTime(batch.getBatchEndTime());

            order.setOwnerUnionid(unionid);
            order.setOwerUnit(orderRequest.getOwerUnit());
            order.setOwerUnitCode(orderRequest.getOwerUnitCode());
            order.setOwnerName(orderRequest.getOwnerName());
            order.setOwnerPhone(orderRequest.getOwnerPhone());
            order.setVisitorsNum(orderRequest.getVisitorsNum());

            order.setOrderStatus(TeamOrderStatusEnum.SUCCESS.getCode());
            order.setOrderCategory(orderRequest.getCategory());
            order.setExhibitionNo(orderRequest.getExhibitionNo());

            order.setCreateBy(unionid);
            order.setUpdateBy(unionid);
            order.setCreateTime(now);
            order.setUpdateTime(now);

            appAppointmentTeamOrderDao.save(order);
        }

        //发送模板消息

        return CommonResult.succeeded("");
    }

    @Override
    public CommonResult<?> getList(String unionid) {
        List<AppAppointmentTeamOrder> appAppointmentOrders = appAppointmentTeamOrderDao.findByOwnerUnionid(unionid);
        appAppointmentOrders.sort((o1,o2) -> o2.getCreateTime().compareTo(o1.getCreateTime()));
        List<AppAppointmentTeamOrderResponse> orderResponses = new ArrayList<>();
        for (AppAppointmentTeamOrder order: appAppointmentOrders) {
            AppAppointmentTeamOrderResponse orderResponse = new AppAppointmentTeamOrderResponse();
            BeanUtils.copyProperties(order,orderResponse);
            orderResponses.add(orderResponse);
            if(StringUtils.hasLength(order.getExhibitionNo())){
                AppTemporaryExhibition exhibitionDetail = iAppTemporaryExhibitionService.getExhibitionDetail(order.getExhibitionNo());
                orderResponse.setExhibition(exhibitionDetail);
            }
        }

        return CommonResult.succeeded(orderResponses);
    }

    @Override
    public synchronized CommonResult<?> cancel(String orderNo,String unionid) {

        Optional<AppAppointmentTeamOrder> orderOptional =  appAppointmentTeamOrderDao.findByOrderNo(orderNo);
        if(!orderOptional.isPresent()){
            return CommonResult.failed("订单未找到");
        }

        AppAppointmentTeamOrder order = orderOptional.get();
        if(!unionid.equals(order.getOwnerUnionid())){
            return CommonResult.failed("无权操作");
        }
        order.setOrderStatus(OrderStatusEnum.CANCELED.getCode());
        order.setUpdateBy(unionid);
        appAppointmentTeamOrderDao.save(order);

        //更新时刻表
        iAppBatchService.updateRemaining(order.getBatchNo(), order.getOrderCategory(),1);

        return CommonResult.succeeded(1);
    }
    @Override
    public CommonResult<?> breaked(String orderNo,String unionid) {
        Optional<AppAppointmentTeamOrder> orderOptional =  appAppointmentTeamOrderDao.findByOrderNo(orderNo);
        if(!orderOptional.isPresent()){
            return CommonResult.failed("订单未找到");
        }
        AppAppointmentTeamOrder order = orderOptional.get();
        order.setOrderStatus(OrderStatusEnum.BREAKED.getCode());
        order.setUpdateBy(unionid);
        appAppointmentTeamOrderDao.save(order);
        //更新时刻表
        iAppBatchService.updateRemaining(order.getBatchNo(), OrderCategoryEnum.TEAM.getCode(),1);

        return CommonResult.succeeded(1);
    }
    @Override
    public CommonResult<?> delete(String orderNo,String unionid) {
        Optional<AppAppointmentTeamOrder> orderOptional =  appAppointmentTeamOrderDao.findByOrderNo(orderNo);
        if(!orderOptional.isPresent()){
            return CommonResult.failed("订单未找到");
        }
        AppAppointmentTeamOrder order = orderOptional.get();
        order.setDeleted((byte)1);
        order.setUpdateBy(unionid);
        appAppointmentTeamOrderDao.save(order);

        return CommonResult.succeeded(1);
    }

    @Override
    public CommonResult<?> getTodayTeamOrder() {
        Calendar now = Calendar.getInstance();
        String yyyMMdd = DateTimeUtil.getTime(DateTimeUtil.PATTERN_7,now);
        List<AppAppointmentTeamOrder> teamOrderByDate = getTeamOrderByDate(yyyMMdd, yyyMMdd);
        return CommonResult.succeeded(teamOrderByDate);
    }

    @Override
    public List<AppAppointmentTeamOrder> getTeamOrderByDate(String startDate, String endDate) {
        List<AppAppointmentTeamOrder> appAppointmentTeamOrders = appAppointmentTeamOrderDao.findByBatchDateBetween(startDate,endDate);
        return appAppointmentTeamOrders;
    }

    @Override
    public CommonResult<?> checkout(OrderRequest orderRequest,String unionid) {
        String suborderNo = orderRequest.getSubOrderId();
        Optional<AppAppointmentTeamOrder> orderOptional = appAppointmentTeamOrderDao.findByOrderNo(suborderNo);
        if(!orderOptional.isPresent()){
            return CommonResult.failed("未找到该预约信息");
        }
        AppAppointmentTeamOrder order = orderOptional.get();
        if(order.getOrderStatus() == OrderStatusEnum.CANCELED.getCode()){
            return CommonResult.failed("该预约已取消");
        }
        if(order.getOrderStatus() == OrderStatusEnum.FINISHED.getCode()){
            return CommonResult.failed("该预约已核销");
        }


        order.setOrderStatus(OrderStatusEnum.FINISHED.getCode());
        order.setUpdateTime(LocalDateTime.now());

        appAppointmentTeamOrderDao.save(order);
        return CommonResult.succeeded("");
    }

    @Override
    public CommonResult<?> getSuborderDetail(String subOrderId) {
        return null;
    }

    public CommonResult<?> listByParam(AppTeamOrderListRequest request){

        return CommonResult.succeeded("");
    }
}
