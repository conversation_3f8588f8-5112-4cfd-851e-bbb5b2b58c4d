package com.spup.user.service.appointment.impl;

import com.spup.data.dao.appointment.AppTemporaryExhibitionDao;
import com.spup.data.entity.appointment.AppTemporaryExhibition;
import com.spup.enums.ExhibitionStatusEnum;
import com.spup.user.service.appointment.IAppTemporaryExhibitionService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

@Service
public class AppTemporaryExhibitionServiceImpl implements IAppTemporaryExhibitionService {
    @Resource
    private AppTemporaryExhibitionDao appTemporaryExhibitionDao;

    public List<AppTemporaryExhibition> getExhibition(){
        List<AppTemporaryExhibition> appTemporaryExhibitions = appTemporaryExhibitionDao.findByStatus(ExhibitionStatusEnum.LIVING.getCode());
        return appTemporaryExhibitions;
    }

    @Override
    public AppTemporaryExhibition getExhibitionDetail(String exhibitionNo) {
        Optional<AppTemporaryExhibition> exhibitionOptional = appTemporaryExhibitionDao.getByExhibitionNo(exhibitionNo);
        if(!exhibitionOptional.isPresent()){
            return null;
        }
        return exhibitionOptional.get();
    }
}
