package com.spup.user.service.impl;

import com.spup.data.dao.CommQuestionnaireDao;
import com.spup.data.entity.CommQuestionnaire;
import com.spup.user.service.ICommQuestionnaireService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

@Service
public class CommQuestionnaireServiceImpl implements ICommQuestionnaireService {
    @Resource
    private CommQuestionnaireDao commQuestionnaireDao;
    public CommQuestionnaire getQuestionnaireById(Long id){
        Optional<CommQuestionnaire> questionnaireOptional = commQuestionnaireDao.findById(id);
        if(!questionnaireOptional.isPresent()){
            return null;
        }
        return questionnaireOptional.get();
    }



}
