package com.spup.user.service.impl;

import com.spup.core.service.impl.BaseCustomerServiceImpl;
import com.spup.data.entity.authority.AppCustomer;
import com.spup.user.service.IAppCustomerService;
import org.springframework.stereotype.Service;

/**
 * User Customer Service Implementation
 * Extends BaseCustomerServiceImpl with user-specific operations
 */
@Service
public class AppCustomerServiceImpl extends BaseCustomerServiceImpl implements IAppCustomerService {

    /**
     * User-specific: Set additional customer fields during full save
     */
    @Override
    protected void setAdditionalCustomerFields(AppCustomer customer, String unionid, String openid,
                                             String userName, String avatar, byte gender) {
        // Set user-specific fields
        customer.setUserGender(gender);
        customer.setUserAvatarSrc(avatar);
        customer.setUserName(userName);
    }
}
