package com.spup.user.service.appointment;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.spup.commons.api.CommonResult;
import com.spup.data.entity.appointment.AppAppointmentOrderTemporaryExhibition;
import com.spup.core.dto.AppAppointmentOrderTemporaryExhibitionRequest;
import com.spup.user.service.IOrderService;

import java.util.List;

public interface IAppAppointmentOrderTemporaryExhibitionService extends IOrderService {
    //对外接口
    CommonResult<?> save(AppAppointmentOrderTemporaryExhibitionRequest orderRequest, String unionid) throws JsonProcessingException;
    CommonResult<?> getList(String unionid);
    CommonResult<?> cancel(String orderNo,String unionid);
    CommonResult<?> breaked(String orderNo,String unionid);
    CommonResult<?> delete(String orderNo);
    //内部接口
    List<AppAppointmentOrderTemporaryExhibition> getListByUnionid(String unionid);
    AppAppointmentOrderTemporaryExhibition getOrderByOrderNo(String orderNo);
}
