package com.spup.user.service.impl;

import com.spup.data.dao.CommQuestionnaireAnswerDao;
import com.spup.data.entity.CommQuestionnaireAnswer;
import com.spup.user.service.ICommQuestionnaireAnswerService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class CommQuestionnaireAnswerServiceImpl implements ICommQuestionnaireAnswerService {
    @Resource
    private CommQuestionnaireAnswerDao commQuestionnaireAnswerDao;
    public CommQuestionnaireAnswer save(CommQuestionnaireAnswer answer, String unionid){
        answer.setUnionid(unionid);
        return commQuestionnaireAnswerDao.save(answer);
    }

    @Override
    public List<CommQuestionnaireAnswer> getAllAnswer(Long questionnaireId,String unionid) {
        List<CommQuestionnaireAnswer> answers = commQuestionnaireAnswerDao.findByUnionidAndQuestionnaireId(unionid,questionnaireId);
        return answers;
    }
}
