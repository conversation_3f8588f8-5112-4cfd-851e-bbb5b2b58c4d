package com.spup.user.service;

import com.spup.core.service.BaseSurroundingGoodsService;
import com.spup.data.entity.AppSurroundingGoods;
import com.spup.user.dto.GoodsListParam;
import org.springframework.data.domain.Page;

/**
 * User Surrounding Goods Service Interface
 * Extends BaseSurroundingGoodsService with user-specific operations
 */
public interface IAppSurroundingGoodsService extends BaseSurroundingGoodsService {

    /**
     * Get paginated goods list (user-specific)
     */
    Page<AppSurroundingGoods> getListByPage(GoodsListParam listParam);
}
