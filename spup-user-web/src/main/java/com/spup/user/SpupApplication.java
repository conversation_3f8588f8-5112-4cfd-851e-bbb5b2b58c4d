package com.spup.user;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

import com.spup.user.config.TurnstilesProperties;

@SpringBootApplication(exclude = {
    org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration.class,
    org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration.class
})
@ComponentScan({"com.spup.user", "com.spup.core", "com.spup.data", "com.spup.activity"})
@EnableJpaRepositories({"com.spup.data"})
@EntityScan({"com.spup.data"})
@EnableConfigurationProperties(TurnstilesProperties.class)
public class SpupApplication extends SpringBootServletInitializer {
    public static void main(String[] args) {
        SpringApplication.run(SpupApplication.class, args);
    }

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
        return builder.sources(SpupApplication.class);
    }
}
