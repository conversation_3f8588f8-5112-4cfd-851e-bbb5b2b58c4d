package com.spup.javaConfig;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

// Test timestamp: 2025-06-27 13:22:38
@Component
@ConfigurationProperties(prefix = "appointment") // 配置 文件的前缀
public class AppointmentConfig {
    Map<String,AppointmentConfigDetail> config;

    public AppointmentConfigDetail getConfig(String exhibitionId) {
        // no appointment config entry in application.yml
        if(config==null){
            System.out.println("DEBUG: AppointmentConfig is null for exhibitionId: " + exhibitionId);
            return null;
        }
        System.out.println("DEBUG: Getting config for exhibitionId: " + exhibitionId);
        return config.get(exhibitionId);
    }

    public void setConfig(Map<String, AppointmentConfigDetail> config) {
        this.config = config;
    }
}
