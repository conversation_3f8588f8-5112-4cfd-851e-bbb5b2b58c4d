package com.spup.user.javaConfig;

import com.spup.core.config.BaseOpenApiConfig;
import org.springframework.context.annotation.Configuration;

/**
 * User Module OpenAPI Configuration
 * Extends BaseOpenApiConfig with user-specific settings
 */
@Configuration
public class OpenApiConfig extends BaseOpenApiConfig {
    
    @Override
    protected String getApiTitle() {
        return "上海博物馆东馆预约系统";
    }
    
    @Override
    protected String getApiDescription() {
        return "上海博物馆东馆预约系统 - 用户端API";
    }
}
