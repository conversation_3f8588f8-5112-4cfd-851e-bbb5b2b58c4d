package com.spup.user.javaConfig;

import org.springframework.web.bind.annotation.RestControllerAdvice;

import com.spup.commons.api.CommonResult;
import com.spup.core.config.BaseExceptionControllerAdvice;

/**
 * User Module Exception Controller Advice
 * Extends BaseExceptionControllerAdvice with user-specific exception handling
 */
@RestControllerAdvice(basePackages = "com.spup.user")
public class ExceptionControllerAdvice extends BaseExceptionControllerAdvice {
    
    @Override
    protected String getModuleName() {
        return "User";
    }
    
    @Override
    protected CommonResult<?> handleModuleSpecificException(Exception e) {
        // Add user-specific exception types here
        if (e instanceof IllegalStateException) {
            return CommonResult.failed("User state error: " + e.getMessage());
        }
        
        return null; // Let base class handle it
    }
}
