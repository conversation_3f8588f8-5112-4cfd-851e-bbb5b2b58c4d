package com.spup.user.javaConfig;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.lang.NonNull;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;

import com.spup.core.config.BaseWebConfig;
import com.spup.core.interceptor.BaseTokenInterceptor;
import com.spup.user.interceptor.UserTokenInterceptor;

@Configuration
public class WebConfig extends BaseWebConfig {

    @Autowired
    private UserTokenInterceptor userTokenInterceptor;

    /**
     * Use the Spring-managed user token interceptor
     */
    @Override
    public BaseTokenInterceptor baseTokenInterceptor() {
        return userTokenInterceptor;
    }

    /**
     * Add user-specific interceptor configurations
     */
    @Override
    protected void addModuleSpecificInterceptors(InterceptorRegistry registry) {
        // User-specific interceptor patterns
        registry.addInterceptor(baseTokenInterceptor())
                .addPathPatterns("/**")
                .excludePathPatterns(getModuleSpecificExcludePatterns());
    }

    /**
     * Get user-specific exclude patterns
     */
    @Override
    protected String[] getModuleSpecificExcludePatterns() {
        return new String[]{
            "/spup/miniprogram/**",
            "/miniprogram/**",
            "/mp/**",
            "/ssb/**",
            "/turnstiles/**",
            "/spup/anniversary/**",
            "/anniversary/**",
            "/images/**"
        };
    }
    @Override
    public void addResourceHandlers(@NonNull ResourceHandlerRegistry registry) {
        // 解决静态资源无法访问
        registry.addResourceHandler("/html/**")
                .addResourceLocations("classpath:/html/");
        // 解决swagger无法访问
        registry.addResourceHandler("/swagger-ui.html")
                .addResourceLocations("classpath:/META-INF/resources/");
        // 解决swagger的js文件无法访问
        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");

    }
    /**
     * 跨域支持
     * @param registry
     */
   /* @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping(
                "/**")
                .allowedOrigins(
                        "*")
                .allowCredentials(
                        true)
                .allowedMethods(
                        "GET",
                        "POST",
                        RequestMethod.OPTIONS.name())
                .allowedHeaders("*")
                .maxAge(3600 * 24);
    }*/
}