package com.spup.user.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

@Schema(description = "客户对象")
public class AppCustomerRequest implements Serializable {
    private String unionid;
    private String realName;
    private String phone;

    @Schema(description = "证件类型，1身份证")
    private Byte cardCategory = 1;

    private String cardNo;
    @Schema(description = "性别，1男2女")
    private Byte userGender = 1;

    private String job;

    public String getUnionid() {
        return unionid;
    }

    public void setUnionid(String unionid) {
        this.unionid = unionid;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Byte getCardCategory() {
        return cardCategory;
    }

    public void setCardCategory(Byte cardCategory) {
        this.cardCategory = cardCategory;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getJob() {
        return job;
    }

    public void setJob(String job) {
        this.job = job;
    }

    public Byte getUserGender() {
        return userGender;
    }

    public void setUserGender(Byte userGender) {
        this.userGender = userGender;
    }
}
