package com.spup.user.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

@Schema(description = "联系人对象")
public class AppCustomerContactsRequest implements Serializable {
    private Long id;

    private String name;

    private String phone;
    @Schema(description = "证件类型，1身份证")
    private Byte idcardCategory;

    private String idcardNo;

    private String remark;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Byte getIdcardCategory() {
        return idcardCategory;
    }

    public void setIdcardCategory(Byte idcardCategory) {
        this.idcardCategory = idcardCategory;
    }

    public String getIdcardNo() {
        return idcardNo;
    }

    public void setIdcardNo(String idcardNo) {
        this.idcardNo = idcardNo;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
}
