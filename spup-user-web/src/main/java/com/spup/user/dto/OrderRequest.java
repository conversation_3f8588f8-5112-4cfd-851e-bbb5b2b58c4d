package com.spup.user.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

@Schema(description = "核销对象")
public class OrderRequest implements Serializable {

    private Byte orderCategory;

    private String subOrderId;

    public Byte getOrderCategory() {
        return orderCategory;
    }

    public void setOrderCategory(Byte orderCategory) {
        this.orderCategory = orderCategory;
    }

    public String getSubOrderId() {
        return subOrderId;
    }

    public void setSubOrderId(String subOrderId) {
        this.subOrderId = subOrderId;
    }
}
