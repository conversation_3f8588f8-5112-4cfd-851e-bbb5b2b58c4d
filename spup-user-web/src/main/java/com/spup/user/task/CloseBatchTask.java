package com.spup.user.task;


import com.spup.user.controller.AppBatchController;
import com.spup.user.service.appointment.IAppBatchService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;


@Configuration      //1.主要用于标记配置类，兼备Component的效果。
@EnableScheduling   // 2.开启定时任务
public class CloseBatchTask {
    private static final Logger logger = LoggerFactory.getLogger(AppBatchController.class);

    @Autowired
    private IAppBatchService iAppBatchService;
    //3.添加定时任务
    @Scheduled(cron = "0 1/5 * * * ?")
    protected void configureTasks() {
        logger.info("执行定时关闭场次任务");
        iAppBatchService.closeOverTime();
    }
}