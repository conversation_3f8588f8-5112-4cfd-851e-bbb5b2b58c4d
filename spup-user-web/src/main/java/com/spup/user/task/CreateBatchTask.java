package com.spup.user.task;


import com.spup.user.controller.AppBatchController;
import com.spup.user.service.appointment.IAppBatchService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;


@Configuration      //1.主要用于标记配置类，兼备Component的效果。
@EnableScheduling   // 2.开启定时任务
public class CreateBatchTask {
    private static final Logger logger = LoggerFactory.getLogger(AppBatchController.class);

    @Autowired
    private IAppBatchService iAppBatchService;
    //3.添加定时任务
    @Scheduled(cron = "0 0 3 1 * ?")
    protected void configureTasks() { //每月1日1点，生成下个月的场次数据
        logger.info("执行创建场次任务");

        LocalDate start =  LocalDate.now().plusMonths(1).with(TemporalAdjusters.firstDayOfMonth());

        LocalDate lastDay = LocalDate.now().plusMonths(1).with(TemporalAdjusters.lastDayOfMonth());

        iAppBatchService.initTicket(start,lastDay);
        iAppBatchService.initTeam(start,lastDay);
    }
}
