# jasypt:
#   encryptor:
#     password: ${JASPYT_ENCRYPTOR_PASSWORD}

# Disable Spring Security for user module
spup:
  security:
    enabled: false

spring:
  profiles:
    active: online
  jmx:
    enabled: false
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration
      - org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration
  mvc:
    pathmatch:
      matching-strategy: ant-path-matcher
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
  jpa:
    #关闭懒加载 否则通过id查询有问题
    properties:
      hibernate:
        "[enable_lazy_load_no_trans]": true
        dialect: org.hibernate.dialect.MySQL8Dialect
        "[format_sql]": true
    generate-ddl: true
    database-platform: org.hibernate.dialect.MySQL8Dialect
  # DevTools configuration for hot reload
  devtools:
    restart:
      enabled: true
      additional-paths: src/main/java
    livereload:
      enabled: true

# JWT Configuration
jwt:
  sign:
    string: '<EMAIL>'
  expires: 60

# SpringDoc OpenAPI Configuration
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    # Let SpringDoc auto-detect server URLs from request headers
    use-root-path: true
  # Enable server URL detection from request
  show-actuator: false

appointment:
  config:
    all:
      weekdayList:
        - MONDAY
      holidayList:
    L20230906T20230915:
      weekdayList:
        - SUNDAY
        - SATURDAY
      holidayList:
    fypd:
      weekdayList:
      holidayList:
    team:
      weekdayList:
        - SUNDAY
        - SATURDAY
        - MONDAY
      holidayList:
    L20240716T20241231:
      weekdayList:
      holidayList:
    L20250326151436123:
      weekdayList:
        - MONDAY
        - THURSDAY
        - FRIDAY
        - SATURDAY
        - SUNDAY
      holidayList:

scheduler:
  CreateTempExhibitionBatchTask:
    cron: "0 55 21 * * ?"
  OpenTempExhibitionBatchTask:
    cron: "0 51 21 * * ?"
