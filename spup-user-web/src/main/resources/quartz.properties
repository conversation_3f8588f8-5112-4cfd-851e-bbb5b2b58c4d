# 在集群中每个实例都必须有一个唯一的instanceId，但是应该有一个相同的instanceName【默认“QuartzScheduler”】
org.quartz.scheduler.instanceName = schedulerFactoryBean
# 只有在”org.quartz.scheduler.instanceId”设置为”AUTO”的时候才使用该属性设置
# 默认情况下，“org.quartz.simpl.SimpleInstanceIdGenerator”是基于instanceId和时间戳来自动生成的
org.quartz.scheduler.instanceId = AUTO
org.quartz.jobStore.class = org.quartz.impl.jdbcjobstore.JobStoreTX
#驱动程序代表理解不同数据库系统的特定方言，许多数据库使用StdJDBCDelegate
org.quartz.jobStore.driverDelegateClass = org.quartz.impl.jdbcjobstore.PostgreSQLDelegate
#数据库中表的前缀
org.quartz.jobStore.tablePrefix = QRTZ_
# 为了指示JDBCJobStore所有的JobDataMaps中的值都是字符串，并且能以“名字-值”对的方式存储而不是以复杂对象的序列化形式存储在BLOB字段中，应该设置为true(缺省方式)
org.quartz.jobStore.useProperties = false
# 实例化ThreadPool时，使用的线程类为SimpleThreadPool
org.quartz.threadPool.class = org.quartz.simpl.SimpleThreadPool
# 并发个数
org.quartz.threadPool.threadCount = 10
# 优先级
org.quartz.threadPool.threadPriority = 5