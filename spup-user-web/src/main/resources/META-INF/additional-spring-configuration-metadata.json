{"properties": [{"name": "turnstiles.suppliers", "type": "java.util.List", "description": "A description for 'turnstiles.suppliers'"}, {"name": "jasypt.encryptor.password", "type": "java.lang.String", "description": "A description for 'jasypt.encryptor.password'"}, {"name": "appointment.config.L20230906-t20230915.weekday-list", "type": "java.util.List", "description": "A description for 'appointment.config.L20230906-t20230915.weekday-list'"}, {"name": "appointment.config.L20230906-t20230915.holidayList", "type": "java.util.List", "description": "A description for 'appointment.config.L20230906-t20230915.holidayList'"}, {"name": "appointment.config.all.weekday-list", "type": "java.util.List", "description": "A description for 'appointment.config.all.weekday-list'"}, {"name": "appointment.config.all.holidayList", "type": "java.util.List", "description": "A description for 'appointment.config.all.holidayList'"}, {"name": "appointment.config.fypd.weekday-list", "type": "java.util.List", "description": "A description for 'appointment.config.fypd.weekday-list'"}, {"name": "appointment.config.fypd.holidayList", "type": "java.util.List", "description": "A description for 'appointment.config.fypd.holidayList'"}, {"name": "appointment.config.L20240716T20241231.weekday-list", "type": "java.util.List", "description": "A description for 'appointment.config.L20240716T20241231.weekday-list'"}, {"name": "appointment.config.team.weekday-list", "type": "java.util.List", "description": "A description for 'appointment.config.team.weekday-list'"}, {"name": "appointment.config.L20240716T20241231.holidayList", "type": "java.util.List", "description": "A description for 'appointment.config.L20240716T20241231.holidayList'"}, {"name": "appointment.config.team.holidayList", "type": "java.lang.String", "description": "A description for 'appointment.config.team.holidayList'"}]}