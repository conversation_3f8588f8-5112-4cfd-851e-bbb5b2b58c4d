// contract-tests/src/test/java/com/spup/contract/BaseContractTest.java
package com.spup.contract;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK)
@ActiveProfiles("test")
public abstract class BaseContractTest {

    @Test
    public void contextLoads() {
        // Simple test to verify Spring context loads correctly
    }
}