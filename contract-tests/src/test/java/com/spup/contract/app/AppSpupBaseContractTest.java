// contract-tests/src/test/java/com/spup/contract/app/AppSpupBaseContractTest.java
package com.spup.contract.app;

import com.spup.contract.ContractTestApplication;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

@SpringBootTest(
    classes = ContractTestApplication.class,
    webEnvironment = SpringBootTest.WebEnvironment.MOCK,
    properties = {
        "spring.profiles.active=test"
    }
)
@ActiveProfiles("test")
public class AppSpupBaseContractTest {

    @Test
    public void contextLoads() {
        // Simple test to verify Spring context loads correctly
    }
}