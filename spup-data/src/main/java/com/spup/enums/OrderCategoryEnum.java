package com.spup.enums;

public enum OrderCategoryEnum {
    TICKET((byte)1, "门票预约"),
    TEAM((byte)2, "团体预约"),
    ITEM_FYPD((byte)4, "展项预约-飞阅浦东"),
    EXHIBITION((byte)8, "临展"),
    EXHIBITION_TEAM((byte)16, "临展团队"),

    ; //此写法防止扩充时忘记分号

    private Byte code;
    private String name;
    private OrderCategoryEnum(Byte code, String name) {
        this.code = code;
        this.name = name;
    }

    public static OrderCategoryEnum getEnum(Byte code) {
        OrderCategoryEnum[] enums = OrderCategoryEnum.values();
        for(int i=0; i< enums.length; i++){
            OrderCategoryEnum _enum = enums[i];
            if(_enum.getCode() == code){
                return _enum;
            }
        }
        return null;
    }

    public Byte getCode() {
        return code;
    }

    public void setCode(Byte code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }



    public static void main(String[] args) {
        byte a = 1;
        System.out.println(a&-1);
    }
}
