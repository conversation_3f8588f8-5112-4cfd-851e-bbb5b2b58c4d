package com.spup.enums;

public enum BatchCategoryEnum {
    TICKET((byte)1, "门票预约"),
    TEAM((byte)2, "团体预约"),
    ITEM_FYPD((byte)4, "展项预约-飞阅浦东"),
    EXHIBITION((byte)8, "临展"),
    EXHIBITION_TEAM((byte)16, "临展团队"),
    TEMP_ITEM_FYPD((byte)32, "临时场次-展项预约-飞阅浦东"),
    ; //此写法防止扩充时忘记分号

    private Byte code;
    private String name;

    BatchCategoryEnum(Byte code, String name) {
        this.code = code;
        this.name = name;
    }

    public Byte getCode() {
        return code;
    }

    public void setCode(Byte code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}
