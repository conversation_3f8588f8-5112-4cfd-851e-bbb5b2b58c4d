package com.spup.data.entity.appointment;

import org.hibernate.annotations.*;
import org.hibernate.boot.jaxb.mapping.spi.JaxbEntityListener;

import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.Enumerated;
import javax.persistence.EnumType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;

import lombok.Getter;
import lombok.Setter;
@Entity
@Table ( name ="black_list" )
@EntityListeners(JaxbEntityListener.class)
@DynamicUpdate
@DynamicInsert
@Getter
@Setter
public class BlackList {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String unionid;
    private String name;
    private LocalDateTime lockingDateTime;
    private LocalDateTime unlockingDateTime;

    @Enumerated(EnumType.STRING)
    private StatusEnum status = StatusEnum.IN_EFFECT;

    @Enumerated(EnumType.STRING)
    private CategoryEnum category = CategoryEnum.APPOINTMENT;

    public enum StatusEnum {
        IN_EFFECT,
        EXPIRED;
    }
    public enum CategoryEnum {
        APPOINTMENT,
        ACTIVITY;
    }
}
