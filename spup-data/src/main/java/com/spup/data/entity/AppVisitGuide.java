package com.spup.data.entity;

import javax.persistence.*;
import javax.persistence.Entity;
import javax.persistence.Table;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Getter;
import lombok.Setter;

import org.hibernate.annotations.Proxy;
import org.hibernate.boot.jaxb.mapping.spi.JaxbEntityListener;
import org.hibernate.annotations.*;
import org.springframework.util.ClassUtils;
import java.time.LocalDateTime;

@SQLDelete(sql = "UPDATE app_visit_guide SET deleted=1 WHERE id=?")
@Where(clause = "deleted=0")
@Proxy(lazy = false)
@Entity
@Table(name = "app_visit_guide")
@EntityListeners(JaxbEntityListener.class)
@DynamicUpdate
@DynamicInsert
@Getter
@Setter
public class AppVisitGuide implements Serializable {

	private static final long serialVersionUID = 549779237368315315L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

	@Column(name = "spot_name")
	private String spotName;

	@Column(name = "spot_area")
	private String spotArea;

	@Column(name = "spot_content")
	private String spotContent;

	@Column(name = "sort_code")
	private Integer sortCode;

	@Column(name = "show_imgs")
	private String showImgs;

	@Column(name = "show_voices")
	private String showVoices;

	@Column(name = "tips")
	private String tips;

	@Column(name = "page_views")
	private Integer pageViews;

	@Column(name = "create_time")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime createTime;

	@Column(name = "create_by")
	private String createBy;

	@Column(name = "update_time")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime updateTime;

	@Column(name = "update_by")
	private String updateBy;

	@Column(name = "deleted")
	private Byte deleted;

	@PrePersist
	public void prePersist() {
		setCreateTime(LocalDateTime.now());
		setCreateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
		setUpdateTime(LocalDateTime.now());
		setUpdateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
	}

	@PreUpdate
	public void preUpdate() {
		setUpdateTime(LocalDateTime.now());
		setUpdateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
	}
}
