package com.spup.data.entity;

import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.Proxy;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;
import org.hibernate.boot.jaxb.mapping.spi.JaxbEntityListener;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;

@Proxy(lazy = false)
@Entity
@Table( name ="volunteer_info" )
@SQLDelete(sql = "UPDATE volunteer_info SET deleted=1 WHERE id=?")
@Where(clause = "deleted=0")
@EntityListeners(JaxbEntityListener.class)
@DynamicUpdate
@DynamicInsert
@Getter
@Setter
public class Volunteer {
    @Id
    @Column(name = "id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String uid;

    private String volunteerId;

    private String name;

    private String gender;

    private Integer age;

    private Double serviceDuration;

    private String fixedTel;

    private String phone;

    private String email;

    private String area;

    private String unit;

    private String skill;

    private VolunteerCategoryEnum category;

    private int deleted = 0;

    public enum VolunteerCategoryEnum {
        RED_SCARF,
        EXPLAINER,
        GUIDELINES,
    }
}
