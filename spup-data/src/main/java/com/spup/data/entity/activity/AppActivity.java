package com.spup.data.entity.activity;

import javax.persistence.*;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import org.hibernate.annotations.Proxy;
import org.hibernate.annotations.Comment;
import org.hibernate.boot.jaxb.mapping.spi.JaxbEntityListener;
import org.hibernate.annotations.*;
import org.springframework.util.ClassUtils;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@SQLDelete(sql = "UPDATE app_activity SET deleted=1 WHERE id=?")
@Where(clause = "deleted=0")
@Proxy(lazy = false)
@Entity
@Table(name = "app_activity")
@EntityListeners(JaxbEntityListener.class)
@DynamicUpdate
@DynamicInsert
@Getter
@Setter
public class AppActivity implements Serializable {

	private static final long serialVersionUID = 4840785523714526881L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

	@Column(name = "title")
	@Comment("活动标题")
	private String title;

	@Column(name = "content")
	@Comment("活动内容")
	private String content;

	@Column(name = "conver_picture")
	@Comment("活动封面图")
	private String converPicture;

	@Column(name = "address")
	@Comment("活动地点")
	private String address;

	@Column(name = "type")
	@Comment("活动类型")
	private Byte type;

	@Column(name = "start_time")
	@Comment("活动开始时间")
	private LocalDateTime startTime;

	@Column(name = "end_time")
	@Comment("活动结束时间")
	private LocalDateTime endTime;

	@Column(name = "sort")
	@Comment("排序值")
	private Integer sort;

	@Column(name = "status")
	@Comment("活动状态")
	private ActivityStatusEnum status;

	@Column(name = "create_time")
	private LocalDateTime createTime;

	@Column(name = "create_by")
	private String createBy;

	@Column(name = "update_time")
	private LocalDateTime updateTime;

	@Column(name = "update_by")
	private String updateBy;

	@Column(name = "deleted")
	private Byte deleted;

	public enum ActivityStatusEnum {
		PREVIEW(1),
		Running(2);

		@SuppressWarnings("unused")
		private Integer value;

		ActivityStatusEnum(Integer value) {
			this.value = value;
		}
	}

	@PrePersist
	public void prePersist() {
		setCreateTime(LocalDateTime.now());
		setCreateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
		setUpdateTime(LocalDateTime.now());
		setUpdateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
	}

	@PreUpdate
	public void preUpdate() {
		setUpdateTime(LocalDateTime.now());
		setUpdateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
	}
}
