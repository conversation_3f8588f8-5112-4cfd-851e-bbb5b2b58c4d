package com.spup.data.entity.activity;

import io.hypersistence.utils.hibernate.type.json.JsonStringType;
import lombok.Getter;
import lombok.Setter;

import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.hibernate.annotations.Where;
import org.hibernate.boot.jaxb.mapping.spi.JaxbEntityListener;

import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Entity
@Table( name  = "activity_round_info")
@SQLDelete(sql = "UPDATE activity_round_info SET deleted=1 WHERE id=?")
@EntityListeners(JaxbEntityListener.class)
@TypeDef(name = "jsonString",typeClass = JsonStringType.class)
@Where(clause = "deleted=0")
@DynamicUpdate
@DynamicInsert
@Getter
@Setter
public class ActivityRound {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;
    private String actRoundId;
    private String activityId;
    @NotBlank(message = "场次名称不能为空")
    private String actRoundInfo;
    @NotNull(message = "报名开始时间不能为空")
    private LocalDateTime actRoundSubmitStartDateTime;
    @NotNull(message = "报名结束时间不能为空")
    private LocalDateTime actRoundSubmitEndDateTime;
    @NotNull(message = "场次开始时间不能为空")
    private LocalDateTime actRoundStartDateTime;
    @NotNull(message = "场次结束时间不能为空")
    private LocalDateTime actRoundEndDateTime;

    private Integer actRoundSubmitNumber = 0;
    @NotNull(message = "最大报名人数不能为空")
    private Integer actRoundMaxSubmitNum;
    @Enumerated(EnumType.STRING)
    private ActRoundStatusEnum status = ActRoundStatusEnum.READY;
    @Enumerated(EnumType.STRING)
    private ActRoundTypeEnum type = ActRoundTypeEnum.NORMAL;

    @Type(type="jsonString")
    private String otherInfo;
    @NotNull
    private Integer deleted = 0;

    private String createBy;
    private LocalDateTime createOn;
    private String updateBy;
    private LocalDateTime updateOn;

    public enum ActRoundStatusEnum {
        READY, SUBMITTING, WAITING, SUBMITPAUSED, RUNNING, CLOSED, DEPRECATED;
    }

    public enum ActRoundTypeEnum {
        NORMAL, CHILD, NOLIMIT,SPECIAL;
    }

    @PrePersist
    public void insert() {
        setCreateOn(LocalDateTime.now());
        setCreateBy(this.getClass().getSimpleName());
        setUpdateOn(LocalDateTime.now());
        setUpdateBy(this.getClass().getSimpleName());
    }

    @PreUpdate
    public void update() {
        setUpdateOn(LocalDateTime.now());
        setUpdateBy(this.getClass().getSimpleName());
    }

    @Override
    public String toString() {
        return "ActivityRound{" +
                "id=" + id +
                ", actRoundId='" + actRoundId + '\'' +
                ", activityId='" + activityId + '\'' +
                ", actRoundInfo='" + actRoundInfo + '\'' +
                ", actRoundSubmitStartDateTime=" + actRoundSubmitStartDateTime +
                ", actRoundSubmitEndDateTime=" + actRoundSubmitEndDateTime +
                ", actRoundStartDateTime=" + actRoundStartDateTime +
                ", actRoundEndDateTime=" + actRoundEndDateTime +
                ", actRoundSubmitNumber=" + actRoundSubmitNumber +
                ", actRoundMaxSubmitNum=" + actRoundMaxSubmitNum +
                ", status=" + status +
                ", type=" + type +
                ", deleted=" + deleted +
                ", createBy='" + createBy + '\'' +
                ", createOn=" + createOn +
                ", updateBy='" + updateBy + '\'' +
                ", updateOn=" + updateOn +
                '}';
    }
}
