package com.spup.data.entity;

import com.fasterxml.jackson.databind.node.ArrayNode;
import io.hypersistence.utils.hibernate.type.json.JsonStringType;
import lombok.Getter;
import lombok.Setter;

import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.hibernate.boot.jaxb.mapping.spi.JaxbEntityListener;

import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Column;
import javax.persistence.Enumerated;
import javax.persistence.EnumType;

@Entity
@Table ( name ="art_center_info" )
@TypeDef(name = "jsonString",typeClass = JsonStringType.class)
@EntityListeners(JaxbEntityListener.class)
@DynamicUpdate
@DynamicInsert
@Getter
@Setter
public class ArtCenterInfo {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Enumerated(EnumType.STRING)
    private SectionEnum section;
    @Column(name = "introduction", length=2000)
    private String introduction;

    private String openTime;

    private String address;

    private String metro;

    private String traffic;

    @Type(type="jsonString")
    private ArrayNode picInfo;

    public enum SectionEnum {
        APPONINTMENT,
        ABOUTUS;
    }

}
