package com.spup.data.entity;

import javax.persistence.*;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;

import org.hibernate.annotations.Proxy;
import org.hibernate.boot.jaxb.mapping.spi.JaxbEntityListener;
import org.hibernate.annotations.*;
import org.springframework.util.ClassUtils;
import java.time.LocalDateTime;

@SQLDelete(sql = "UPDATE app_comments SET deleted=1 WHERE id=?")
@Where(clause = "deleted=0")
@Proxy(lazy = false)
@Entity
@Table(name = "app_comments")
@EntityListeners(JaxbEntityListener.class)
@DynamicUpdate
@DynamicInsert
@Getter
@Setter
public class AppComments implements Serializable {

	private static final long serialVersionUID = 8176865227536733370L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

	@Column(name = "customer")
	private String customer;

	@Column(name = "purpose")
	private String purpose;

	@Column(name = "content")
	private String content;

	@Column(name = "visit_time")
	private String visitTime;

	@Enumerated(EnumType.STRING)
	private CommentStatusEnum status = CommentStatusEnum.PROCESSING;

	public enum CommentStatusEnum {
		PROCESSING,
		PROCESSED;
	}

	@Column(name = "create_time")
	@Comment("订单生成时间")
	private LocalDateTime createTime;

	@Column(name = "create_by")
	@Comment("创建者unionid-可为用户、店员、店长、系统等")
	private String createBy;

	@Column(name = "update_time")
	@Comment("数据更新时间")
	private LocalDateTime updateTime;

	@Column(name = "update_by")
	@Comment("更新者unionid")
	private String updateBy;

	@Column(name = "deleted")
	@Comment("逻辑删除，默认为0未删除，1已删除")
	private Byte deleted;

	@PrePersist
	public void prePersist() {
		setCreateTime(LocalDateTime.now());
		setCreateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
		setUpdateTime(LocalDateTime.now());
		setUpdateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
	}

	@PreUpdate
	public void preUpdate() {
		setUpdateTime(LocalDateTime.now());
		setUpdateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
	}
}
