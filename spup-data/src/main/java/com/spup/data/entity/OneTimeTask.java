package com.spup.data.entity;

import java.time.Instant;

import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.hibernate.annotations.Where;

import io.hypersistence.utils.hibernate.type.json.JsonStringType;
import lombok.Data;

@Entity
@Table(name = "one_time_tasks")
@SQLDelete(sql = "UPDATE one_time_tasks SET deleted=1 WHERE id=?")
@Where(clause = "deleted=0")
@Data
@TypeDef(name = "jsonString", typeClass = JsonStringType.class)
public class OneTimeTask {
    @Id
    private String id;

    @Enumerated(EnumType.STRING)
    private TaskTypeEnum taskType;
    private String taskClass; // 可执行类的全限定名
    
    @Type(type="jsonString")
    private String taskData; // JSON格式的任务参数

    private Instant executeTime;
    private Instant createdAt;
    @Enumerated(EnumType.STRING)
    private TaskStatusEnum status;

    private Integer deleted = 0;

    public enum TaskStatusEnum {
        PENDING, EXECUTING, COMPLETED, FAILED, CANCELLED
    }

    public enum TaskTypeEnum {
        CREATE_TEAM_BATCH, CREATE_PERSONAL_BATCH, CREATE_ITEM_BATCH,
        CLOSE_TEAM_BATCH, CLOSE_ITEM_BATCH, CLOSE_PERSONAL_BATCH,
        OPEN_TEAM_BATCH, OPEN_ITEM_BATCH, OPEN_PERSONAL_BATCH,
        UPDATE_ITEM_BATCH, UPDATE_PERSONAL_BATCH, UPDATE_TEAM_BATCH,
        OVERWRITE_ITEM_BATCH;
    }
}