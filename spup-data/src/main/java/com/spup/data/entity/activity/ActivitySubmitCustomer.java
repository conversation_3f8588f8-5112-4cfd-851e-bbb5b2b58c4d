package com.spup.data.entity.activity;

import java.time.LocalDateTime;

import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import javax.persistence.Table;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;
import org.hibernate.boot.jaxb.mapping.spi.JaxbEntityListener;

import lombok.Getter;
import lombok.Setter;

@Entity
@Table( name = "activity_submit_customer")
@SQLDelete(sql = "UPDATE activity_submit_customer SET deleted=1 WHERE id=?")
@Where(clause = "deleted=0")
@EntityListeners(JaxbEntityListener.class)
@DynamicUpdate
@DynamicInsert
@Getter
@Setter
public class ActivitySubmitCustomer {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String submitId;

    @NotEmpty
    private String unionid;
    @NotEmpty
    private String username;
    @Enumerated(EnumType.STRING)
    @NotNull
    private SubmitCustomerPassTypeEnum passType = SubmitCustomerPassTypeEnum.IDCARD;
    @NotEmpty
    private String passString;
    @NotEmpty
    private String phoneString;
    @NotNull
    private Integer age;
    @NotNull
    private Integer gender;
    @Enumerated(EnumType.STRING)
    private SubmitCustomerStatusEnum status = SubmitCustomerStatusEnum.SUBMITTED;
    @Enumerated(EnumType.STRING)
    private SubmitCustomerTypeEnum type = SubmitCustomerTypeEnum.ADULT;

    private LocalDateTime checkInDateTime = null;
    @NotEmpty
    private String actRoundId;
    private String createBy = "";
    private LocalDateTime createOn;
    private String updateBy;
    private LocalDateTime updateOn;
    @NotNull
    private Integer deleted = 0;

    public enum SubmitCustomerStatusEnum {
        SUBMITTED, CHECKEDIN, CANCELLED;
    }

    public enum SubmitCustomerTypeEnum {
        ADULT, CHILD, SPECIAL;
    }

    @PrePersist
    public void insert() {
        setCreateOn(LocalDateTime.now());
        setCreateBy(getCreateBy().isEmpty()?this.getClass().getSimpleName():getCreateBy());
        setUpdateOn(LocalDateTime.now());
        setUpdateBy(this.getClass().getSimpleName());
    }

    @PreUpdate
    public void update() {
        setUpdateOn(LocalDateTime.now());
        setUpdateBy(this.getClass().getSimpleName());
    }



    public  enum SubmitCustomerPassTypeEnum {
        IDCARD,PASSPORT,OTHER;
    }

    @Override
    public String toString() {
        return "ActivitySubmitCustomer{" +
                "id=" + id +
                ", unionid='" + unionid + '\'' +
                ", username='" + username + '\'' +
                ", passType=" + passType +
                ", passString='" + passString + '\'' +
                ", phoneString='" + phoneString + '\'' +
                ", age=" + age +
                ", gender=" + gender +
                ", status=" + status +
                ", type=" + type +
                ", checkInDateTime=" + checkInDateTime +
                ", actRoundId='" + actRoundId + '\'' +
                ", createBy='" + createBy + '\'' +
                ", createOn=" + createOn +
                ", updateBy='" + updateBy + '\'' +
                ", updateOn=" + updateOn +
                '}';
    }
}
