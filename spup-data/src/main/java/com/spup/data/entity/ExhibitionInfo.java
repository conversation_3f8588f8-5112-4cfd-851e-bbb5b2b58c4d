package com.spup.data.entity;

import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;
import org.hibernate.boot.jaxb.mapping.spi.JaxbEntityListener;

import lombok.Getter;
import lombok.Setter;

@Entity
@Table( name ="exhibition_info" )
@SQLDelete(sql = "UPDATE exhibition_info SET deleted=1 WHERE id=?")
@Where(clause = "deleted=0")
@EntityListeners(JaxbEntityListener.class)
@DynamicUpdate
@DynamicInsert
@Getter
@Setter
public class ExhibitionInfo {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String exhibitionId; //0001   0002
    @Enumerated(EnumType.STRING)
    private AppointmentType exhibitionType; //personal  group
    @Enumerated(EnumType.STRING)
    private ExhibitionStatus exhibitionStatus;
    private int deleted = 0;

    public enum AppointmentType {
        personal,
        group;
    }

    public enum ExhibitionStatus {
        open,
        close;
    }
}
