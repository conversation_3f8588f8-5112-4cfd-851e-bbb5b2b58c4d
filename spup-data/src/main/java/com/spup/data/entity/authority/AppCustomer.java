package com.spup.data.entity.authority;

import javax.persistence.*;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import org.hibernate.annotations.Proxy;
import org.hibernate.annotations.Comment;
import org.hibernate.boot.jaxb.mapping.spi.JaxbEntityListener;
import org.hibernate.annotations.*;
import org.springframework.util.ClassUtils;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@SQLDelete(sql = "UPDATE app_customer SET deleted=1 WHERE id=?")
@Where(clause = "deleted=0")
@Proxy(lazy = false)
@Entity
@Table ( name ="app_customer" )
@EntityListeners(JaxbEntityListener.class)
@DynamicUpdate
@DynamicInsert
@Getter
@Setter
public class AppCustomer  implements Serializable {

	private static final long serialVersionUID =  3016856395179053962L;

   	@Id 
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

   	@Column(name = "unionid")
	@Comment("微信唯一识别码")
	private String unionid;

   	@Column(name = "mini_openid")
	@Comment("小程序openid")
	private String miniOpenid;

   	@Column(name = "customer_id" )
	private String customerId;

   	@Column(name = "user_name")
	@Comment("用户姓名")
	private String userName;

   	@Column(name = "user_gender")
	@Comment("用户性别： 1， 男， 2 女， 0 未知")
	private Byte userGender;

   	@Column(name = "user_birthdate")
	@Comment("用户生日")
	private String userBirthdate;

   	@Column(name = "user_avatar_src")
	@Comment("用户本地头像，可供用户替换头像；2作为备用")
	private String userAvatarSrc;

   	@Column(name = "real_name" )
	private String realName;

   	@Column(name = "phone" )
	private String phone;

   	@Column(name = "card_category" )
	private Byte cardCategory;

   	@Column(name = "card_no" )
	private String cardNo;

   	@Column(name = "job" )
	private String job;

	private Integer breakedTotalNum = 0;

	private Integer breakedNum = 0;
   	@Column(name = "create_by")
	@Comment("系统创建-给相关类名")
	private String createBy;

   	@Column(name = "create_time")
	@Comment("记录创建时间")
	private LocalDateTime createTime;

   	@Column(name = "update_by")
	@Comment("创建者unionid-可为用户、管理员、系统等")
	private String updateBy;

   	@Column(name = "update_time")
	@Comment("记录修改时间")
	private LocalDateTime updateTime;

   	@Column(name = "deleted")
	@Comment("逻辑删除，默认为0未删除，1已删除")
	private Byte deleted;


	@PrePersist
	public void prePersist() {
		setCreateTime(LocalDateTime.now());
		setCreateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "." + Thread.currentThread().getStackTrace()[1].getMethodName());
		setUpdateTime(LocalDateTime.now());
		setUpdateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "." + Thread.currentThread().getStackTrace()[1].getMethodName());
	}

	@PreUpdate
	public void preUpdate() {
		setUpdateTime(LocalDateTime.now());
		setUpdateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "." + Thread.currentThread().getStackTrace()[1].getMethodName());
	}
}
