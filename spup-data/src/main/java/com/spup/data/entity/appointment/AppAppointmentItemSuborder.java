package com.spup.data.entity.appointment;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import javax.persistence.Table;
import java.io.Serializable;

import org.hibernate.annotations.Comment;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.Proxy;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;
import org.hibernate.boot.jaxb.mapping.spi.JaxbEntityListener;
import org.springframework.util.ClassUtils;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import lombok.Getter;
import lombok.Setter;

@SQLDelete(sql = "UPDATE app_appointment_item_suborder SET deleted=1 WHERE id=?")
@Where(clause = "deleted=0")
@Proxy(lazy = false)
@Entity
@Table(name = "app_appointment_item_suborder")
@EntityListeners(JaxbEntityListener.class)
@DynamicUpdate
@DynamicInsert
@Getter
@Setter
public class AppAppointmentItemSuborder implements Serializable {

	private static final long serialVersionUID = 6700400268662209441L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

	@Column(name = "order_no")
	@Comment("订单编号")
	private String orderNo;

	@Column(name = "suborder_no")
	@Comment("子订单编号")
	private String suborderNo;

	@Column(name = "batch_no")
	private String batchNo;

	@Column(name = "batch_date")
	private String batchDate;

	@Column(name = "batch_start_time")
	private String batchStartTimeStr;

	@Column(name = "batch_end_time")
	private String batchEndTimeStr;

	@Column(name = "onwer_unionid")
	@Comment("用户小程序唯一id")
	private String onwerUnionid;

	@Column(name = "contacts_name")
	private String contactsName;

	@Column(name = "contacts_phone")
	private String contactsPhone;

	@Column(name = "contacts_idcard_category")
	private Byte contactsIdcardCategory;

	@Column(name = "contacts_idcard_no")
	private String contactsIdcardNo;

	@Column(name = "seat_no")
	private Byte seatNo;

	@Column(name = "suborder_status")
	@Comment("子订单状态-无符号int，状态可以为负数，表明异常状态；1：待支付，2: 待服务，4: 服务中，8: 已完成。 -1：售后中； -2: 已退款； -4: 已取消；")
	private Short suborderStatus;

	@Column(name = "create_by")
	@Comment("创建者unionid-可为用户、店员、店长、系统等")
	private String createBy;

	@Column(name = "create_time")
	private LocalDateTime createTime;

	@Column(name = "update_by")
	@Comment("创建者unionid-可为用户、店员、店长、系统等")
	private String updateBy;

	@Column(name = "update_time")
	private LocalDateTime updateTime;

	@Column(name = "deleted")
	@Comment("逻辑删除，默认为0未删除，1已删除")
	private Byte deleted;

	public LocalDateTime getBatchStartTime() {
		return LocalDateTime.parse(this.batchDate + this.batchStartTimeStr,
				DateTimeFormatter.ofPattern("yyyyMMddHHmm"));
	}

	public LocalDateTime getBatchEndTime() {
		return LocalDateTime.parse(this.batchDate + this.batchEndTimeStr, 
				DateTimeFormatter.ofPattern("yyyyMMddHHmm"));
	}

	@PrePersist
	public void prePersist() {
		setCreateTime(LocalDateTime.now());
		setCreateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
		setUpdateTime(LocalDateTime.now());
		setUpdateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
	}

	@PreUpdate
	public void preUpdate() {
		setUpdateTime(LocalDateTime.now());
		setUpdateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
	}
}
