package com.spup.data.entity.mp;

import org.hibernate.annotations.*;
import org.hibernate.boot.jaxb.mapping.spi.JaxbEntityListener;
import org.springframework.util.ClassUtils;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

@SQLDelete(sql = "UPDATE mp_datacube_everday SET deleted=1 WHERE id=?")
@Where(clause = "deleted=0")
@Entity
@Table(name = "mp_datacube_everday")
@EntityListeners(JaxbEntityListener.class)
@DynamicUpdate
@DynamicInsert
@Getter
@Setter
public class MpDatacubeEverday implements Serializable {

	private static final long serialVersionUID = 4840785523714526881L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

	@Column(name = "analysis_date")
	private LocalDate analysisDate;

	@Column(name = "user_cumulate")
	private Integer userCumulate;

	@Column(name = "article_total")
	private Integer articleTotal;

	@Column(name = "article_read_total")
	private Integer articleReadTotal;

	@Column(name = "user_cumulate_json")
	@Lob
	private String userCumulateJson;

	@Column(name = "article_summary_json")
	@Lob
	private String articleSummaryJson;

	@Column(name = "create_on")
	private LocalDateTime createOn;

	@Column(name = "create_by")
	private String createBy;

	@Column(name = "update_on")
	private LocalDateTime updateOn;

	@Column(name = "update_by")
	private String updateBy;

	@Column(name = "deleted")
	private Byte deleted;

	@PrePersist
	public void prePersist() {
		setCreateOn(LocalDateTime.now());
		setCreateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
		setUpdateOn(LocalDateTime.now());
		setUpdateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
	}

	@PreUpdate
	public void preUpdate() {
		setUpdateOn(LocalDateTime.now());
		setUpdateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
	}
}
