package com.spup.data.dao.appointment;

import com.spup.data.entity.appointment.AppAppointmentTeamOffline;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

/**
 * @Description  app_appointment_team_offline Dao 
 * <AUTHOR>  
 * @Date 2023-12-19 
 */

@Repository
public interface AppAppointmentTeamOfflineDao  extends JpaRepository<AppAppointmentTeamOffline, Long> , JpaSpecificationExecutor<AppAppointmentTeamOffline> {


}
