package com.spup.data.dao.mp;

import com.spup.data.entity.mp.MpDatacubeEverday;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.time.LocalDate;
import java.util.Optional;

public interface MpDatacubeEverdayDao extends JpaRepository<MpDatacubeEverday, Long>
                                    , JpaSpecificationExecutor<MpDatacubeEverday> {
    Optional<MpDatacubeEverday> getByAnalysisDate(LocalDate analysisDate);
}
