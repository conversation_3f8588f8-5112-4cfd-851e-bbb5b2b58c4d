package com.spup.data.dao.authority;

import com.spup.data.entity.authority.AppManageUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface AppManageUserDao extends JpaRepository<AppManageUser, Long>, JpaSpecificationExecutor<AppManageUser> {
    Optional<AppManageUser> findByOpenId(String openId);

    Optional<AppManageUser> findByUnionid(String unionid);

}
