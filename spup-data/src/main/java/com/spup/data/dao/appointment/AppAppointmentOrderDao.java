package com.spup.data.dao.appointment;

import com.spup.data.entity.appointment.AppAppointmentOrder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * @Description  app_appointment_order Dao 
 * <AUTHOR>  
 * @Date 2023-12-14 
 */

@Repository
public interface AppAppointmentOrderDao  extends JpaRepository<AppAppointmentOrder, Long> , JpaSpecificationExecutor<AppAppointmentOrder> {
    Optional<AppAppointmentOrder> getByOrderNo(String orderNo);
    List<AppAppointmentOrder> findByOwnerUnionid(String ownerUnionid);
    List<AppAppointmentOrder> findByBatchDateBetween(String start, String end);

}
