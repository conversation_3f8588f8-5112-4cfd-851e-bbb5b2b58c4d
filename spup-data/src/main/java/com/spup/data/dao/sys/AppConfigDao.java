package com.spup.data.dao.sys;

import com.spup.data.entity.sys.AppConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AppConfigDao  extends JpaRepository<AppConfig, Long> , JpaSpecificationExecutor<AppConfig> {
    List<AppConfig> findByGroupNo(String groupNo);
}
