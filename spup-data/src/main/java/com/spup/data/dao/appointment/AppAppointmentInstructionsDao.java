package com.spup.data.dao.appointment;

import com.spup.data.entity.appointment.AppAppointmentInstructions;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

/**
 * @Description  app_appointment_instructions Dao 
 * <AUTHOR>  
 * @Date 2023-12-14 
 */

@Repository
public interface AppAppointmentInstructionsDao  extends JpaRepository<AppAppointmentInstructions, Long> , JpaSpecificationExecutor<AppAppointmentInstructions> {


}
