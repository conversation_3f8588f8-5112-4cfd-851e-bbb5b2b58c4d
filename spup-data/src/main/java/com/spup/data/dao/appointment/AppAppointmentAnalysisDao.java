package com.spup.data.dao.appointment;

import com.spup.data.entity.appointment.AppAppointmentAnalysis;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Description  app_appointment_analysis Dao 
 * <AUTHOR>  
 * @Date 2023-12-14 
 */

@Repository
public interface AppAppointmentAnalysisDao  extends JpaRepository<AppAppointmentAnalysis, Long> , JpaSpecificationExecutor<AppAppointmentAnalysis> {
    List<AppAppointmentAnalysis> findByAnalysisDateBetweenOrderByAnalysisDateDesc(String startDate,String endDate);
}
