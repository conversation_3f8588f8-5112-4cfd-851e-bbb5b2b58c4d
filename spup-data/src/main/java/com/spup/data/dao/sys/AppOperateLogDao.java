package com.spup.data.dao.sys;

import com.spup.data.entity.sys.AppOperateLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface AppOperateLogDao  extends JpaRepository<AppOperateLog, Long> , JpaSpecificationExecutor<AppOperateLog> {
    List<AppOperateLog> findByOperateTimeBetween(LocalDateTime start,LocalDateTime end);
    Long countByOperateTimeBetween(LocalDateTime start,LocalDateTime end);
}
