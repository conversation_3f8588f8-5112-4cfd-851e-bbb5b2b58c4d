package com.spup.data.dao.appointment;

import com.spup.data.entity.appointment.AppTemporaryExhibition;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * @Description  app_temporary_exhibition Dao 
 * <AUTHOR>  
 * @Date 2023-12-14 
 */

@Repository
public interface AppTemporaryExhibitionDao  extends JpaRepository<AppTemporaryExhibition, Long> , JpaSpecificationExecutor<AppTemporaryExhibition> {
    List<AppTemporaryExhibition> findByStatus(Byte status);
    Optional<AppTemporaryExhibition> getByExhibitionNoAndStatus(String exhibitionNo, Byte status);
    Optional<AppTemporaryExhibition> getByExhibitionNo(String exhibitionNo);

}
