package com.spup.data.dao.appointment;

import com.spup.data.entity.appointment.AppBatchSetDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Description  app_batch_set_detail Dao 
 * <AUTHOR>  
 * @Date 2023-12-14 
 */

@Repository
public interface AppBatchSetDetailDao  extends JpaRepository<AppBatchSetDetail, Long> , JpaSpecificationExecutor<AppBatchSetDetail> {
    List<AppBatchSetDetail> findByBatchSetId(Long batchSetId);
}
