package com.spup.data.dao.mp;

import com.spup.data.entity.mp.MpDatacubeUserSummary;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.time.LocalDate;
import java.util.Optional;

public interface MpDatacubeUserSummaryDao extends JpaRepository<MpDatacubeUserSummary, Long>
                                    , JpaSpecificationExecutor<MpDatacubeUserSummary> {
    Optional<MpDatacubeUserSummary> getByAnalysisDate(LocalDate analysisDate);

}
