package com.spup.data.dao.appointment;

import com.spup.data.entity.appointment.AppWorkday;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * @Description  app_workday Dao 
 * <AUTHOR>  
 * @Date 2023-12-14 
 */

@Repository
public interface AppWorkdayDao  extends JpaRepository<AppWorkday, Long> , JpaSpecificationExecutor<AppWorkday> {
    List<AppWorkday> findByDayBetween(String start,String end);
    Optional<AppWorkday> getByDay(String day);
}
