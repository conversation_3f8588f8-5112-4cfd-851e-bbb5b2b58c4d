package com.spup.data.dao.appointment;

import com.spup.data.entity.appointment.AppAppointmentItemSuborder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * @Description  app_appointment_item_suborder Dao 
 * <AUTHOR>  
 * @Date 2023-12-14 
 */

@Repository
public interface AppAppointmentItemSuborderDao  extends JpaRepository<AppAppointmentItemSuborder, Long> , JpaSpecificationExecutor<AppAppointmentItemSuborder> {
    List<AppAppointmentItemSuborder> findByBatchDate(String batchDate);
    List<AppAppointmentItemSuborder> findByOnwerUnionid(String onwerUnionid);
    List<AppAppointmentItemSuborder> findByOrderNo(String orderNo);

    Optional<AppAppointmentItemSuborder> findBySuborderNo(String suborderNo);

    long countBySuborderStatus(Short suborderStatus);
    long countBySuborderStatusAndBatchDateBetween(Short suborderStatus, String start,String end);

    List<AppAppointmentItemSuborder>  findByBatchDateBetween(String startDate,String endDate);
}
