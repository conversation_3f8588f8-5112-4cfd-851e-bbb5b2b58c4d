package com.spup.data.dao.activity;

import com.spup.data.entity.activity.ActivitySubmitCustomer;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Optimized DAO for ActivitySubmitCustomer with essential joined queries
 */
@Repository
public interface ActivitySubmitCustomerDetailDao extends JpaRepository<ActivitySubmitCustomer, Long> {

       /**
        * Find ActivitySubmitCustomer by ID with joined Activity and ActivityRound data
        */
       @Query("SELECT ac FROM ActivitySubmitCustomer ac WHERE ac.id = :id")
       Optional<ActivitySubmitCustomer> fetchActivityDetails(@Param("id") Long id);

       /**
        * Find ActivitySubmitCustomer by unionid with ordering
        */
       @Query("SELECT ac FROM ActivitySubmitCustomer ac " +
                     "WHERE ac.unionid = :unionid " +
                     "ORDER BY ac.createOn DESC")
       List<ActivitySubmitCustomer> fetchByUnionid(@Param("unionid") String unionid);

       /**
        * Find ActivitySubmitCustomer by actRoundId with ordering
        */
       @Query("SELECT ac FROM ActivitySubmitCustomer ac " +
                     "WHERE ac.actRoundId = :actRoundId " +
                     "ORDER BY ac.createOn DESC")
       List<ActivitySubmitCustomer> fetchByActRoundId(@Param("actRoundId") String actRoundId);

       /**
        * Find ActivitySubmitCustomer by actRoundId and unionid
        */
       List<ActivitySubmitCustomer> findByActRoundIdAndUnionidOrderByCreateOnDesc(String actRoundId, String unionid);

       /**
        * Find ActivitySubmitCustomer by status with ordering
        */
       List<ActivitySubmitCustomer> findByStatusOrderByCreateOnDesc(
                     ActivitySubmitCustomer.SubmitCustomerStatusEnum status);

       /**
        * Find ActivitySubmitCustomer by type with ordering
        */
       List<ActivitySubmitCustomer> findByTypeOrderByCreateOnDesc(ActivitySubmitCustomer.SubmitCustomerTypeEnum type);

       /**
        * Search ActivitySubmitCustomer by customer name
        */
       List<ActivitySubmitCustomer> findByUsernameContainingIgnoreCaseOrderByCreateOnDesc(String username);

       /**
        * Find all ActivitySubmitCustomer with pagination and ordering
        */
       Page<ActivitySubmitCustomer> findAllByOrderByCreateOnDesc(Pageable pageable);

       /**
        * Find ActivitySubmitCustomer by date range
        */
       List<ActivitySubmitCustomer> findByCreateOnBetweenOrderByCreateOnDesc(
                     java.time.LocalDateTime startDateTime,
                     java.time.LocalDateTime endDateTime);

       /**
        * Find ActivitySubmitCustomer by date range with pagination
        */
       Page<ActivitySubmitCustomer> findByCreateOnBetweenOrderByCreateOnDesc(
                     java.time.LocalDateTime startDateTime,
                     java.time.LocalDateTime endDateTime,
                     Pageable pageable);

}
