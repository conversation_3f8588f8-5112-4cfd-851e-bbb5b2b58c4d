package com.spup.data.dao.appointment;

import com.spup.data.entity.appointment.AppAppointmentItemOrder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * @Description  app_appointment_item_order Dao 
 * <AUTHOR>  
 * @Date 2023-12-14 
 */

@Repository
public interface AppAppointmentItemOrderDao  extends JpaRepository<AppAppointmentItemOrder, Long> , JpaSpecificationExecutor<AppAppointmentItemOrder> {
    List<AppAppointmentItemOrder> findByOwnerUnionid(String ownerUnionid);
    List<AppAppointmentItemOrder> findByBatchDateBetween(String start, String end);
    Optional<AppAppointmentItemOrder> findByOrderNo(String orderNo);

}
