package com.spup.data.dao.authority;

import com.spup.data.entity.authority.AppCustomer;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * @Description  app_customer Dao 
 * <AUTHOR>  
 * @Date 2023-12-14 
 */

@Repository
public interface AppCustomerDao  extends JpaRepository<AppCustomer, Long> , JpaSpecificationExecutor<AppCustomer> {
    Optional<AppCustomer> getByUnionid(String unionid);
}
