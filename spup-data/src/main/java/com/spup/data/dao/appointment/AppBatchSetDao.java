package com.spup.data.dao.appointment;

import com.spup.data.entity.appointment.AppBatchSet;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * @Description  app_batch_set Dao 
 * <AUTHOR>  
 * @Date 2023-12-14 
 */

@Repository
public interface AppBatchSetDao  extends JpaRepository<AppBatchSet, Long> , JpaSpecificationExecutor<AppBatchSet> {
    Optional<AppBatchSet> getByBatchCategoryAndStatus(Byte batchCategory, Byte status);
}
