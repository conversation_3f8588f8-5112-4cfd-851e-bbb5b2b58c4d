package com.spup.data.dao;

import com.spup.data.entity.Volunteer;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface VolunteerDao extends JpaRepository<Volunteer, Long>, JpaSpecificationExecutor<Volunteer> {
    List<Volunteer> findByCategory(Volunteer.VolunteerCategoryEnum category);
    Optional<Volunteer> getByVolunteerId(String volunteerId);
}
