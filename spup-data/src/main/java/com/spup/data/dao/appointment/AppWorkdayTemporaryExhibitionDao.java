package com.spup.data.dao.appointment;

import com.spup.data.entity.appointment.AppWorkdayTemporaryExhibition;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

/**
 * @Description  app_workday_temporary_exhibition Dao 
 * <AUTHOR>  
 * @Date 2023-12-14 
 */

@Repository
public interface AppWorkdayTemporaryExhibitionDao  extends JpaRepository<AppWorkdayTemporaryExhibition, Long> , JpaSpecificationExecutor<AppWorkdayTemporaryExhibition> {


}
