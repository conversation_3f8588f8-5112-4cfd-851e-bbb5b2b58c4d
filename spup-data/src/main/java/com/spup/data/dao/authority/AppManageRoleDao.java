package com.spup.data.dao.authority;

import com.spup.data.entity.authority.AppManageRole;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

/**
 * @Description  app_manage_role Dao 
 * <AUTHOR>  
 * @Date 2023-12-14 
 */

@Repository
public interface AppManageRoleDao  extends JpaRepository<AppManageRole, Long> , JpaSpecificationExecutor<AppManageRole> {


}
