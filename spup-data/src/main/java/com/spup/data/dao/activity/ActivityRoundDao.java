package com.spup.data.dao.activity;

import com.spup.data.entity.activity.ActivityRound;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;


@Repository
public interface ActivityRoundDao extends JpaRepository<ActivityRound, Long> , JpaSpecificationExecutor<ActivityRound> {
    Optional<ActivityRound> findByActRoundId(String actRoundId);
    List<ActivityRound> findByActivityId(String activityId);
    @Transactional
    @Modifying
    @Query("update ActivityRound set actRoundSubmitNumber = actRoundSubmitNumber+ ?1 where id = ?2 and actRoundSubmitNumber+ ?3 <= actRoundMaxSubmitNum and actRoundSubmitNumber+ ?4 >=0 ")
    int updateSubmitNumber(int addSubmitNumber, Long id, int addSubmitNumberCp,int addSubmitNumberCp2);

    @Transactional
    @Modifying
    @Query("update ActivityRound set status = ?1 where id = ?2")
    int updateStatus(ActivityRound.ActRoundStatusEnum actRoundStatusEnum, Long id);

}
