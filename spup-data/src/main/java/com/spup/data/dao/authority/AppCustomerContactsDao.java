package com.spup.data.dao.authority;

import com.spup.data.entity.authority.AppCustomerContacts;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Description  app_customer_contacts Dao 
 * <AUTHOR>  
 * @Date 2023-12-14 
 */

@Repository
public interface AppCustomerContactsDao  extends JpaRepository<AppCustomerContacts, Long> , JpaSpecificationExecutor<AppCustomerContacts> {
    List<AppCustomerContacts> findByOwerUnionid(String owerUnionid);
}

