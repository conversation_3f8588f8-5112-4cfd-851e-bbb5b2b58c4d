package com.spup.data.dao.appointment;

import com.spup.data.entity.appointment.AppAppointmentPersonalOffline;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

/**
 * @Description  app_appointment_personal_offline Dao 
 * <AUTHOR>  
 * @Date 2023-12-19 
 */

@Repository
public interface AppAppointmentPersonalOfflineDao  extends JpaRepository<AppAppointmentPersonalOffline, Long> , JpaSpecificationExecutor<AppAppointmentPersonalOffline> {


}
