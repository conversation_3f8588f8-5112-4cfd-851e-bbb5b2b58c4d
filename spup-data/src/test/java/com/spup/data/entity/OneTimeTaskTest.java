package com.spup.data.entity;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.time.Instant;
import java.util.UUID;

import com.spup.data.entity.OneTimeTask.TaskStatusEnum;
import com.spup.data.entity.OneTimeTask.TaskTypeEnum;

public class OneTimeTaskTest {

    @Test
    public void testOneTimeTaskProperties() {
        // Arrange
        String id = UUID.randomUUID().toString();
        TaskTypeEnum taskType = TaskTypeEnum.CREATE_TEAM_BATCH;
        String taskClass = "com.example.TaskExecutor";
        String taskData = "{\"batchId\":1,\"batchName\":\"Test Batch\"}"; // JSON string

        Instant executeTime = Instant.now().plusSeconds(3600); // 1 hour from now
        Instant createdAt = Instant.now();
        TaskStatusEnum status = TaskStatusEnum.PENDING;

        // Act
        OneTimeTask task = new OneTimeTask();
        task.setId(id);
        task.setTaskType(taskType);
        task.setTaskClass(taskClass);
        task.setTaskData(taskData);
        task.setExecuteTime(executeTime);
        task.setCreatedAt(createdAt);
        task.setStatus(status);

        // Assert
        assertEquals(id, task.getId());
        assertEquals(taskType, task.getTaskType());
        assertEquals(taskClass, task.getTaskClass());
        assertEquals(taskData, task.getTaskData());
        assertEquals(executeTime, task.getExecuteTime());
        assertEquals(createdAt, task.getCreatedAt());
        assertEquals(status, task.getStatus());
    }

    @Test
    public void testTaskStatusEnumValues() {
        // Verify all expected enum values exist
        assertEquals(5, TaskStatusEnum.values().length);
        assertNotNull(TaskStatusEnum.PENDING);
        assertNotNull(TaskStatusEnum.EXECUTING);
        assertNotNull(TaskStatusEnum.COMPLETED);
        assertNotNull(TaskStatusEnum.FAILED);
        assertNotNull(TaskStatusEnum.CANCELLED);
    }

    @Test
    public void testTaskTypeEnumValues() {
        // Verify all expected enum values exist
        assertEquals(13, TaskTypeEnum.values().length);
        assertNotNull(TaskTypeEnum.CREATE_TEAM_BATCH);
        assertNotNull(TaskTypeEnum.CREATE_PERSONAL_BATCH);
        assertNotNull(TaskTypeEnum.CREATE_ITEM_BATCH);
        assertNotNull(TaskTypeEnum.CLOSE_TEAM_BATCH);
        assertNotNull(TaskTypeEnum.CLOSE_ITEM_BATCH);
        assertNotNull(TaskTypeEnum.CLOSE_PERSONAL_BATCH);
        assertNotNull(TaskTypeEnum.OPEN_TEAM_BATCH);
        assertNotNull(TaskTypeEnum.OPEN_ITEM_BATCH);
        assertNotNull(TaskTypeEnum.OPEN_PERSONAL_BATCH);
        assertNotNull(TaskTypeEnum.UPDATE_ITEM_BATCH);
        assertNotNull(TaskTypeEnum.UPDATE_PERSONAL_BATCH);
        assertNotNull(TaskTypeEnum.UPDATE_TEAM_BATCH);
        assertNotNull(TaskTypeEnum.OVERWRITE_ITEM_BATCH);
    }

    @Test
    public void testEqualsAndHashCode() {
        // Arrange
        String id = UUID.randomUUID().toString();

        OneTimeTask task1 = new OneTimeTask();
        task1.setId(id);
        task1.setTaskType(TaskTypeEnum.CREATE_TEAM_BATCH);
        task1.setStatus(TaskStatusEnum.PENDING);

        OneTimeTask task2 = new OneTimeTask();
        task2.setId(id);  // Same ID
        task2.setTaskType(TaskTypeEnum.CREATE_PERSONAL_BATCH); // Different type
        task2.setStatus(TaskStatusEnum.EXECUTING); // Different status

        OneTimeTask task3 = new OneTimeTask();
        task3.setId(UUID.randomUUID().toString()); // Different ID
        task3.setTaskType(TaskTypeEnum.CREATE_TEAM_BATCH);
        task3.setStatus(TaskStatusEnum.PENDING);

        // Assert
        // Lombok's @Data provides equals/hashCode implementation based on all fields
        assertEquals(task1, task1); // Same object
        assertEquals(task1.hashCode(), task1.hashCode());

        // Different objects with same ID but different other fields
        // With Lombok's @Data, they should be different
        assertNotEquals(task1, task2);
        assertNotEquals(task1.hashCode(), task2.hashCode());

        // Different objects with different IDs
        assertNotEquals(task1, task3);
        assertNotEquals(task1.hashCode(), task3.hashCode());

        // Not equal to null or other types
        assertNotEquals(task1, null);
        assertNotEquals(task1, "not a task");
    }

    @Test
    public void testToString() {
        // Arrange
        OneTimeTask task = new OneTimeTask();
        task.setId("test-id");
        task.setTaskType(TaskTypeEnum.CREATE_TEAM_BATCH);
        task.setStatus(TaskStatusEnum.PENDING);

        // Act
        String toString = task.toString();

        // Assert
        // Lombok's @Data provides a toString implementation
        assertNotNull(toString);
        assertTrue(toString.contains("test-id"));
        assertTrue(toString.contains("CREATE_TEAM_BATCH"));
        assertTrue(toString.contains("PENDING"));
    }
}
