{
  // === Java Language Server Configuration ===
  "java.server.launchMode": "Standard",
  "java.jdt.ls.vmargs": "-XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m",
  "java.eclipse.downloadSources": true,
  "java.maven.downloadSources": true,
  "java.referencesCodeLens.enabled": true,
  "java.signatureHelp.enabled": true,
  "java.contentProvider.preferred": "fernflower",

  // === Build and Compilation Settings ===
  "java.compile.nullAnalysis.mode": "automatic",
  "java.configuration.updateBuildConfiguration": "automatic",
  "java.autobuild.enabled": true,
  "java.maxConcurrentBuilds": 1,
  "java.import.maven.enabled": true,
  "java.import.gradle.enabled": false,

  // === Multi-module Maven Project Settings ===
  "java.maven.updateSnapshots": true,

  // === Source Path Configuration (Updated for your modules) ===
  "java.project.sourcePaths": [
    "spup-user-web/src/main/java",
    "spup-admin-web/src/main/java",
    "spup-core/src/main/java",
    "spup-data/src/main/java",
    "spup-common/src/main/java",
    "spup-activity/src/main/java"
  ],

  // === Output and Dependencies ===
  "java.project.outputPath": "target/classes",
  "java.project.referencedLibraries": [
    "lib/**/*.jar",
    "**/target/classes"
  ],

  // === Error Reporting and Diagnostics ===
  "java.errors.incompleteClasspath.severity": "warning",
  "java.dependency.syncWithFolderExplorer": true,
  "java.dependency.packagePresentation": "hierarchical",
  "java.dependency.showMembers": true,

  // === Import Organization ===
  "java.sources.organizeImports.starThreshold": 99,
  "java.sources.organizeImports.staticStarThreshold": 99,
  "java.completion.favoriteStaticMembers": [
    "org.junit.Assert.*",
    "org.junit.Assume.*",
    "org.junit.jupiter.api.Assertions.*",
    "org.junit.jupiter.api.Assumptions.*",
    "org.junit.jupiter.api.DynamicContainer.*",
    "org.junit.jupiter.api.DynamicTest.*",
    "org.mockito.Mockito.*",
    "org.mockito.ArgumentMatchers.*",
    "org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*",
    "org.springframework.test.web.servlet.result.MockMvcResultMatchers.*"
  ],

  // === Debug Configuration ===
  "java.debug.settings.onBuildFailureProceed": true,
  "java.debug.settings.console": "integratedTerminal",
  "java.debug.settings.showQualifiedNames": true,
  "java.debug.settings.showStaticVariables": true,
  "java.debug.settings.showHex": false,
  "java.debug.settings.maxStringLength": 0,
  "java.debug.settings.exceptionBreakpoint.skipClasses": [
    "org.springframework.boot.devtools.restart.SilentExitExceptionHandler$SilentExitException"
  ],

  // === Performance and Memory Settings ===
  "java.codeGeneration.hashCodeEquals.useJava7Objects": true,
  "java.codeGeneration.useBlocks": true,
  "java.saveActions.organizeImports": true,

  // === Project Metadata Settings ===
  "java.import.generatesMetadataFilesAtProjectRoot": false,

  // === File Exclusions ===
  "files.exclude": {
    "**/.classpath": true,
    "**/.project": true,
    "**/.settings": true,
    "**/.factorypath": true
  }
}