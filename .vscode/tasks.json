{"version": "2.0.0", "tasks": [{"type": "java (build)", "paths": ["${workspace}"], "isFullBuild": true, "group": "build", "problemMatcher": [], "label": "java (build): Build Workspace", "detail": "$(tools) Build all the Java projects in workspace."}, {"label": "🔥 Compile and Sync to Remote", "type": "shell", "command": "./scripts/auto-sync-remote.sh", "args": ["sync-once"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "🔄 Start Auto-Sync Watcher", "type": "shell", "command": "./scripts/auto-sync-remote.sh", "args": ["start"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "isBackground": true}, {"label": "⚡ Quick Build All", "type": "shell", "command": "./scripts/dev-build.sh", "args": ["--quick", "--skip-tests"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$maven-compile-problem-matcher"]}, {"label": "🔧 Build Core Module", "type": "shell", "command": "./scripts/dev-module.sh", "args": ["spup-core", "install"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "🌐 Build Admin Module", "type": "shell", "command": "./scripts/dev-module.sh", "args": ["spup-admin-web", "compile"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "👤 Build User Module", "type": "shell", "command": "./scripts/dev-module.sh", "args": ["spup-user-web", "compile"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "📦 Full Install", "type": "shell", "command": "mvn", "args": ["clean", "install", "-DskipTests"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}]}