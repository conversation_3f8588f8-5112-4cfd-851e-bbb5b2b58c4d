# Spring Security Configuration Guide

## Problem
When Spring Security is included in `spup-core`, all modules that depend on `spup-core` automatically get Spring Security auto-configuration enabled, even if they don't need it.

## Solution Overview
We've implemented a multi-layered approach to control Spring Security activation:

### 1. Exclude Auto-Configuration at Application Level
```java
@SpringBootApplication(exclude = {
    org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration.class,
    org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration.class
})
```

### 2. Property-Based Configuration
- **Admin Module**: `spup.security.enabled=true` (enables security)
- **User Module**: `spup.security.enabled=false` (disables security)

### 3. Conditional Security Configuration
```java
@Configuration
@EnableWebSecurity
@ConditionalOnProperty(name = "spup.security.enabled", havingValue = "true")
public class SecurityConfig {
    // Security configuration only when explicitly enabled
}
```

## Module Configuration

### spup-admin-web
- **Has Spring Security dependency**: ✅
- **Security enabled**: ✅ (`spup.security.enabled=true`)
- **Custom SecurityConfig**: ✅ (with token interceptor integration)

### spup-user-web  
- **Has Spring Security dependency**: ❌
- **Security disabled**: ✅ (excluded at application level + property)
- **Uses token interceptor**: ✅ (for authentication without Spring Security)

### spup-core
- **Has Spring Security dependency**: ❌ (commented out)
- **Provides base interceptor**: ✅ (BaseTokenInterceptor)

## Benefits
1. **Selective Security**: Only modules that need Spring Security get it
2. **No Interference**: User module continues using custom token interceptor
3. **Flexible Configuration**: Easy to enable/disable via properties
4. **Clean Separation**: Core module doesn't force security on dependents

## Configuration Files

### Admin Module (application.yml)
```yaml
spup:
  security:
    enabled: true
```

### User Module (application.yml)
```yaml
spup:
  security:
    enabled: false
spring:
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration
      - org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration
```

## Testing
1. **Admin Module**: Should have Spring Security active with custom configuration
2. **User Module**: Should use only the custom token interceptor without Spring Security
3. **Both**: Should maintain their existing authentication mechanisms
