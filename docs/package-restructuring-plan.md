# Package Restructuring Plan

## 🎯 Objective
Eliminate package name conflicts between modules by giving each module its own unique package namespace.

## 📊 Current Package Conflicts

### Critical Conflicts (Same package in multiple modules):
- `com.spup.config` → spup-admin-web, spup-user-web
- `com.spup.controller` → spup-admin-web, spup-user-web  
- `com.spup.dto` → spup-admin-web, spup-user-web
- `com.spup.enums` → spup-admin-web, spup-user-web
- `com.spup.counter` → spup-admin-web, spup-user-web
- `com.spup.javaConfig` → spup-admin-web, spup-user-web
- `com.spup.interceptor` → spup-user-web (conflicts with core)

## 🎯 Target Package Structure

### spup-admin-web
```
com.spup.admin.config.*
com.spup.admin.controller.*
com.spup.admin.dto.*
com.spup.admin.enums.*
com.spup.admin.service.*
com.spup.admin.interceptor.*
com.spup.admin.javaConfig.*
com.spup.admin.counter.*
com.spup.admin.poi.*
com.spup.admin.task.*
com.spup.admin.utils.*
com.spup.admin.mp.*
```

### spup-user-web
```
com.spup.user.config.*
com.spup.user.controller.*
com.spup.user.dto.*
com.spup.user.enums.*
com.spup.user.service.*
com.spup.user.interceptor.*
com.spup.user.javaConfig.*
com.spup.user.counter.*
com.spup.user.init.*
com.spup.user.mpapi.*
```

### spup-core (Already correct)
```
com.spup.core.config.*
com.spup.core.controller.*
com.spup.core.dto.*
com.spup.core.service.*
com.spup.core.interceptor.*
com.spup.core.util.*
com.spup.core.exception.*
```

### spup-data (Rename from com.spup.db)
```
com.spup.data.dao.*
com.spup.data.entity.*
com.spup.data.repository.*
```

### spup-common (Already correct)
```
com.spup.common.* (rename from com.huangdou.commons)
```

### spup-activity (Already correct)
```
com.spup.activity.* (rename from com.huangdou.activity)
```

## 🔄 Migration Steps

### Phase 1: spup-admin-web
1. Rename packages from `com.spup.*` to `com.spup.admin.*`
2. Update all import statements
3. Update Spring component scanning

### Phase 2: spup-user-web  
1. Rename packages from `com.spup.*` to `com.spup.user.*`
2. Update all import statements
3. Update Spring component scanning

### Phase 3: spup-data
1. Rename packages from `com.spup.db.*` to `com.spup.data.*`
2. Update all references across modules

### Phase 4: spup-common & spup-activity
1. Rename from `com.huangdou.*` to `com.spup.*`
2. Update all references

### Phase 5: Update Component Scanning
1. Update @ComponentScan annotations
2. Update @EnableJpaRepositories annotations  
3. Update @EntityScan annotations

## 🧪 Testing Strategy
1. Compile each module after changes
2. Run unit tests
3. Integration testing
4. Verify no package conflicts remain
