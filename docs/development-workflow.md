# SPUP Development Workflow Guide

This guide provides efficient development workflows to avoid the common multi-module Maven dependency issues.

## 🚀 Quick Start

### Option 1: Use Development Scripts (Recommended)

```bash
# Quick compile all modules (fastest for development)
./scripts/dev-build.sh --quick --skip-tests

# Build specific module with dependencies
./scripts/dev-module.sh spup-core install
./scripts/dev-module.sh spup-admin-web compile
./scripts/dev-module.sh spup-user-web compile

# Full build when needed
./scripts/dev-build.sh --clean
```

### Option 2: Use Maven Profiles

```bash
# Fast development build (skips tests, docs, etc.)
mvn compile -Pdev

# Quick compile only (no install)
mvn compile -Pquick

# Test only
mvn test -Ptest-only
```

### Option 3: Use Maven Reactor Commands

```bash
# Build from root - Maven handles dependencies automatically
mvn compile                           # Compile all modules
mvn compile -pl spup-admin-web -am    # Build admin-web and dependencies
mvn compile -pl spup-user-web -am     # Build user-web and dependencies
mvn install -DskipTests               # Install all without tests
```

## 🔧 Development Scenarios

### Scenario 1: Working on Core Module
When you modify `spup-core`, other modules need the updated version:

```bash
# Option A: Quick install core only
./scripts/dev-module.sh spup-core install

# Option B: Build from root (recommended)
mvn install -pl spup-core -DskipTests

# Then compile dependent modules
./scripts/dev-module.sh spup-admin-web compile
./scripts/dev-module.sh spup-user-web compile
```

### Scenario 2: Working on Admin/User Modules
When working on web modules, you usually only need compilation:

```bash
# Quick compile without install
./scripts/dev-module.sh spup-admin-web compile
# or
mvn compile -pl spup-admin-web -am
```

### Scenario 3: Full Development Build
When you need everything up-to-date:

```bash
# Fast full build
./scripts/dev-build.sh --skip-tests

# Or with Maven profiles
mvn install -Pdev
```

## 🎯 IDE Integration

### VS Code Tasks
Use Ctrl+Shift+P → "Tasks: Run Task" and select:
- ⚡ Quick Build All
- 🔧 Build Core Module  
- 🌐 Build Admin Module
- 👤 Build User Module
- 📦 Full Install

### IntelliJ IDEA
1. Import as Maven project from root
2. Enable "Auto-import" for Maven
3. Use Maven tool window for module-specific builds
4. Create run configurations for common tasks

## 📋 Best Practices

### 1. Always Build from Root
```bash
# ✅ Good - Maven handles dependency order
cd /path/to/spup-root
mvn compile

# ❌ Avoid - Manual dependency management
cd spup-admin-web
mvn compile  # May fail if spup-core changed
```

### 2. Use Appropriate Build Commands

| Scenario | Command | Speed | Use Case |
|----------|---------|-------|----------|
| Quick check | `mvn compile -Pquick` | ⚡⚡⚡ | Syntax check |
| Development | `mvn compile -Pdev` | ⚡⚡ | Active development |
| Testing | `mvn test -pl module` | ⚡ | Unit testing |
| Integration | `mvn install -DskipTests` | ⚡ | Module integration |
| Full build | `mvn clean install` | 🐌 | Before commit/deploy |

### 3. Module Dependency Order
Always build in dependency order:
1. `spup-common` (no dependencies)
2. `spup-data` (depends on common)
3. `spup-core` (depends on common, data)
4. `spup-activity` (depends on common, data, core)
5. `spup-admin-web` (depends on common, data, core)
6. `spup-user-web` (depends on common, data, core)

### 4. Incremental Development
```bash
# 1. Make changes to spup-core
# 2. Quick install core
./scripts/dev-module.sh spup-core install

# 3. Compile dependent modules
./scripts/dev-module.sh spup-admin-web compile
./scripts/dev-module.sh spup-user-web compile

# 4. Test your changes
mvn test -pl spup-admin-web
```

## 🚨 Troubleshooting

### Problem: "Package does not exist" errors
**Solution**: Build dependencies first
```bash
mvn install -pl spup-core -DskipTests
```

### Problem: "Class not found" at runtime
**Solution**: Full install required
```bash
mvn install -DskipTests
```

### Problem: Slow builds
**Solution**: Use development profiles
```bash
mvn compile -Pdev  # Skips tests, docs, static analysis
```

### Problem: IDE not recognizing changes
**Solution**: Refresh Maven projects
- IntelliJ: Maven tool window → Reload
- VS Code: Reload window or restart Java Language Server

## 🔄 Continuous Development Setup

For active development, consider this workflow:

1. **Terminal 1**: File watcher for auto-build
```bash
# Install fswatch (macOS) or inotify-tools (Linux)
brew install fswatch

# Auto-build on file changes
fswatch -o src/ | xargs -n1 -I{} ./scripts/dev-build.sh --quick
```

2. **Terminal 2**: Development server
```bash
# Run your application
mvn spring-boot:run -pl spup-admin-web
```

3. **IDE**: Code editing with immediate feedback

This setup provides near real-time feedback during development without manual build steps.
