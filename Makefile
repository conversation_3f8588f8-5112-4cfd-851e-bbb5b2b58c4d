# SPUP Development Makefile
# Provides simple commands for common development tasks

.PHONY: help build quick install clean test core admin user activity all

# Default target
help:
	@echo "SPUP Development Commands:"
	@echo ""
	@echo "Quick Development:"
	@echo "  make quick     - Quick compile all modules (fastest)"
	@echo "  make build     - Build all modules with install"
	@echo "  make clean     - Clean all modules"
	@echo ""
	@echo "Module-specific:"
	@echo "  make core      - Build spup-core module"
	@echo "  make admin     - Build spup-admin-web module"
	@echo "  make user      - Build spup-user-web module"
	@echo "  make activity  - Build spup-activity module"
	@echo ""
	@echo "Testing:"
	@echo "  make test      - Run all tests"
	@echo "  make test-core - Test spup-core module"
	@echo "  make test-admin - Test spup-admin-web module"
	@echo "  make test-user - Test spup-user-web module"
	@echo ""
	@echo "Utilities:"
	@echo "  make deps      - Show dependency tree"
	@echo "  make status    - Show build status"

# Quick development build
quick:
	@echo "🚀 Quick build (compile only, no tests)..."
	@mvn compile -Pquick -q

# Full build with install
build:
	@echo "📦 Building all modules..."
	@mvn install -Pdev -q

# Install all modules
install:
	@echo "📦 Installing all modules..."
	@mvn install -DskipTests -q

# Clean all modules
clean:
	@echo "🧹 Cleaning all modules..."
	@mvn clean -q

# Test all modules
test:
	@echo "🧪 Running all tests..."
	@mvn test -q

# Core module
core:
	@echo "🔧 Building spup-core..."
	@mvn install -pl spup-core -DskipTests -q

# Admin module
admin:
	@echo "🌐 Building spup-admin-web..."
	@mvn compile -pl spup-admin-web -am -q

# User module  
user:
	@echo "👤 Building spup-user-web..."
	@mvn compile -pl spup-user-web -am -q

# Activity module
activity:
	@echo "🎯 Building spup-activity..."
	@mvn compile -pl spup-activity -am -q

# Test specific modules
test-core:
	@echo "🧪 Testing spup-core..."
	@mvn test -pl spup-core -q

test-admin:
	@echo "🧪 Testing spup-admin-web..."
	@mvn test -pl spup-admin-web -q

test-user:
	@echo "🧪 Testing spup-user-web..."
	@mvn test -pl spup-user-web -q

# Utility commands
deps:
	@echo "📊 Dependency tree:"
	@mvn dependency:tree -q

status:
	@echo "📋 Build status:"
	@echo "Checking compilation..."
	@mvn compile -q && echo "✅ All modules compile successfully" || echo "❌ Compilation errors found"

# Development workflow shortcuts
dev-core:
	@echo "🔄 Core development workflow..."
	@make core && make admin && make user

dev-admin:
	@echo "🔄 Admin development workflow..."
	@make admin

dev-user:
	@echo "🔄 User development workflow..."
	@make user

# Full development cycle
all: clean build test
	@echo "✅ Full build cycle completed"
