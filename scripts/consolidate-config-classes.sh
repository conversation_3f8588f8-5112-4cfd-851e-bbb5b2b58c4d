#!/bin/bash

# Script to consolidate duplicate configuration classes

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Get the script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

cd "$PROJECT_ROOT"

print_status "Starting Configuration Class consolidation..."

# 1. Create module-specific OpenAPI configurations that extend the base
print_status "Creating module-specific OpenAPI configurations..."

# User OpenAPI Config
cat > spup-user-web/src/main/java/com/spup/user/javaConfig/OpenApiConfig.java << 'EOF'
package com.spup.user.javaConfig;

import com.spup.core.config.BaseOpenApiConfig;
import org.springframework.context.annotation.Configuration;

/**
 * User Module OpenAPI Configuration
 * Extends BaseOpenApiConfig with user-specific settings
 */
@Configuration
public class OpenApiConfig extends BaseOpenApiConfig {
    
    @Override
    protected String getApiTitle() {
        return "上海博物馆东馆预约系统";
    }
    
    @Override
    protected String getApiDescription() {
        return "上海博物馆东馆预约系统 - 用户端API";
    }
}
EOF

# Admin OpenAPI Config
cat > spup-admin-web/src/main/java/com/spup/admin/javaConfig/OpenApiConfig.java << 'EOF'
package com.spup.admin.javaConfig;

import com.spup.core.config.BaseOpenApiConfig;
import org.springframework.context.annotation.Configuration;

/**
 * Admin Module OpenAPI Configuration
 * Extends BaseOpenApiConfig with admin-specific settings
 */
@Configuration
public class OpenApiConfig extends BaseOpenApiConfig {
    
    @Override
    protected String getApiTitle() {
        return "浦东规划馆后台管理API";
    }
    
    @Override
    protected String getApiDescription() {
        return "接口文档详情信息 - 基于SpringDoc OpenAPI 3";
    }
}
EOF

# 2. Create module-specific Exception handlers that extend the base
print_status "Creating module-specific Exception handlers..."

# User Exception Handler
cat > spup-user-web/src/main/java/com/spup/user/javaConfig/ExceptionControllerAdvice.java << 'EOF'
package com.spup.user.javaConfig;

import com.spup.common.CommonResult;
import com.spup.core.config.BaseExceptionControllerAdvice;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * User Module Exception Controller Advice
 * Extends BaseExceptionControllerAdvice with user-specific exception handling
 */
@RestControllerAdvice(basePackages = "com.spup.user")
public class ExceptionControllerAdvice extends BaseExceptionControllerAdvice {
    
    @Override
    protected String getModuleName() {
        return "User";
    }
    
    @Override
    protected CommonResult<?> handleModuleSpecificException(Exception e) {
        // Add user-specific exception types here
        if (e instanceof IllegalStateException) {
            return CommonResult.failed("User state error: " + e.getMessage());
        }
        
        return null; // Let base class handle it
    }
}
EOF

# Admin Exception Handler
cat > spup-admin-web/src/main/java/com/spup/admin/javaConfig/ExceptionControllerAdvice.java << 'EOF'
package com.spup.admin.javaConfig;

import com.spup.common.CommonResult;
import com.spup.core.config.BaseExceptionControllerAdvice;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * Admin Module Exception Controller Advice
 * Extends BaseExceptionControllerAdvice with admin-specific exception handling
 */
@RestControllerAdvice(basePackages = "com.spup.admin")
public class ExceptionControllerAdvice extends BaseExceptionControllerAdvice {
    
    @Override
    protected String getModuleName() {
        return "Admin";
    }
    
    @Override
    protected CommonResult<?> handleModuleSpecificException(Exception e) {
        // Add admin-specific exception types here
        if (e instanceof SecurityException) {
            return CommonResult.failed("Admin security error: " + e.getMessage());
        }
        
        return null; // Let base class handle it
    }
}
EOF

print_success "Created module-specific configuration classes"

# 3. Test compilation
print_status "Testing compilation with new configuration classes..."
if mvn compile -q; then
    print_success "✅ All configuration classes compile successfully!"
    
    print_warning "Ready to remove duplicate CorsConfig files. Continue? (y/n)"
    read -r response
    if [ "$response" = "y" ]; then
        print_status "Removing duplicate CorsConfig files..."
        
        # Remove duplicate CorsConfig files
        if [ -f "spup-user-web/src/main/java/com/spup/user/javaConfig/CorsConfig.java" ]; then
            rm "spup-user-web/src/main/java/com/spup/user/javaConfig/CorsConfig.java"
            print_success "Removed user CorsConfig"
        fi
        
        if [ -f "spup-admin-web/src/main/java/com/spup/admin/javaConfig/CorsConfig.java" ]; then
            rm "spup-admin-web/src/main/java/com/spup/admin/javaConfig/CorsConfig.java"
            print_success "Removed admin CorsConfig"
        fi
        
        print_status "Final compilation test..."
        if mvn compile -q; then
            print_success "✅ Configuration class consolidation completed successfully!"
            print_status "Summary:"
            print_status "- Created unified CorsConfig in spup-core"
            print_status "- Created BaseOpenApiConfig with module-specific extensions"
            print_status "- Created BaseExceptionControllerAdvice with module-specific extensions"
            print_status "- Removed 2 duplicate CorsConfig files"
            print_status "- Enhanced OpenAPI and Exception handling with inheritance"
            print_status "- All modules compile successfully"
        else
            print_warning "❌ Compilation failed after removing duplicates"
            exit 1
        fi
    else
        print_status "Skipping file removal. Configuration updates completed."
    fi
else
    print_warning "❌ Compilation failed with new configuration classes"
    exit 1
fi

print_success "Configuration class consolidation script completed!"
