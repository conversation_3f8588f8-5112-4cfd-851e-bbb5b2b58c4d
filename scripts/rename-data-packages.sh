#!/bin/bash

# Script to rename packages in spup-data from com.spup.db.* to com.spup.data.*

set -e

echo "🔄 Starting package renaming for spup-data..."

# Define the module path
MODULE_PATH="./spup-data/src/main/java"

# Create new directory structure
echo "📁 Creating new directory structure..."
mkdir -p "$MODULE_PATH/com/spup/data"

# Move all db packages to data
echo "📦 Moving packages..."

# Move dao packages
if [ -d "$MODULE_PATH/com/spup/db/dao" ]; then
    echo "Moving dao packages..."
    mv "$MODULE_PATH/com/spup/db/dao" "$MODULE_PATH/com/spup/data/"
fi

# Move entity packages
if [ -d "$MODULE_PATH/com/spup/db/entity" ]; then
    echo "Moving entity packages..."
    mv "$MODULE_PATH/com/spup/db/entity" "$MODULE_PATH/com/spup/data/"
fi

# Move repository package
if [ -d "$MODULE_PATH/com/spup/db/repository" ]; then
    echo "Moving repository package..."
    mv "$MODULE_PATH/com/spup/db/repository" "$MODULE_PATH/com/spup/data/"
fi

# Move any remaining files in db root
if [ -d "$MODULE_PATH/com/spup/db" ]; then
    find "$MODULE_PATH/com/spup/db" -maxdepth 1 -name "*.java" -exec mv {} "$MODULE_PATH/com/spup/data/" \;
fi

echo "✅ Directory structure updated!"

# Update package declarations in all Java files
echo "🔧 Updating package declarations..."

find "$MODULE_PATH/com/spup/data" -name "*.java" -type f -exec sed -i '' 's/^package com\.spup\.db\./package com.spup.data./g' {} \;

echo "✅ Package declarations updated!"

echo "🎉 Package renaming for spup-data completed!"
