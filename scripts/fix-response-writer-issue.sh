#!/bin/bash

# Fix getWriter() already called issue
# This script helps identify and fix response writing conflicts

echo "🔧 Response Writer Issue Fix Script"
echo "=================================="

echo ""
echo "📋 Common Causes of 'getWriter() already called' Error:"
echo "======================================================"
echo "1. ❌ Mixing @Controller + @ResponseBody with manual response writing"
echo "2. ❌ Using both response.getWriter() and response.getOutputStream()"
echo "3. ❌ Writing to response multiple times"
echo "4. ❌ Exception handler conflicts with normal response"
echo "5. ❌ Interceptor writing response before controller"

echo ""
echo "✅ Fixes Applied:"
echo "================="
echo "1. ✅ Changed QuestionnaireController from @Controller to @RestController"
echo "2. ✅ Removed redundant @ResponseBody annotations"
echo "3. ✅ Enhanced ExceptionControllerAdvice with original exception logging"
echo "4. ✅ Added response.isCommitted() checks in interceptors"
echo "5. ✅ Fixed LogAop2 to prevent JSON serialization issues"

echo ""
echo "🔍 Files Modified:"
echo "=================="
echo "✅ app-spup-admin/src/main/java/com/spup/controller/others/QuestionnaireController.java"
echo "   - Changed @Controller to @RestController"
echo "   - Removed @ResponseBody annotations"
echo "   - Removed unused HttpServletRequest/Response parameters"
echo ""
echo "✅ app-spup-admin/src/main/java/com/spup/javaConfig/ExceptionControllerAdvice.java"
echo "   - Added comprehensive exception logging"
echo "   - Added try-catch around exception handlers"
echo "   - Added safe error response creation"
echo ""
echo "✅ app-spup/src/main/java/com/spup/javaConfig/ExceptionControllerAdvice.java"
echo "   - Enhanced with original exception logging"
echo "   - Added response.isCommitted() checks"
echo ""
echo "✅ app-spup-admin/src/main/java/com/spup/interceptor/TokenInterceptor.java"
echo "   - Already has response.isCommitted() checks"

echo ""
echo "🧪 Testing Steps:"
echo "================="
echo "1. Restart both applications"
echo "2. Test endpoints that previously failed"
echo "3. Check logs for enhanced exception details"
echo "4. Look for '=== ORIGINAL EXCEPTION ===' markers"

echo ""
echo "📝 Test Commands:"
echo "================="
echo "# Test questionnaire endpoints"
echo "curl -X POST http://localhost:8888/questionnaire/listByPage \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -d '{\"pageNum\": 1, \"pageSize\": 10}'"
echo ""
echo "# Test file upload"
echo "curl -X POST http://localhost:8888/media/upload \\"
echo "  -F 'file=@test.txt'"

echo ""
echo "🔧 Additional Checks:"
echo "===================="

# Check for potential issues in controllers
echo "Checking for potential response writing conflicts..."

# Check for mixed @Controller and @ResponseBody usage
echo ""
echo "🔍 Checking for @Controller + @ResponseBody patterns:"
if find app-spup-admin/src -name "*.java" -exec grep -l "@Controller" {} \; | xargs grep -l "@ResponseBody" 2>/dev/null; then
    echo "⚠️  Found controllers mixing @Controller with @ResponseBody"
else
    echo "✅ No @Controller + @ResponseBody conflicts found"
fi

# Check for manual response writing in @RestController classes
echo ""
echo "🔍 Checking for manual response writing in @RestController:"
if find app-spup-admin/src -name "*.java" -exec grep -l "@RestController" {} \; | xargs grep -l "response\.getWriter\|response\.getOutputStream" 2>/dev/null; then
    echo "⚠️  Found @RestController classes with manual response writing"
    find app-spup-admin/src -name "*.java" -exec grep -l "@RestController" {} \; | xargs grep -l "response\.getWriter\|response\.getOutputStream" 2>/dev/null
else
    echo "✅ No manual response writing in @RestController found"
fi

# Check for duplicate method mappings
echo ""
echo "🔍 Checking for duplicate method mappings:"
if find app-spup-admin/src -name "*Controller.java" -exec grep -H "@.*Mapping" {} \; | cut -d: -f2 | sort | uniq -d | head -5; then
    echo "⚠️  Potential duplicate mappings found (check above)"
else
    echo "✅ No obvious duplicate mappings found"
fi

echo ""
echo "📊 Summary:"
echo "==========="
echo "✅ QuestionnaireController: Fixed @Controller/@ResponseBody conflict"
echo "✅ ExceptionControllerAdvice: Enhanced with original exception logging"
echo "✅ Response writing: Added safety checks"
echo "✅ Interceptors: Already have response.isCommitted() checks"

echo ""
echo "🎯 Expected Results:"
echo "==================="
echo "Before: Only saw 'getWriter() has already been called'"
echo "After: See original exception + context + handler details"
echo ""
echo "Example improved log:"
echo "=== ORIGINAL EXCEPTION: NullPointerException ==="
echo "Request URL: http://localhost:8888/questionnaire/listByPage"
echo "Request Method: POST"
echo "Exception message: Cannot invoke method on null object"
echo "[Full stack trace]"
echo ""
echo "If handler fails:"
echo "=== EXCEPTION IN EXCEPTION HANDLER ==="
echo "Original exception was: NullPointerException - Cannot invoke method on null object"
echo "Handler exception: IllegalStateException - getWriter() has already been called"

echo ""
echo "✨ Fix complete! Restart your applications and test the endpoints."
