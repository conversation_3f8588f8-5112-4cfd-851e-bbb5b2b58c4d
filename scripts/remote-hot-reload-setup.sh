#!/bin/bash

# Remote Hot Reload Setup for Debugging
# This script provides instructions for setting up hot reload with remote debugging

echo "🔥 Remote Hot Reload Setup Guide"
echo "================================"

echo ""
echo "📋 Overview:"
echo "============"
echo "Hot reload allows you to modify code and see changes immediately"
echo "without restarting the remote application during debugging."

echo ""
echo "🛠️ Step 1: Remote Server JVM Configuration"
echo "=========================================="

echo ""
echo "**Required JVM Arguments for Remote Server:**"
echo ""
echo "# Basic debug + hot swap configuration"
echo "java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:8000 \\"
echo "     -XX:+AllowEnhancedClassRedefinition \\"
echo "     -XX:+EnableDynamicAgentLoading \\"
echo "     -Dspring.devtools.restart.enabled=true \\"
echo "     -Dspring.devtools.livereload.enabled=false \\"
echo "     -jar app-spup-admin.jar"

echo ""
echo "**For Tomcat deployment:**"
echo ""
echo "export CATALINA_OPTS=\"-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:8000 \\"
echo "                      -XX:+AllowEnhancedClassRedefinition \\"
echo "                      -XX:+EnableDynamicAgentLoading \\"
echo "                      -Dspring.devtools.restart.enabled=true\""
echo "catalina.sh run"

echo ""
echo "**For Maven Spring Boot:**"
echo ""
echo "mvn spring-boot:run -Dspring-boot.run.jvmArguments=\"\\"
echo "  -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:8000 \\"
echo "  -XX:+AllowEnhancedClassRedefinition \\"
echo "  -XX:+EnableDynamicAgentLoading\""

echo ""
echo "🔧 Step 2: VS Code Configuration"
echo "==============================="

echo ""
echo "**Enhanced launch.json for hot reload:**"
echo ""
echo "{"
echo "  \"name\": \"🔥 Remote: Hot Reload Debug\","
echo "  \"type\": \"java\","
echo "  \"request\": \"attach\","
echo "  \"hostName\": \"supec.douwifi.cn\","
echo "  \"port\": \"8000\","
echo "  \"projectName\": \"spup-admin\","
echo "  \"hotCodeReplace\": \"auto\","
echo "  \"sourcePaths\": ["
echo "    \"/Users/<USER>/workspace/supup-app/app-spup-admin/src/main/java\","
echo "    \"/Users/<USER>/workspace/supup-app/common/src/main/java\""
echo "  ],"
echo "  \"vmArgs\": [\"-XX:+AllowEnhancedClassRedefinition\"]"
echo "}"

echo ""
echo "📁 Step 3: File Sync Configuration"
echo "================================="

echo ""
echo "**Option A: Manual Sync (Simple)**"
echo "1. Make code changes locally"
echo "2. Compile: mvn compile"
echo "3. Copy .class files to remote server"
echo "4. Hot swap happens automatically during debug"

echo ""
echo "**Option B: Automated Sync with rsync**"
echo "# Create sync script"
echo "rsync -avz --delete target/classes/ <EMAIL>:/path/to/remote/classes/"

echo ""
echo "**Option C: IDE Auto-sync (Recommended)**"
echo "Configure VS Code to auto-compile and sync on save"

echo ""
echo "🚀 Step 4: VS Code Auto-Compile Setup"
echo "===================================="

echo ""
echo "**Add to .vscode/settings.json:**"
echo "{"
echo "  \"java.compile.nullAnalysis.mode\": \"automatic\","
echo "  \"java.configuration.updateBuildConfiguration\": \"automatic\","
echo "  \"java.autobuild.enabled\": true,"
echo "  \"java.debug.settings.hotCodeReplace\": \"auto\","
echo "  \"java.debug.settings.enableHotCodeReplace\": true"
echo "}"

echo ""
echo "🔄 Step 5: Auto-Sync Script"
echo "=========================="

echo ""
echo "**Create auto-sync on file change:**"
echo ""
echo "# Install fswatch (macOS) or inotify-tools (Linux)"
echo "brew install fswatch  # macOS"
echo ""
echo "# Auto-sync script"
echo "fswatch -o app-spup-admin/src/main/java | while read f; do"
echo "  echo \"File changed, compiling and syncing...\""
echo "  mvn compile -q"
echo "  rsync -avz target/classes/ <EMAIL>:/remote/path/classes/"
echo "done"

echo ""
echo "⚡ Step 6: Testing Hot Reload"
echo "=========================="

echo ""
echo "**Test Process:**"
echo "1. Start remote server with hot swap JVM args"
echo "2. Connect VS Code debugger"
echo "3. Set breakpoint in AppAnalysisController"
echo "4. Make a request to trigger the breakpoint"
echo "5. Modify code while paused at breakpoint"
echo "6. Continue execution - changes should be applied"

echo ""
echo "**Test Commands:**"
echo "# Test the analysis controller"
echo "curl -X POST http://supec.douwifi.cn:8888/spup-admin/analysis/getTeamDataByPage \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -d '{\"pageNum\": 1, \"pageSize\": 10}'"

echo ""
echo "🎯 Limitations of Hot Reload"
echo "=========================="

echo ""
echo "✅ **What CAN be hot swapped:**"
echo "- Method body changes"
echo "- Adding/removing local variables"
echo "- Changing method logic"
echo "- Modifying annotations (limited)"

echo ""
echo "❌ **What CANNOT be hot swapped:**"
echo "- Adding/removing methods"
echo "- Adding/removing fields"
echo "- Changing method signatures"
echo "- Changing class hierarchy"
echo "- Adding/removing classes"

echo ""
echo "🔧 Alternative: Spring DevTools Remote"
echo "====================================="

echo ""
echo "**For more advanced hot reload, use Spring DevTools:**"
echo ""
echo "1. Add to remote application.properties:"
echo "spring.devtools.remote.secret=mysecret"
echo "spring.devtools.remote.restart.enabled=true"
echo ""
echo "2. Run remote client locally:"
echo "java -cp spring-boot-devtools.jar org.springframework.boot.devtools.RemoteSpringApplication \\"
echo "  http://supec.douwifi.cn:8888"

echo ""
echo "✨ Quick Setup for Your Case"
echo "=========================="

echo ""
echo "**For AppAnalysisController hot reload:**"
echo ""
echo "1. **Remote server start command:**"
echo "java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:8000 \\"
echo "     -XX:+AllowEnhancedClassRedefinition \\"
echo "     -jar app-spup-admin.jar"
echo ""
echo "2. **VS Code debug config:** Use 'Remote: Hot Reload Debug'"
echo ""
echo "3. **Test hot reload:**"
echo "   - Set breakpoint in getTeamDataByPage method"
echo "   - Trigger with curl command above"
echo "   - Modify method while paused"
echo "   - Continue - see changes applied"

echo ""
echo "⚠️ **Important Notes:**"
echo "====================="
echo "1. Hot reload works best with method body changes"
echo "2. Structural changes require restart"
echo "3. Always test thoroughly after hot reload"
echo "4. Keep backup of working version"
echo "5. Some changes may require full restart"
