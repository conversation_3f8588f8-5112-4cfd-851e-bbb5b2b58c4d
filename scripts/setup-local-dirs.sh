#!/bin/bash

# Setup Local Development Directories
# This script creates the required directories for file uploads and temp storage

echo "🔧 Setting up local development directories"
echo "=========================================="

# Get user home directory
USER_HOME="${HOME}"
MEDIA_BASE="${USER_HOME}/spup-media"

echo ""
echo "📁 Creating directories in: $MEDIA_BASE"
echo "======================================="

# Create base media directory
if [ ! -d "$MEDIA_BASE" ]; then
    mkdir -p "$MEDIA_BASE"
    echo "✅ Created: $MEDIA_BASE"
else
    echo "✅ Already exists: $MEDIA_BASE"
fi

# Create temp directory
TEMP_DIR="${MEDIA_BASE}/temp"
if [ ! -d "$TEMP_DIR" ]; then
    mkdir -p "$TEMP_DIR"
    echo "✅ Created: $TEMP_DIR"
else
    echo "✅ Already exists: $TEMP_DIR"
fi

# Create upload directory
UPLOAD_DIR="${MEDIA_BASE}/upload"
if [ ! -d "$UPLOAD_DIR" ]; then
    mkdir -p "$UPLOAD_DIR"
    echo "✅ Created: $UPLOAD_DIR"
else
    echo "✅ Already exists: $UPLOAD_DIR"
fi

# Set permissions (make writable)
chmod 755 "$MEDIA_BASE"
chmod 755 "$TEMP_DIR"
chmod 755 "$UPLOAD_DIR"

echo ""
echo "📋 Directory Structure Created:"
echo "=============================="
echo "Base:   $MEDIA_BASE"
echo "Temp:   $TEMP_DIR"
echo "Upload: $UPLOAD_DIR"

echo ""
echo "🔍 Verifying directories:"
echo "========================"
ls -la "$MEDIA_BASE"

echo ""
echo "✅ Local development directories setup complete!"
echo ""
echo "📝 Configuration used in fileConfig_dev.properties:"
echo "=================================================="
echo "fileTempPath=${USER_HOME}/spup-media/temp/"
echo "fileSavePath=${USER_HOME}/spup-media/upload/"
echo "fileWebPath=/upload/"
echo "file.path.save=${USER_HOME}/spup-media"

echo ""
echo "🚀 You can now start the application with the debug configuration!"
