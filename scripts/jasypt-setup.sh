#!/bin/bash

# Jasypt Setup Script for SPUP Application
# This script helps you configure Jasypt encryption for sensitive properties

echo "🔐 Jasypt Configuration Setup for SPUP Application"
echo "=================================================="

# Check if password is provided
if [ -z "$1" ]; then
    echo "Usage: $0 <encryption_password>"
    echo ""
    echo "Example: $0 mySecretPassword123"
    echo ""
    echo "This script will:"
    echo "1. Set the JASYPT_ENCRYPTOR_PASSWORD environment variable"
    echo "2. Show you how to encrypt sensitive values"
    echo "3. Provide examples for different environments"
    exit 1
fi

ENCRYPTION_PASSWORD="$1"

echo "Setting up Jasypt with password: $ENCRYPTION_PASSWORD"
echo ""

# Set environment variable
export JASYPT_ENCRYPTOR_PASSWORD="$ENCRYPTION_PASSWORD"

echo "✅ Environment variable set:"
echo "   JASYPT_ENCRYPTOR_PASSWORD=$ENCRYPTION_PASSWORD"
echo ""

# Examples of values to encrypt
echo "🔧 Example: Encrypting common sensitive values"
echo "=============================================="

# Database password
DB_PASSWORD="123456"
echo "Database password: $DB_PASSWORD"

# WeChat secrets (examples)
WX_SECRET="af27750a173c3c53a3a69291aa36c407"
echo "WeChat secret: $WX_SECRET"

echo ""
echo "📝 To encrypt values manually, use the JasyptUtil class:"
echo "java -cp target/classes:target/lib/* com.spup.utils.JasyptUtil encrypt $ENCRYPTION_PASSWORD \"$DB_PASSWORD\""
echo ""

echo "🚀 To run your application with Jasypt:"
echo "======================================"
echo "Option 1 - Environment variable:"
echo "   export JASYPT_ENCRYPTOR_PASSWORD=$ENCRYPTION_PASSWORD"
echo "   java -jar your-app.jar"
echo ""
echo "Option 2 - JVM argument:"
echo "   java -Djasypt.encryptor.password=$ENCRYPTION_PASSWORD -jar your-app.jar"
echo ""
echo "Option 3 - Program argument:"
echo "   java -jar your-app.jar --jasypt.encryptor.password=$ENCRYPTION_PASSWORD"
echo ""

echo "📋 Configuration Files to Update:"
echo "================================="
echo "1. application.yml - Enable jasypt configuration"
echo "2. application-dev.yml - Use ENC() for sensitive values"
echo "3. application-pro.yml - Use ENC() for production secrets"
echo ""

echo "🔒 Security Best Practices:"
echo "==========================="
echo "1. Never commit the encryption password to version control"
echo "2. Use different passwords for different environments"
echo "3. Store passwords in secure environment variables or secret managers"
echo "4. Rotate encryption passwords periodically"
echo "5. Use strong, random passwords (at least 32 characters)"
echo ""

echo "✨ Setup complete! Your application is now ready to use encrypted properties."
