#!/bin/bash

# Script to consolidate duplicate enums from admin/user modules to data module

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get the script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

cd "$PROJECT_ROOT"

print_status "Starting enum consolidation..."

# Define the enums to consolidate
ENUMS=(
    "BatchStatusEnum"
    "BatchSetStatusEnum" 
    "DeletedStatusEnum"
    "WorkdayEnum"
    "ItemOrderStatusEnum"
)

# Function to update imports in a file
update_imports_in_file() {
    local file="$1"
    local enum_name="$2"
    local old_import="$3"
    local new_import="$4"
    
    if grep -q "$old_import" "$file"; then
        print_status "Updating $enum_name import in $file"
        # Use perl for better cross-platform compatibility
        perl -pi -e "s|$old_import|$new_import|g" "$file"
    fi
}

# Function to consolidate a specific enum
consolidate_enum() {
    local enum_name="$1"
    
    print_status "Consolidating $enum_name..."
    
    # Check if enum exists in data module
    local data_enum_file="spup-data/src/main/java/com/spup/enums/${enum_name}.java"
    if [ ! -f "$data_enum_file" ]; then
        print_error "Data module enum not found: $data_enum_file"
        return 1
    fi
    
    # Update imports in admin module
    local admin_enum_file="spup-admin-web/src/main/java/com/spup/admin/enums/${enum_name}.java"
    if [ -f "$admin_enum_file" ]; then
        print_status "Found admin enum: $admin_enum_file"
        
        # Find all files that import the admin enum
        local admin_import="import com.spup.admin.enums.${enum_name}"
        local new_import="import com.spup.enums.${enum_name}"
        
        # Update imports in all Java files
        find spup-admin-web/src -name "*.java" -type f | while read file; do
            update_imports_in_file "$file" "$enum_name" "$admin_import" "$new_import"
        done
        
        print_success "Updated imports for admin $enum_name"
    fi
    
    # Update imports in user module (for OrderCategoryEnum)
    local user_enum_file="spup-user-web/src/main/java/com/spup/user/enums/${enum_name}.java"
    if [ -f "$user_enum_file" ]; then
        print_status "Found user enum: $user_enum_file"
        
        # Find all files that import the user enum
        local user_import="import com.spup.user.enums.${enum_name}"
        local new_import="import com.spup.enums.${enum_name}"
        
        # Update imports in all Java files
        find spup-user-web/src -name "*.java" -type f | while read file; do
            update_imports_in_file "$file" "$enum_name" "$user_import" "$new_import"
        done
        
        print_success "Updated imports for user $enum_name"
    fi
}

# Consolidate each enum
for enum in "${ENUMS[@]}"; do
    consolidate_enum "$enum"
done

# Special handling for OrderCategoryEnum (exists in both admin and user)
print_status "Consolidating OrderCategoryEnum..."

# Check if we need to create OrderCategoryEnum in data module
if [ ! -f "spup-data/src/main/java/com/spup/enums/OrderCategoryEnum.java" ]; then
    print_warning "OrderCategoryEnum not found in data module. Using admin version as canonical."
    # Copy admin version to data module
    cp "spup-admin-web/src/main/java/com/spup/admin/enums/OrderCategoryEnum.java" "spup-data/src/main/java/com/spup/enums/OrderCategoryEnum.java"
    # Update package declaration
    perl -pi -e 's/package com\.spup\.admin\.enums;/package com.spup.enums;/g' "spup-data/src/main/java/com/spup/enums/OrderCategoryEnum.java"
    print_success "Created OrderCategoryEnum in data module"
fi

# Update imports for OrderCategoryEnum
consolidate_enum "OrderCategoryEnum"

print_status "Checking compilation..."
if mvn compile -q; then
    print_success "All modules compile successfully after import updates!"
    
    print_status "Ready to remove duplicate enum files. Run with --remove-duplicates to proceed."
    
    if [ "$1" = "--remove-duplicates" ]; then
        print_warning "Removing duplicate enum files..."
        
        # Remove admin enum duplicates
        for enum in "${ENUMS[@]}" "OrderCategoryEnum"; do
            local admin_file="spup-admin-web/src/main/java/com/spup/admin/enums/${enum}.java"
            if [ -f "$admin_file" ]; then
                rm "$admin_file"
                print_success "Removed $admin_file"
            fi
        done
        
        # Remove user enum duplicates
        local user_file="spup-user-web/src/main/java/com/spup/user/enums/OrderCategoryEnum.java"
        if [ -f "$user_file" ]; then
            rm "$user_file"
            print_success "Removed $user_file"
        fi
        
        print_status "Final compilation check..."
        if mvn compile -q; then
            print_success "✅ Enum consolidation completed successfully!"
            print_status "Summary:"
            print_status "- Updated imports in all affected files"
            print_status "- Removed duplicate enum files"
            print_status "- All modules compile successfully"
        else
            print_error "❌ Compilation failed after removing duplicates"
            exit 1
        fi
    fi
else
    print_error "❌ Compilation failed after import updates"
    exit 1
fi

print_status "Enum consolidation script completed."
