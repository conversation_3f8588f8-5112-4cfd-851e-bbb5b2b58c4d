#!/bin/bash

# Architecture Reorganization Script
# This script reorganizes the SPUP project according to the new architecture

set -e  # Exit on any error

echo "🏗️  SPUP Architecture Reorganization"
echo "===================================="

# Configuration
OLD_STRUCTURE=(
    "app-spup-admin"
    "app-spup"
    "app-db"
    "activity"
    "common"
)

NEW_STRUCTURE=(
    "spup-admin-web"
    "spup-user-web"
    "spup-data"
    "spup-activity"
    "spup-common"
)

echo ""
echo "📋 Reorganization Plan:"
echo "======================"
for i in "${!OLD_STRUCTURE[@]}"; do
    echo "${OLD_STRUCTURE[$i]} → ${NEW_STRUCTURE[$i]}"
done

echo ""
echo "⚠️  This will:"
echo "=============="
echo "1. Rename modules to follow new naming convention"
echo "2. Update all pom.xml files with new module names"
echo "3. Update package references and imports"
echo "4. Consolidate configurations"
echo "5. Update VS Code configurations"

echo ""
read -p "Do you want to continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Reorganization cancelled"
    exit 1
fi

echo ""
echo "🚀 Starting reorganization..."
echo "============================="

# Step 1: Rename directories
echo ""
echo "📁 Step 1: Renaming module directories..."
echo "========================================"

for i in "${!OLD_STRUCTURE[@]}"; do
    old_name="${OLD_STRUCTURE[$i]}"
    new_name="${NEW_STRUCTURE[$i]}"
    
    if [ -d "$old_name" ]; then
        echo "  Renaming: $old_name → $new_name"
        git mv "$old_name" "$new_name"
    else
        echo "  ⚠️  Directory not found: $old_name"
    fi
done

echo "✅ Module directories renamed"

# Step 2: Update parent pom.xml
echo ""
echo "📝 Step 2: Updating parent pom.xml..."
echo "===================================="

# Create backup
cp pom.xml pom.xml.backup

# Update module names in parent pom
sed -i.bak \
    -e 's/<module>app-spup-admin<\/module>/<module>spup-admin-web<\/module>/' \
    -e 's/<module>app-spup<\/module>/<module>spup-user-web<\/module>/' \
    -e 's/<module>app-db<\/module>/<module>spup-data<\/module>/' \
    -e 's/<module>activity<\/module>/<module>spup-activity<\/module>/' \
    -e 's/<module>common<\/module>/<module>spup-common<\/module>/' \
    pom.xml

echo "✅ Parent pom.xml updated"

# Step 3: Update individual module pom.xml files
echo ""
echo "📝 Step 3: Updating module pom.xml files..."
echo "=========================================="

update_module_pom() {
    local module_dir=$1
    local pom_file="$module_dir/pom.xml"
    
    if [ -f "$pom_file" ]; then
        echo "  Updating: $pom_file"
        
        # Create backup
        cp "$pom_file" "$pom_file.backup"
        
        # Update artifact IDs and dependency references
        sed -i.bak \
            -e 's/<artifactId>app-spup-admin<\/artifactId>/<artifactId>spup-admin-web<\/artifactId>/' \
            -e 's/<artifactId>app-spup<\/artifactId>/<artifactId>spup-user-web<\/artifactId>/' \
            -e 's/<artifactId>app-db<\/artifactId>/<artifactId>spup-data<\/artifactId>/' \
            -e 's/<artifactId>spup-db<\/artifactId>/<artifactId>spup-data<\/artifactId>/' \
            -e 's/<artifactId>activity<\/artifactId>/<artifactId>spup-activity<\/artifactId>/' \
            -e 's/<artifactId>common<\/artifactId>/<artifactId>spup-common<\/artifactId>/' \
            "$pom_file"
        
        # Remove .bak file
        rm -f "$pom_file.bak"
    else
        echo "  ⚠️  POM file not found: $pom_file"
    fi
}

# Update all module pom.xml files
for new_name in "${NEW_STRUCTURE[@]}"; do
    update_module_pom "$new_name"
done

echo "✅ Module pom.xml files updated"

# Step 4: Update VS Code configurations
echo ""
echo "🔧 Step 4: Updating VS Code configurations..."
echo "============================================="

if [ -f ".vscode/launch.json" ]; then
    echo "  Updating launch.json..."
    cp .vscode/launch.json .vscode/launch.json.backup
    
    sed -i.bak \
        -e 's/app-spup-admin/spup-admin-web/g' \
        -e 's/app-spup/spup-user-web/g' \
        -e 's/spup-admin/spup-admin-web/g' \
        .vscode/launch.json
    
    rm -f .vscode/launch.json.bak
fi

if [ -f ".vscode/settings.json" ]; then
    echo "  Updating settings.json..."
    cp .vscode/settings.json .vscode/settings.json.backup
    
    sed -i.bak \
        -e 's/app-spup-admin/spup-admin-web/g' \
        -e 's/app-spup/spup-user-web/g' \
        .vscode/settings.json
    
    rm -f .vscode/settings.json.bak
fi

echo "✅ VS Code configurations updated"

# Step 5: Update documentation
echo ""
echo "📚 Step 5: Updating documentation..."
echo "==================================="

if [ -f "README.md" ]; then
    echo "  Updating README.md..."
    cp README.md README.md.backup
    
    sed -i.bak \
        -e 's/app-spup-admin/spup-admin-web/g' \
        -e 's/app-spup/spup-user-web/g' \
        -e 's/app-db/spup-data/g' \
        README.md
    
    rm -f README.md.bak
fi

echo "✅ Documentation updated"

echo ""
echo "🎉 Architecture Reorganization Complete!"
echo "========================================"

echo ""
echo "📋 Summary of Changes:"
echo "====================="
echo "✅ Renamed 5 modules to follow new naming convention"
echo "✅ Updated parent pom.xml with new module names"
echo "✅ Updated all module pom.xml files"
echo "✅ Updated VS Code configurations"
echo "✅ Updated documentation"

echo ""
echo "🔧 Next Steps:"
echo "=============="
echo "1. Test compilation: mvn clean compile"
echo "2. Run tests: mvn test"
echo "3. Update any remaining references manually"
echo "4. Commit changes: git add -A && git commit -m 'Architecture reorganization'"

echo ""
echo "📁 New Module Structure:"
echo "======================="
for new_name in "${NEW_STRUCTURE[@]}"; do
    if [ -d "$new_name" ]; then
        echo "✅ $new_name"
    else
        echo "❌ $new_name (missing)"
    fi
done

echo ""
echo "⚠️  Manual Steps Required:"
echo "========================="
echo "1. Check for any hardcoded paths in configuration files"
echo "2. Update any scripts that reference old module names"
echo "3. Update deployment configurations"
echo "4. Verify all imports and package references"

echo ""
echo "🚀 Ready to test the new architecture!"
