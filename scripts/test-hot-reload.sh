#!/bin/bash

# Hot Reload Test Script
# This script helps you test if code changes are being reflected

echo "🔥 Hot Reload Test Script"
echo "========================"

# Function to check if applications are running
check_app_running() {
    local app_spup_running=false
    local app_spup_admin_running=false

    if curl -s http://localhost:8080/health > /dev/null 2>&1; then
        echo "✅ app-spup is running on port 8080"
        app_spup_running=true
    else
        echo "❌ app-spup is not running on port 8080"
    fi

    if curl -s http://localhost:8888/health > /dev/null 2>&1; then
        echo "✅ app-spup-admin is running on port 8888"
        app_spup_admin_running=true
    else
        echo "❌ app-spup-admin is not running on port 8888"
    fi

    if [ "$app_spup_running" = true ] || [ "$app_spup_admin_running" = true ]; then
        return 0
    else
        return 1
    fi
}

# Function to make test changes in both modules
make_test_change() {
    local timestamp=$(date +"%Y-%m-%d %H:%M:%S")
    echo "Making test changes at $timestamp..."

    # Test change in app-spup module
    if [ -f "app-spup/src/main/java/com/spup/javaConfig/AppointmentConfig.java" ]; then
        sed -i.bak "s|// Test timestamp:.*|// Test timestamp: $timestamp|g" app-spup/src/main/java/com/spup/javaConfig/AppointmentConfig.java

        if ! grep -q "Test timestamp:" app-spup/src/main/java/com/spup/javaConfig/AppointmentConfig.java; then
            sed -i.bak "s|@Component|// Test timestamp: $timestamp\n@Component|g" app-spup/src/main/java/com/spup/javaConfig/AppointmentConfig.java
        fi
        echo "✅ app-spup test change made"
    fi

    # Test change in app-spup-admin module
    if [ -f "app-spup-admin/src/main/java/com/spup/controller/auth/AppLoginController.java" ]; then
        sed -i.bak "s|HOT RELOAD TEST.*|HOT RELOAD TEST - $timestamp\"|g" app-spup-admin/src/main/java/com/spup/controller/auth/AppLoginController.java
        echo "✅ app-spup-admin test change made"
    fi

    echo "✅ All test changes completed at $timestamp"
}

# Function to check if changes were compiled in both modules
check_compiled() {
    echo "Checking compilation status for both modules..."

    # Check app-spup module
    local app_spup_class="app-spup/target/classes/com/spup/javaConfig/AppointmentConfig.class"
    if [ -f "$app_spup_class" ]; then
        local mod_time=$(stat -f "%m" "$app_spup_class" 2>/dev/null || stat -c "%Y" "$app_spup_class" 2>/dev/null)
        local current_time=$(date +%s)
        local diff=$((current_time - mod_time))

        if [ $diff -lt 30 ]; then
            echo "✅ app-spup: Class file recently compiled ($diff seconds ago)"
        else
            echo "⚠️  app-spup: Class file is old ($diff seconds ago)"
        fi
    else
        echo "❌ app-spup: Class file not found"
    fi

    # Check app-spup-admin module
    local app_admin_class="app-spup-admin/target/classes/com/spup/controller/auth/AppLoginController.class"
    if [ -f "$app_admin_class" ]; then
        local mod_time=$(stat -f "%m" "$app_admin_class" 2>/dev/null || stat -c "%Y" "$app_admin_class" 2>/dev/null)
        local current_time=$(date +%s)
        local diff=$((current_time - mod_time))

        if [ $diff -lt 30 ]; then
            echo "✅ app-spup-admin: Class file recently compiled ($diff seconds ago)"
        else
            echo "⚠️  app-spup-admin: Class file is old ($diff seconds ago)"
        fi
    else
        echo "❌ app-spup-admin: Class file not found"
    fi
}

echo ""
echo "📋 Current Status:"
echo "=================="

# Check if app is running
check_app_running

# Check current class file
check_compiled

echo ""
echo "🧪 Testing Hot Reload:"
echo "======================"

echo "1. Make a test change..."
make_test_change

echo "2. Wait for compilation..."
sleep 3

echo "3. Check if compiled..."
check_compiled

echo ""
echo "📝 Manual Testing Steps:"
echo "========================"
echo "1. Start your application with: mvn spring-boot:run"
echo "2. Make a code change in AppointmentConfig.java"
echo "3. Save the file"
echo "4. Watch the console for restart messages"
echo "5. Check if your changes are reflected"

echo ""
echo "🔧 Troubleshooting:"
echo "==================="
echo "If hot reload doesn't work:"
echo "1. Check IDE settings (Build automatically)"
echo "2. Verify DevTools dependency is added"
echo "3. Make sure you're running with mvn spring-boot:run"
echo "4. Check for compilation errors"
echo "5. Try manual restart: Ctrl+C and restart"

echo ""
echo "✨ Test complete!"
