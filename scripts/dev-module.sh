#!/bin/bash

# Module Development Script
# Quick build for specific modules during development

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get the script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

cd "$PROJECT_ROOT"

# Available modules
MODULES=("spup-common" "spup-data" "spup-core" "spup-activity" "spup-admin-web" "spup-user-web")

# Function to show usage
show_usage() {
    echo "Usage: $0 <module-name> [action]"
    echo ""
    echo "Available modules:"
    for module in "${MODULES[@]}"; do
        echo "  - $module"
    done
    echo ""
    echo "Available actions:"
    echo "  compile   - Compile the module and dependencies (default)"
    echo "  install   - Install the module and dependencies"
    echo "  test      - Run tests for the module"
    echo "  clean     - Clean the module"
    echo ""
    echo "Examples:"
    echo "  $0 spup-core                    # Quick compile spup-core and dependencies"
    echo "  $0 spup-admin-web install       # Install spup-admin-web and dependencies"
    echo "  $0 spup-user-web test           # Run tests for spup-user-web"
}

# Check if module is provided
if [ $# -eq 0 ]; then
    print_error "Module name is required"
    show_usage
    exit 1
fi

MODULE=$1
ACTION=${2:-compile}

# Validate module
if [[ ! " ${MODULES[@]} " =~ " ${MODULE} " ]]; then
    print_error "Invalid module: $MODULE"
    show_usage
    exit 1
fi

# Module dependency mapping
declare -A MODULE_DEPS
MODULE_DEPS["spup-common"]=""
MODULE_DEPS["spup-data"]="spup-common"
MODULE_DEPS["spup-core"]="spup-common,spup-data"
MODULE_DEPS["spup-activity"]="spup-common,spup-data,spup-core"
MODULE_DEPS["spup-admin-web"]="spup-common,spup-data,spup-core"
MODULE_DEPS["spup-user-web"]="spup-common,spup-data,spup-core"

# Build dependency list
DEPS=${MODULE_DEPS[$MODULE]}
if [ -n "$DEPS" ]; then
    DEP_LIST="$DEPS,$MODULE"
else
    DEP_LIST="$MODULE"
fi

# Execute based on action
case $ACTION in
    compile)
        print_status "Compiling $MODULE and its dependencies..."
        mvn compile -pl $DEP_LIST -q
        ;;
    install)
        print_status "Installing $MODULE and its dependencies..."
        mvn install -pl $DEP_LIST -DskipTests -q
        ;;
    test)
        print_status "Running tests for $MODULE..."
        mvn test -pl $MODULE -q
        ;;
    clean)
        print_status "Cleaning $MODULE..."
        mvn clean -pl $MODULE -q
        ;;
    *)
        print_error "Invalid action: $ACTION"
        show_usage
        exit 1
        ;;
esac

if [ $? -eq 0 ]; then
    print_success "$ACTION completed successfully for $MODULE"
else
    print_error "$ACTION failed for $MODULE"
    exit 1
fi
