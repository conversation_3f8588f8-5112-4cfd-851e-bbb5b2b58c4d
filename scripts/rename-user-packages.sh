#!/bin/bash

# Script to rename packages in spup-user-web from com.spup.* to com.spup.user.*

set -e

echo "🔄 Starting package renaming for spup-user-web..."

# Define the module path
MODULE_PATH="./spup-user-web/src/main/java"

# Create new directory structure
echo "📁 Creating new directory structure..."
mkdir -p "$MODULE_PATH/com/spup/user"

# Move main application class and other root files
if [ -f "$MODULE_PATH/com/spup/SpupApplication.java" ]; then
    mv "$MODULE_PATH/com/spup/SpupApplication.java" "$MODULE_PATH/com/spup/user/"
fi

if [ -f "$MODULE_PATH/com/spup/TaskMonitoringAspect.java" ]; then
    mv "$MODULE_PATH/com/spup/TaskMonitoringAspect.java" "$MODULE_PATH/com/spup/user/"
fi

# Move all other packages
echo "📦 Moving packages..."
for dir in config controller counter dto enums init interceptor javaConfig mpapi service task utils; do
    if [ -d "$MODULE_PATH/com/spup/$dir" ]; then
        echo "Moving $dir..."
        mv "$MODULE_PATH/com/spup/$dir" "$MODULE_PATH/com/spup/user/"
    fi
done

echo "✅ Directory structure updated!"

# Update package declarations in all Java files
echo "🔧 Updating package declarations..."

find "$MODULE_PATH/com/spup/user" -name "*.java" -type f -exec sed -i '' 's/^package com\.spup\./package com.spup.user./g' {} \;

echo "✅ Package declarations updated!"

# Update import statements
echo "🔧 Updating import statements..."

find "$MODULE_PATH" -name "*.java" -type f -exec sed -i '' 's/import com\.spup\./import com.spup.user./g' {} \;

echo "✅ Import statements updated!"

echo "🎉 Package renaming for spup-user-web completed!"
