#!/bin/bash

# Comprehensive script to find duplicate classes and code patterns

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${PURPLE}=== $1 ===${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_duplicate() {
    echo -e "${YELLOW}[DUPLICATE]${NC} $1"
}

print_consolidation() {
    echo -e "${GREEN}[CONSOLIDATION]${NC} $1"
}

# Get the script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

cd "$PROJECT_ROOT"

print_header "DUPLICATE CLASS ANALYSIS"

# 1. Find duplicate class names
print_status "Finding duplicate class names..."
DUPLICATE_CLASSES=$(find . -name "*.java" -not -path "./target/*" -not -path "./.git/*" -not -path "./contract-tests/*" | xargs basename -s .java | sort | uniq -d)

echo "$DUPLICATE_CLASSES" | while read class_name; do
    if [ ! -z "$class_name" ]; then
        print_duplicate "Class: $class_name"
        find . -name "${class_name}.java" -not -path "./target/*" -not -path "./.git/*" -not -path "./contract-tests/*" | sed 's/^/  /'
        echo ""
    fi
done

print_header "DTO CONSOLIDATION CANDIDATES"

# 2. Analyze DTO similarities
print_status "Analyzing DTO similarities..."

# Find all DTOs
DTO_FILES=$(find . -name "*DTO.java" -o -name "*Request.java" -o -name "*Response.java" | grep -v target | grep -v ".git")

# Group by similar names
echo "$DTO_FILES" | sed 's|.*/||' | sed 's|\.java||' | sort | uniq -d | while read dto_name; do
    if [ ! -z "$dto_name" ]; then
        print_duplicate "DTO: $dto_name"
        find . -name "${dto_name}.java" -not -path "./target/*" -not -path "./.git/*" | while read file; do
            lines=$(wc -l < "$file")
            echo "  $file ($lines lines)"
        done
        echo ""
    fi
done

print_header "SERVICE IMPLEMENTATION ANALYSIS"

# 3. Find duplicate service implementations
print_status "Finding duplicate service implementations..."

find . -name "*ServiceImpl.java" -not -path "./target/*" -not -path "./.git/*" | sed 's|.*/||' | sed 's|\.java||' | sort | uniq -d | while read service_name; do
    if [ ! -z "$service_name" ]; then
        print_duplicate "Service: $service_name"
        find . -name "${service_name}.java" -not -path "./target/*" -not -path "./.git/*" | while read file; do
            lines=$(wc -l < "$file")
            echo "  $file ($lines lines)"
        done
        echo ""
    fi
done

print_header "UTILITY CLASS ANALYSIS"

# 4. Find utility classes
print_status "Finding utility classes..."

find . -name "*Util.java" -o -name "*Utils.java" -o -name "*Helper.java" | grep -v target | grep -v ".git" | sed 's|.*/||' | sed 's|\.java||' | sort | uniq -d | while read util_name; do
    if [ ! -z "$util_name" ]; then
        print_duplicate "Utility: $util_name"
        find . -name "${util_name}.java" -not -path "./target/*" -not -path "./.git/*" | while read file; do
            lines=$(wc -l < "$file")
            echo "  $file ($lines lines)"
        done
        echo ""
    fi
done

print_header "CONSOLIDATION RECOMMENDATIONS"

print_consolidation "High Priority Candidates:"
echo "1. AppAppointmentOrderRequest (3 files) - Very similar DTOs"
echo "2. AppAppointmentOrderServiceImpl (2 files) - Large service implementations"
echo "3. AppAppointmentOrderResponse (multiple files) - Response DTOs"
echo "4. ActivityRoundCheckinDTO/Vo/Pojo (multiple files) - Activity-related DTOs"

print_consolidation "Medium Priority Candidates:"
echo "1. Various *ServiceImpl classes with similar functionality"
echo "2. Response DTOs that could be unified"
echo "3. Configuration classes"

print_consolidation "Analysis Complete!"
echo "Review the duplicates above and prioritize based on:"
echo "- File size (larger files = more impact)"
echo "- Similarity (use diff to compare)"
echo "- Usage frequency (grep for imports)"
echo "- Business logic overlap"
