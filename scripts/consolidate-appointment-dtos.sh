#!/bin/bash

# Script to consolidate AppAppointment DTOs

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Get the script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

cd "$PROJECT_ROOT"

print_status "Starting AppAppointment DTO consolidation..."

# 1. Update imports for AppAppointmentOrderRequest
print_status "Updating AppAppointmentOrderRequest imports..."

# Update user module
find spup-user-web/src -name "*.java" -type f -exec grep -l "com.spup.user.dto.AppAppointmentOrderRequest" {} \; | while read file; do
    print_status "Updating imports in: $file"
    sed -i '' 's/import com\.spup\.user\.dto\.AppAppointmentOrderRequest;/import com.spup.core.dto.AppAppointmentOrderRequest;/g' "$file"
done

# Update admin module
find spup-admin-web/src -name "*.java" -type f -exec grep -l "com.spup.admin.dto.AppAppointmentOrderRequest" {} \; | while read file; do
    print_status "Updating imports in: $file"
    sed -i '' 's/import com\.spup\.admin\.dto\.AppAppointmentOrderRequest;/import com.spup.core.dto.AppAppointmentOrderRequest;/g' "$file"
done

# Update activity module
find spup-activity/src -name "*.java" -type f -exec grep -l "com.spup.activity.dto.AppAppointmentOrderRequest" {} \; | while read file; do
    print_status "Updating imports in: $file"
    sed -i '' 's/import com\.spup\.activity\.dto\.AppAppointmentOrderRequest;/import com.spup.core.dto.AppAppointmentOrderRequest;/g' "$file"
done

print_status "Testing compilation..."
if mvn compile -q; then
    print_success "✅ Compilation successful after import updates!"
    
    print_warning "Ready to remove duplicate files. Continue? (y/n)"
    read -r response
    if [ "$response" = "y" ]; then
        print_status "Removing duplicate DTO files..."
        
        # Remove duplicates
        if [ -f "spup-user-web/src/main/java/com/spup/user/dto/AppAppointmentOrderRequest.java" ]; then
            rm "spup-user-web/src/main/java/com/spup/user/dto/AppAppointmentOrderRequest.java"
            print_success "Removed user AppAppointmentOrderRequest"
        fi
        
        if [ -f "spup-admin-web/src/main/java/com/spup/admin/dto/AppAppointmentOrderRequest.java" ]; then
            rm "spup-admin-web/src/main/java/com/spup/admin/dto/AppAppointmentOrderRequest.java"
            print_success "Removed admin AppAppointmentOrderRequest"
        fi
        
        if [ -f "spup-activity/src/main/java/com/spup/activity/dto/AppAppointmentOrderRequest.java" ]; then
            rm "spup-activity/src/main/java/com/spup/activity/dto/AppAppointmentOrderRequest.java"
            print_success "Removed activity AppAppointmentOrderRequest"
        fi
        
        print_status "Final compilation test..."
        if mvn compile -q; then
            print_success "✅ AppAppointmentOrderRequest consolidation completed successfully!"
            print_status "Summary:"
            print_status "- Consolidated 3 duplicate DTOs into spup-core"
            print_status "- Updated all imports across modules"
            print_status "- Removed 3 duplicate files"
            print_status "- All modules compile successfully"
        else
            print_warning "❌ Compilation failed after removing duplicates"
            exit 1
        fi
    else
        print_status "Skipping file removal. Import updates completed."
    fi
else
    print_warning "❌ Compilation failed after import updates"
    exit 1
fi

# 2. Consolidate AppAppointmentOrderTemporaryExhibitionRequest
print_status "Starting AppAppointmentOrderTemporaryExhibitionRequest consolidation..."

# Update user module
find spup-user-web/src -name "*.java" -type f -exec grep -l "com.spup.user.dto.AppAppointmentOrderTemporaryExhibitionRequest" {} \; | while read file; do
    print_status "Updating imports in: $file"
    sed -i '' 's/import com\.spup\.user\.dto\.AppAppointmentOrderTemporaryExhibitionRequest;/import com.spup.core.dto.AppAppointmentOrderTemporaryExhibitionRequest;/g' "$file"
done

# Update admin module
find spup-admin-web/src -name "*.java" -type f -exec grep -l "com.spup.admin.dto.AppAppointmentOrderTemporaryExhibitionRequest" {} \; | while read file; do
    print_status "Updating imports in: $file"
    sed -i '' 's/import com\.spup\.admin\.dto\.AppAppointmentOrderTemporaryExhibitionRequest;/import com.spup.core.dto.AppAppointmentOrderTemporaryExhibitionRequest;/g' "$file"
done

print_status "Testing compilation..."
if mvn compile -q; then
    print_success "✅ Compilation successful after import updates!"

    print_status "Removing duplicate TemporaryExhibitionRequest files..."

    # Remove duplicates
    if [ -f "spup-user-web/src/main/java/com/spup/user/dto/AppAppointmentOrderTemporaryExhibitionRequest.java" ]; then
        rm "spup-user-web/src/main/java/com/spup/user/dto/AppAppointmentOrderTemporaryExhibitionRequest.java"
        print_success "Removed user AppAppointmentOrderTemporaryExhibitionRequest"
    fi

    if [ -f "spup-admin-web/src/main/java/com/spup/admin/dto/AppAppointmentOrderTemporaryExhibitionRequest.java" ]; then
        rm "spup-admin-web/src/main/java/com/spup/admin/dto/AppAppointmentOrderTemporaryExhibitionRequest.java"
        print_success "Removed admin AppAppointmentOrderTemporaryExhibitionRequest"
    fi

    print_status "Final compilation test..."
    if mvn compile -q; then
        print_success "✅ AppAppointmentOrderTemporaryExhibitionRequest consolidation completed!"
    else
        print_warning "❌ Compilation failed after removing TemporaryExhibitionRequest duplicates"
        exit 1
    fi
else
    print_warning "❌ Compilation failed after TemporaryExhibitionRequest import updates"
    exit 1
fi

print_success "All DTO consolidations completed successfully!"
