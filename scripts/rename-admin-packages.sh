#!/bin/bash

# Script to rename packages in spup-admin-web from com.spup.* to com.spup.admin.*

set -e

echo "🔄 Starting package renaming for spup-admin-web..."

# Define the module path
MODULE_PATH="./spup-admin-web/src/main/java"

# Create new directory structure
echo "📁 Creating new directory structure..."
mkdir -p "$MODULE_PATH/com/spup/admin"

# Move directories and rename packages
echo "📦 Moving packages..."

# Move main application class
if [ -f "$MODULE_PATH/com/spup/SpupAdmin.java" ]; then
    mv "$MODULE_PATH/com/spup/SpupAdmin.java" "$MODULE_PATH/com/spup/admin/"
fi

# Move all other packages
for dir in config controller counter dto enums exception interceptor javaConfig mp poi service task utils; do
    if [ -d "$MODULE_PATH/com/spup/$dir" ]; then
        echo "Moving $dir..."
        mv "$MODULE_PATH/com/spup/$dir" "$MODULE_PATH/com/spup/admin/"
    fi
done

echo "✅ Directory structure updated!"

# Update package declarations in all Java files
echo "🔧 Updating package declarations..."

find "$MODULE_PATH/com/spup/admin" -name "*.java" -type f -exec sed -i '' 's/^package com\.spup\./package com.spup.admin./g' {} \;

echo "✅ Package declarations updated!"

# Update import statements
echo "🔧 Updating import statements..."

find "$MODULE_PATH" -name "*.java" -type f -exec sed -i '' 's/import com\.spup\./import com.spup.admin./g' {} \;

echo "✅ Import statements updated!"

echo "🎉 Package renaming for spup-admin-web completed!"
