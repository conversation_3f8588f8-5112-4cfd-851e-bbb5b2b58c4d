#!/bin/bash

# Script to update javax imports to jakarta imports in Java source code

set -e

echo "🔄 Updating javax imports to jakarta imports in Java source code..."

# Find all Java files and update javax imports to jakarta
find . -name "*.java" -type f -exec sed -i '' 's/import javax\.annotation\./import jakarta.annotation./g' {} \;
find . -name "*.java" -type f -exec sed -i '' 's/import javax\.validation\./import jakarta.validation./g' {} \;
find . -name "*.java" -type f -exec sed -i '' 's/import javax\.persistence\./import jakarta.persistence./g' {} \;
find . -name "*.java" -type f -exec sed -i '' 's/import javax\.transaction\./import jakarta.transaction./g' {} \;
find . -name "*.java" -type f -exec sed -i '' 's/import javax\.servlet\./import jakarta.servlet./g' {} \;
find . -name "*.java" -type f -exec sed -i '' 's/import javax\.xml\.bind\./import jakarta.xml.bind./g' {} \;
find . -name "*.java" -type f -exec sed -i '' 's/import javax\.activation\./import jakarta.activation./g' {} \;

echo "✅ Java source code imports updated from javax to jakarta!"

# Count the number of files that were updated
UPDATED_FILES=$(find . -name "*.java" -type f -exec grep -l "import jakarta\." {} \; | wc -l)
echo "📊 Updated imports in $UPDATED_FILES Java files"

echo "🎉 javax to jakarta migration completed!"
