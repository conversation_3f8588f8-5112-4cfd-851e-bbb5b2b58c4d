#!/bin/bash

# Script to consolidate duplicate service classes

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Get the script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

cd "$PROJECT_ROOT"

print_status "Starting service consolidation..."

# 1. Consolidate AppAppointmentInstructionsService
print_status "Consolidating AppAppointmentInstructionsService..."

# Update user module imports
find spup-user-web/src -name "*.java" -type f -exec grep -l "com.spup.user.service.appointment.IAppAppointmentInstructionsService" {} \; | while read file; do
    print_status "Updating interface import in: $file"
    sed -i '' 's/import com\.spup\.user\.service\.appointment\.IAppAppointmentInstructionsService;/import com.spup.core.service.IAppAppointmentInstructionsService;/g' "$file"
done

find spup-user-web/src -name "*.java" -type f -exec grep -l "com.spup.user.service.appointment.impl.AppAppointmentInstructionsServiceImpl" {} \; | while read file; do
    print_status "Updating implementation import in: $file"
    sed -i '' 's/import com\.spup\.user\.service\.appointment\.impl\.AppAppointmentInstructionsServiceImpl;/import com.spup.core.service.impl.AppAppointmentInstructionsServiceImpl;/g' "$file"
done

# Update admin module imports
find spup-admin-web/src -name "*.java" -type f -exec grep -l "com.spup.admin.service.appointment.IAppAppointmentInstructionsService" {} \; | while read file; do
    print_status "Updating interface import in: $file"
    sed -i '' 's/import com\.spup\.admin\.service\.appointment\.IAppAppointmentInstructionsService;/import com.spup.core.service.IAppAppointmentInstructionsService;/g' "$file"
done

find spup-admin-web/src -name "*.java" -type f -exec grep -l "com.spup.admin.service.appointment.impl.AppAppointmentInstructionsServiceImpl" {} \; | while read file; do
    print_status "Updating implementation import in: $file"
    sed -i '' 's/import com\.spup\.admin\.service\.appointment\.impl\.AppAppointmentInstructionsServiceImpl;/import com.spup.core.service.impl.AppAppointmentInstructionsServiceImpl;/g' "$file"
done

print_status "Testing compilation..."
if mvn compile -q; then
    print_success "✅ Compilation successful after import updates!"
    
    print_warning "Ready to remove duplicate service files. Continue? (y/n)"
    read -r response
    if [ "$response" = "y" ]; then
        print_status "Removing duplicate service files..."
        
        # Remove duplicates
        if [ -f "spup-user-web/src/main/java/com/spup/user/service/appointment/IAppAppointmentInstructionsService.java" ]; then
            rm "spup-user-web/src/main/java/com/spup/user/service/appointment/IAppAppointmentInstructionsService.java"
            print_success "Removed user IAppAppointmentInstructionsService"
        fi
        
        if [ -f "spup-user-web/src/main/java/com/spup/user/service/appointment/impl/AppAppointmentInstructionsServiceImpl.java" ]; then
            rm "spup-user-web/src/main/java/com/spup/user/service/appointment/impl/AppAppointmentInstructionsServiceImpl.java"
            print_success "Removed user AppAppointmentInstructionsServiceImpl"
        fi
        
        if [ -f "spup-admin-web/src/main/java/com/spup/admin/service/appointment/IAppAppointmentInstructionsService.java" ]; then
            rm "spup-admin-web/src/main/java/com/spup/admin/service/appointment/IAppAppointmentInstructionsService.java"
            print_success "Removed admin IAppAppointmentInstructionsService"
        fi
        
        if [ -f "spup-admin-web/src/main/java/com/spup/admin/service/appointment/impl/AppAppointmentInstructionsServiceImpl.java" ]; then
            rm "spup-admin-web/src/main/java/com/spup/admin/service/appointment/impl/AppAppointmentInstructionsServiceImpl.java"
            print_success "Removed admin AppAppointmentInstructionsServiceImpl"
        fi
        
        print_status "Final compilation test..."
        if mvn compile -q; then
            print_success "✅ AppAppointmentInstructionsService consolidation completed successfully!"
            print_status "Summary:"
            print_status "- Consolidated 2 duplicate interfaces into spup-core"
            print_status "- Consolidated 2 duplicate implementations into spup-core"
            print_status "- Updated all imports across modules"
            print_status "- Removed 4 duplicate files"
            print_status "- All modules compile successfully"
        else
            print_warning "❌ Compilation failed after removing duplicates"
            exit 1
        fi
    else
        print_status "Skipping file removal. Import updates completed."
    fi
else
    print_warning "❌ Compilation failed after import updates"
    exit 1
fi

print_success "Service consolidation script completed!"
