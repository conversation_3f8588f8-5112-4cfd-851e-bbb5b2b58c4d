#!/bin/bash

# Exception Logging Test Script
# This script helps you test the enhanced exception logging

echo "🔍 Enhanced Exception Logging Test"
echo "=================================="

echo ""
echo "📋 What's Been Enhanced:"
echo "======================="
echo "✅ Original exception details are now logged BEFORE handler exceptions"
echo "✅ Request URL and method are logged for context"
echo "✅ Full exception cause chain is logged (up to 5 levels)"
echo "✅ Handler exceptions are caught and logged separately"
echo "✅ Safe error responses prevent infinite exception loops"

echo ""
echo "🔍 New Log Format:"
echo "=================="
echo "When an exception occurs, you'll now see:"
echo ""
echo "=== ORIGINAL EXCEPTION: SomeException ==="
echo "Request URL: http://localhost:8080/api/some-endpoint"
echo "Request Method: POST"
echo "Exception message: The actual error message"
echo "Full stack trace: [complete stack trace]"
echo "Caused by (level 1): IOException - Connection failed"
echo "Caused by (level 2): SocketException - Network unreachable"
echo ""
echo "If the exception handler itself fails:"
echo "=== EXCEPTION IN EXCEPTION HANDLER ==="
echo "Original exception was: SomeException - The actual error message"
echo "Handler exception: [handler exception details]"

echo ""
echo "🧪 Testing Steps:"
echo "================="
echo "1. Start your application"
echo "2. Trigger an exception (e.g., call an endpoint that fails)"
echo "3. Check the logs for the enhanced format"
echo "4. Look for the '=== ORIGINAL EXCEPTION ===' markers"

echo ""
echo "📝 Example Test Endpoints:"
echo "=========================="
echo "# Test validation exception"
echo "curl -X POST http://localhost:8888/api/some-endpoint -H 'Content-Type: application/json' -d '{}'"
echo ""
echo "# Test general exception"
echo "curl -X GET http://localhost:8888/api/non-existent-endpoint"

echo ""
echo "🔧 Key Improvements:"
echo "===================="
echo "1. **Original Exception Visibility**: You'll see the actual exception that triggered the handler"
echo "2. **Request Context**: URL and method help identify which endpoint failed"
echo "3. **Cause Chain**: Root cause analysis with nested exception details"
echo "4. **Handler Safety**: If the handler fails, you'll see both exceptions"
echo "5. **Response Safety**: Prevents 'getWriter() already called' errors"

echo ""
echo "📊 Log Levels:"
echo "=============="
echo "- ERROR: Original exceptions and handler failures"
echo "- WARN: Response already committed warnings"
echo "- INFO: Normal application flow"

echo ""
echo "🎯 What to Look For:"
echo "===================="
echo "Before: Only saw 'getWriter() has already been called'"
echo "After: See the original exception + context + handler exception"
echo ""
echo "Example improved log:"
echo "=== ORIGINAL EXCEPTION: NullPointerException ==="
echo "Request URL: http://localhost:8888/oauth2/access_token/invalid_code"
echo "Request Method: GET"
echo "Exception message: Cannot invoke method on null object"
echo "[Full stack trace of the NPE]"
echo ""
echo "=== EXCEPTION IN EXCEPTION HANDLER ==="
echo "Original exception was: NullPointerException - Cannot invoke method on null object"
echo "Handler exception: IllegalStateException - getWriter() has already been called"

echo ""
echo "✨ Test complete! Check your application logs for the enhanced exception details."
