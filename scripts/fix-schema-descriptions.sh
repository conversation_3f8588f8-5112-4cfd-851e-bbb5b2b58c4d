#!/bin/bash

# Script to fix Schema descriptions with escaped quotes and consolidate DTOs

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get the script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

cd "$PROJECT_ROOT"

print_status "Starting Schema description fixes and DTO consolidation..."

# Define the files that need schema fixes
FILES_TO_FIX=(
    "spup-user-web/src/main/java/com/spup/user/dto/AppAppointmentOrderRequest.java"
    "spup-user-web/src/main/java/com/spup/user/dto/AppAppointmentOrderTemporaryExhibitionRequest.java"
    "spup-activity/src/main/java/com/spup/activity/dto/AppAppointmentOrderRequest.java"
    "spup-admin-web/src/main/java/com/spup/admin/dto/AppAppointmentOrderRequest.java"
    "spup-admin-web/src/main/java/com/spup/admin/dto/AppAppointmentOrderTemporaryExhibitionRequest.java"
)

# Function to fix schema descriptions in a file
fix_schema_description() {
    local file="$1"
    
    if [ ! -f "$file" ]; then
        print_warning "File not found: $file"
        return 1
    fi
    
    print_status "Fixing schema descriptions in: $file"
    
    # Create a backup
    cp "$file" "$file.backup"
    
    # Fix the schema description using perl for better handling of multiline patterns
    perl -i -pe '
        # Fix the contacts field schema description
        if (/\@Schema\(description = "联系人列表，数组字符串格式，如:\[\{/) {
            # Start collecting the multiline schema
            my $schema_content = $_;
            while (<>) {
                $schema_content .= $_;
                if (/"\]"\)/) {
                    last;
                }
            }
            # Replace the entire schema with the fixed version
            $schema_content =~ s/\@Schema\(description = "联系人列表，数组字符串格式，如:\[\{[^}]*\}[^"]*"\)/\@Schema(description = "联系人列表，数组字符串格式", example = "[{\\\"idcardCategory\\\": 1, \\\"idcardNo\\\": \\\"string\\\", \\\"name\\\": \\\"string\\\", \\\"phone\\\": \\\"string\\\"}]")/s;
            print $schema_content;
            next;
        }
    ' "$file"
    
    print_success "Fixed schema descriptions in: $file"
}

# Fix schema descriptions in all identified files
for file in "${FILES_TO_FIX[@]}"; do
    fix_schema_description "$file"
done

print_status "Schema description fixes completed."

# Now handle DTO consolidation
print_status "Starting DTO consolidation..."

# Update imports in admin module
print_status "Updating imports in admin module..."
find spup-admin-web/src -name "*.java" -type f -exec grep -l "com.spup.admin.dto.AppAppointmentItemOrderRequest" {} \; | while read file; do
    print_status "Updating imports in: $file"
    sed -i '' 's/import com\.spup\.admin\.dto\.AppAppointmentItemOrderRequest;/import com.spup.core.dto.AppAppointmentItemOrderRequest;/g' "$file"
done

# Update imports in user module  
print_status "Updating imports in user module..."
find spup-user-web/src -name "*.java" -type f -exec grep -l "com.spup.user.dto.AppAppointmentItemOrderRequest" {} \; | while read file; do
    print_status "Updating imports in: $file"
    sed -i '' 's/import com\.spup\.user\.dto\.AppAppointmentItemOrderRequest;/import com.spup.core.dto.AppAppointmentItemOrderRequest;/g' "$file"
done

print_status "Checking compilation..."
if mvn compile -q; then
    print_success "All modules compile successfully after fixes!"
    
    print_status "Ready to remove duplicate DTO files. Run with --remove-duplicates to proceed."
    
    if [ "$1" = "--remove-duplicates" ]; then
        print_warning "Removing duplicate DTO files..."
        
        # Remove duplicate files
        if [ -f "spup-admin-web/src/main/java/com/spup/admin/dto/AppAppointmentItemOrderRequest.java" ]; then
            rm "spup-admin-web/src/main/java/com/spup/admin/dto/AppAppointmentItemOrderRequest.java"
            print_success "Removed spup-admin-web/src/main/java/com/spup/admin/dto/AppAppointmentItemOrderRequest.java"
        fi
        
        if [ -f "spup-user-web/src/main/java/com/spup/user/dto/AppAppointmentItemOrderRequest.java" ]; then
            rm "spup-user-web/src/main/java/com/spup/user/dto/AppAppointmentItemOrderRequest.java"
            print_success "Removed spup-user-web/src/main/java/com/spup/user/dto/AppAppointmentItemOrderRequest.java"
        fi
        
        print_status "Final compilation check..."
        if mvn compile -q; then
            print_success "✅ Schema fixes and DTO consolidation completed successfully!"
            print_status "Summary:"
            print_status "- Fixed schema descriptions in 5+ files"
            print_status "- Consolidated AppAppointmentItemOrderRequest to spup-core"
            print_status "- Removed 2 duplicate DTO files"
            print_status "- All modules compile successfully"
        else
            print_error "❌ Compilation failed after removing duplicates"
            exit 1
        fi
    fi
else
    print_error "❌ Compilation failed after schema fixes"
    exit 1
fi

print_status "Schema fixes and DTO consolidation script completed."
