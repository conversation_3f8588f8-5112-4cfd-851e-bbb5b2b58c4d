#!/bin/bash

# Script to consolidate duplicate response DTO classes

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Get the script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

cd "$PROJECT_ROOT"

print_status "Starting Response DTO consolidation..."

# Function to consolidate a specific response DTO
consolidate_response_dto() {
    local dto_name="$1"
    local description="$2"
    
    print_status "Consolidating $dto_name..."
    
    # Update user module imports
    find spup-user-web/src -name "*.java" -type f -exec grep -l "com.spup.user.dto.$dto_name" {} \; | while read file; do
        print_status "Updating import in: $file"
        sed -i '' "s/import com\.spup\.user\.dto\.$dto_name;/import com.spup.core.dto.$dto_name;/g" "$file"
    done
    
    # Update admin module imports
    find spup-admin-web/src -name "*.java" -type f -exec grep -l "com.spup.admin.dto.$dto_name" {} \; | while read file; do
        print_status "Updating import in: $file"
        sed -i '' "s/import com\.spup\.admin\.dto\.$dto_name;/import com.spup.core.dto.$dto_name;/g" "$file"
    done
    
    print_status "Testing compilation after $dto_name import updates..."
    if mvn compile -q; then
        print_success "✅ Compilation successful for $dto_name!"
        
        # Remove duplicates
        if [ -f "spup-user-web/src/main/java/com/spup/user/dto/$dto_name.java" ]; then
            rm "spup-user-web/src/main/java/com/spup/user/dto/$dto_name.java"
            print_success "Removed user $dto_name"
        fi
        
        if [ -f "spup-admin-web/src/main/java/com/spup/admin/dto/$dto_name.java" ]; then
            rm "spup-admin-web/src/main/java/com/spup/admin/dto/$dto_name.java"
            print_success "Removed admin $dto_name"
        fi
        
        print_status "Final compilation test for $dto_name..."
        if mvn compile -q; then
            print_success "✅ $dto_name consolidation completed successfully!"
            return 0
        else
            print_warning "❌ Compilation failed after removing $dto_name duplicates"
            return 1
        fi
    else
        print_warning "❌ Compilation failed after $dto_name import updates"
        return 1
    fi
}

# Create unified response DTOs first
print_status "Creating unified response DTOs in spup-core..."

# 1. AppAppointmentOrderResponse
cat > spup-core/src/main/java/com/spup/core/dto/AppAppointmentOrderResponse.java << 'EOF'
package com.spup.core.dto;

import com.spup.data.entity.appointment.AppAppointmentOrder;
import com.spup.data.entity.appointment.AppAppointmentSuborder;
import com.spup.data.entity.appointment.AppTemporaryExhibition;

import java.util.List;

/**
 * Unified Appointment Order Response DTO
 * Consolidates the duplicate AppAppointmentOrderResponse classes from admin and user modules
 */
public class AppAppointmentOrderResponse extends AppAppointmentOrder {
    private AppTemporaryExhibition exhibition;
    private List<AppAppointmentSuborder> suborders;

    public List<AppAppointmentSuborder> getSuborders() {
        return suborders;
    }

    public void setSuborders(List<AppAppointmentSuborder> suborders) {
        this.suborders = suborders;
    }

    public AppTemporaryExhibition getExhibition() {
        return exhibition;
    }

    public void setExhibition(AppTemporaryExhibition exhibition) {
        this.exhibition = exhibition;
    }
}
EOF

# 2. AppAppointmentItemOrderResponse
cat > spup-core/src/main/java/com/spup/core/dto/AppAppointmentItemOrderResponse.java << 'EOF'
package com.spup.core.dto;

import com.spup.data.entity.appointment.AppAppointmentItemSuborder;
import com.spup.data.entity.appointment.AppAppointmentOrder;

import java.util.List;

/**
 * Unified Appointment Item Order Response DTO
 * Consolidates the duplicate AppAppointmentItemOrderResponse classes from admin and user modules
 */
public class AppAppointmentItemOrderResponse extends AppAppointmentOrder {
    private List<AppAppointmentItemSuborder> suborders;

    public List<AppAppointmentItemSuborder> getSuborders() {
        return suborders;
    }

    public void setSuborders(List<AppAppointmentItemSuborder> suborders) {
        this.suborders = suborders;
    }
}
EOF

# 3. AppAppointmentOrderTemporaryExhibitionResponse
cat > spup-core/src/main/java/com/spup/core/dto/AppAppointmentOrderTemporaryExhibitionResponse.java << 'EOF'
package com.spup.core.dto;

import com.spup.data.entity.appointment.AppAppointmentOrder;
import com.spup.data.entity.appointment.AppAppointmentSuborder;
import com.spup.data.entity.appointment.AppTemporaryExhibition;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * Unified Appointment Order Temporary Exhibition Response DTO
 * Consolidates the duplicate AppAppointmentOrderTemporaryExhibitionResponse classes from admin and user modules
 */
@Schema(description = "临展预约响应对象")
public class AppAppointmentOrderTemporaryExhibitionResponse extends AppAppointmentOrder {
    private AppTemporaryExhibition exhibition;
    private List<AppAppointmentSuborder> suborders;

    public AppTemporaryExhibition getExhibition() {
        return exhibition;
    }

    public void setExhibition(AppTemporaryExhibition exhibition) {
        this.exhibition = exhibition;
    }

    public List<AppAppointmentSuborder> getSuborders() {
        return suborders;
    }

    public void setSuborders(List<AppAppointmentSuborder> suborders) {
        this.suborders = suborders;
    }
}
EOF

# 4. AppAppointmentTeamOrderResponse
cat > spup-core/src/main/java/com/spup/core/dto/AppAppointmentTeamOrderResponse.java << 'EOF'
package com.spup.core.dto;

import com.spup.data.entity.appointment.AppAppointmentOrder;
import com.spup.data.entity.appointment.AppAppointmentSuborder;

import java.util.List;

/**
 * Unified Appointment Team Order Response DTO
 * Consolidates the duplicate AppAppointmentTeamOrderResponse classes from admin and user modules
 */
public class AppAppointmentTeamOrderResponse extends AppAppointmentOrder {
    private List<AppAppointmentSuborder> suborders;

    public List<AppAppointmentSuborder> getSuborders() {
        return suborders;
    }

    public void setSuborders(List<AppAppointmentSuborder> suborders) {
        this.suborders = suborders;
    }
}
EOF

print_success "Created unified response DTOs in spup-core"

# Test compilation with new DTOs
print_status "Testing compilation with new unified DTOs..."
if mvn compile -q; then
    print_success "✅ All unified DTOs compile successfully!"
    
    # Now consolidate each DTO
    consolidate_response_dto "AppAppointmentOrderResponse" "Appointment Order Response"
    consolidate_response_dto "AppAppointmentItemOrderResponse" "Appointment Item Order Response"
    consolidate_response_dto "AppAppointmentOrderTemporaryExhibitionResponse" "Appointment Order Temporary Exhibition Response"
    consolidate_response_dto "AppAppointmentTeamOrderResponse" "Appointment Team Order Response"
    
    print_success "✅ All Response DTO consolidations completed successfully!"
    print_status "Summary:"
    print_status "- Consolidated 4 response DTO types"
    print_status "- Created 4 unified DTOs in spup-core"
    print_status "- Removed 8 duplicate files"
    print_status "- Updated all imports across modules"
    print_status "- All modules compile successfully"
    
else
    print_warning "❌ Compilation failed with new unified DTOs"
    exit 1
fi

print_success "Response DTO consolidation script completed!"
