#!/bin/bash

# <PERSON><PERSON>t to update all POM versions to 1.0.0

set -e

echo "🔄 Updating POM versions to 1.0.0..."

# Update parent version references in all child modules
find . -name "pom.xml" -not -path "./pom.xml" -exec sed -i '' 's/<version>0\.2<\/version>/<version>1.0.0<\/version>/g' {} \;
find . -name "pom.xml" -not -path "./pom.xml" -exec sed -i '' 's/<version>0\.3<\/version>/<version>1.0.0<\/version>/g' {} \;
find . -name "pom.xml" -not -path "./pom.xml" -exec sed -i '' 's/<version>1\.1<\/version>/<version>1.0.0<\/version>/g' {} \;

echo "✅ POM versions updated to 1.0.0!"
