#!/bin/bash

# Development Build Script
# This script provides fast development builds without full installation

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get the script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

cd "$PROJECT_ROOT"

# Parse command line arguments
QUICK_MODE=false
SKIP_TESTS=false
MODULE=""
CLEAN=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -q|--quick)
            QUICK_MODE=true
            shift
            ;;
        -s|--skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        -m|--module)
            MODULE="$2"
            shift 2
            ;;
        -c|--clean)
            CLEAN=true
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  -q, --quick       Quick build (compile only, no package)"
            echo "  -s, --skip-tests  Skip running tests"
            echo "  -m, --module      Build specific module and its dependencies"
            echo "  -c, --clean       Clean before build"
            echo "  -h, --help        Show this help message"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Build command construction
BUILD_CMD="mvn"

if [ "$CLEAN" = true ]; then
    BUILD_CMD="$BUILD_CMD clean"
fi

if [ "$QUICK_MODE" = true ]; then
    BUILD_CMD="$BUILD_CMD compile"
    print_status "Running quick build (compile only)..."
else
    BUILD_CMD="$BUILD_CMD install"
    print_status "Running full build with install..."
fi

if [ "$SKIP_TESTS" = true ]; then
    BUILD_CMD="$BUILD_CMD -DskipTests"
    print_warning "Skipping tests"
fi

# Add quiet mode for cleaner output
BUILD_CMD="$BUILD_CMD -q"

# Module-specific build
if [ -n "$MODULE" ]; then
    print_status "Building module: $MODULE and its dependencies..."
    BUILD_CMD="$BUILD_CMD -pl $MODULE -am"
fi

# Execute build
print_status "Executing: $BUILD_CMD"
echo "----------------------------------------"

if eval $BUILD_CMD; then
    print_success "Build completed successfully!"
    
    if [ "$QUICK_MODE" = true ]; then
        print_warning "Note: Quick build completed. Run full build before deployment."
    fi
else
    print_error "Build failed!"
    exit 1
fi

echo "----------------------------------------"
print_status "Build finished at $(date)"
