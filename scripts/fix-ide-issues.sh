#!/bin/bash

# Script to fix common IDE issues with multi-module Maven projects

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Get the script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

cd "$PROJECT_ROOT"

print_status "Fixing IDE issues for multi-module Maven project..."

# 1. Clean Maven build
print_status "Cleaning Maven build..."
mvn clean -q

# 2. Force update dependencies
print_status "Updating Maven dependencies..."
mvn dependency:resolve -U -q

# 3. Compile all modules
print_status "Compiling all modules..."
mvn compile -q

# 4. Generate IDE files
print_status "Generating IDE configuration files..."

# For IntelliJ IDEA
if [ -d ".idea" ]; then
    print_status "Detected IntelliJ IDEA project"
    mvn idea:idea -q
    print_warning "Please restart IntelliJ IDEA and use 'File → Invalidate Caches and Restart'"
fi

# For Eclipse
if [ -f ".project" ]; then
    print_status "Detected Eclipse project"
    mvn eclipse:eclipse -q
    print_warning "Please refresh all projects in Eclipse (F5)"
fi

# For VS Code
if [ -d ".vscode" ]; then
    print_status "Detected VS Code project"
    print_warning "Please run 'Java: Clean Workspace' and 'Java: Rebuild Projects' in VS Code"
fi

# 5. Install all modules to local repository
print_status "Installing modules to local Maven repository..."
mvn install -DskipTests -q

print_success "✅ IDE issue fixes completed!"
print_status "Next steps:"
echo "  1. Restart your IDE"
echo "  2. Refresh/reimport Maven projects"
echo "  3. If using IntelliJ: File → Invalidate Caches and Restart"
echo "  4. If using VS Code: Cmd+Shift+P → 'Java: Clean Workspace'"
echo "  5. If using Eclipse: Project → Clean → Clean all projects"

print_status "If issues persist, check:"
echo "  - Maven settings in IDE point to correct Maven installation"
echo "  - Java SDK version matches project requirements"
echo "  - All modules are properly imported as Maven projects"
