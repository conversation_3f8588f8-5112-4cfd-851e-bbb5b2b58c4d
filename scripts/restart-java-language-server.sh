#!/bin/bash

# Script to restart Java Language Server and apply new settings

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Get the script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

cd "$PROJECT_ROOT"

print_status "Restarting Java Language Server with new configuration..."

# 1. Clean Maven build first
print_status "Cleaning Maven build..."
mvn clean -q

# 2. Install all modules to ensure dependencies are available
print_status "Installing all modules..."
mvn install -DskipTests -q

# 3. Check if VS Code is running
if pgrep -f "Visual Studio Code" > /dev/null; then
    print_warning "VS Code is currently running. Please follow these steps:"
    echo ""
    echo "1. Open Command Palette (Cmd+Shift+P)"
    echo "2. Run: 'Java: Clean Workspace'"
    echo "3. Run: 'Java: Restart Projects'"
    echo "4. Run: 'Developer: Reload Window'"
    echo ""
    echo "Or close VS Code and run this script again."
    echo ""
    
    read -p "Press Enter after completing the above steps, or 'q' to quit: " response
    if [ "$response" = "q" ]; then
        exit 0
    fi
else
    print_status "VS Code is not running. You can start it now."
fi

# 4. Verify the configuration
print_status "Verifying Java Language Server configuration..."

if [ -f ".vscode/settings.json" ]; then
    print_success "✅ VS Code settings.json found and updated"
    
    # Check for key settings
    if grep -q "java.jdt.ls.vmargs" .vscode/settings.json; then
        print_success "✅ Java Language Server VM arguments configured"
    fi
    
    if grep -q "java.configuration.updateBuildConfiguration.*automatic" .vscode/settings.json; then
        print_success "✅ Automatic build configuration update enabled"
    fi
    
    if grep -q "java.autobuild.enabled.*true" .vscode/settings.json; then
        print_success "✅ Auto-build enabled"
    fi
    
    if grep -q "java.maven.updateSnapshots.*true" .vscode/settings.json; then
        print_success "✅ Maven snapshot updates enabled"
    fi
else
    print_warning "⚠️  VS Code settings.json not found"
fi

print_success "✅ Java Language Server restart process completed!"

print_status "Key improvements in the new configuration:"
echo "  • Increased memory allocation (2GB) for better performance"
echo "  • Automatic build configuration updates"
echo "  • Maven snapshot updates enabled"
echo "  • Proper source path configuration for all modules"
echo "  • Error reporting improvements"
echo "  • Metadata file management"

print_status "If you still see phantom errors:"
echo "  1. Wait for the language server to fully initialize (check bottom status bar)"
echo "  2. Try opening a problematic file - errors should disappear"
echo "  3. If issues persist, run: Cmd+Shift+P → 'Java: Restart Projects'"

print_status "Monitor the Java Language Server status in VS Code's bottom status bar."
print_status "The server should show 'Ready' when fully initialized."
