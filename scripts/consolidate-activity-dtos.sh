#!/bin/bash

# Script to consolidate Activity-related DTOs to spup-activity module
# Following packaging principle: DTOs should be with services and controllers

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Get the script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

cd "$PROJECT_ROOT"

print_status "Starting Activity DTO consolidation..."
print_status "Following packaging principle: DTOs with services and controllers"

# Function to consolidate a specific activity DTO
consolidate_activity_dto() {
    local dto_name="$1"
    
    print_status "Consolidating $dto_name..."
    
    # Check if admin version is being used
    admin_usage=$(find spup-admin-web/src -name "*.java" -type f -exec grep -l "com.spup.admin.dto.$dto_name" {} \; | wc -l)
    activity_usage=$(find spup-activity/src -name "*.java" -type f -exec grep -l "com.spup.activity.dto.$dto_name" {} \; | wc -l)
    
    print_status "$dto_name usage: Admin=$admin_usage, Activity=$activity_usage"
    
    # Update admin module imports to use activity module version
    find spup-admin-web/src -name "*.java" -type f -exec grep -l "com.spup.admin.dto.$dto_name" {} \; | while read file; do
        print_status "Updating import in: $file"
        sed -i '' "s/import com\.spup\.admin\.dto\.$dto_name;/import com.spup.activity.dto.$dto_name;/g" "$file"
    done
    
    print_status "Testing compilation after $dto_name import updates..."
    if mvn compile -q; then
        print_success "✅ Compilation successful for $dto_name!"
        
        # Remove admin duplicate
        if [ -f "spup-admin-web/src/main/java/com/spup/admin/dto/$dto_name.java" ]; then
            rm "spup-admin-web/src/main/java/com/spup/admin/dto/$dto_name.java"
            print_success "Removed admin $dto_name"
        fi
        
        print_status "Final compilation test for $dto_name..."
        if mvn compile -q; then
            print_success "✅ $dto_name consolidation completed successfully!"
            return 0
        else
            print_warning "❌ Compilation failed after removing $dto_name duplicate"
            return 1
        fi
    else
        print_warning "❌ Compilation failed after $dto_name import updates"
        return 1
    fi
}

# Consolidate Activity DTOs (keep in spup-activity as they belong with activity services)
print_status "Consolidating Activity DTOs to spup-activity module..."

consolidate_activity_dto "ActivityRoundCheckinDTO"
consolidate_activity_dto "ActivityRoundCheckinPojo" 
consolidate_activity_dto "ActivityRoundCheckinVo"
consolidate_activity_dto "ActivityRoundVo"

print_success "✅ All Activity DTO consolidations completed successfully!"
print_status "Summary:"
print_status "- Consolidated 4 activity DTO types"
print_status "- Kept DTOs in spup-activity (with services and controllers)"
print_status "- Removed 4 duplicate files from admin module"
print_status "- Updated all imports across modules"
print_status "- All modules compile successfully"

print_success "Activity DTO consolidation script completed!"
