#!/bin/bash

# Script to rename packages in Phase 4: spup-common and spup-activity from com.huangdou.* to com.spup.*

set -e

echo "🔄 Starting Phase 4 package renaming..."

# Define the module paths
COMMON_PATH="./spup-common/src/main/java"
ACTIVITY_PATH="./spup-activity/src/main/java"

echo "📦 Processing spup-common module..."

# Create new directory structure for spup-common
mkdir -p "$COMMON_PATH/com/spup"

# Move huangdou packages to spup in common module
if [ -d "$COMMON_PATH/com/huangdou/commons" ]; then
    echo "Moving commons packages..."
    mv "$COMMON_PATH/com/huangdou/commons" "$COMMON_PATH/com/spup/"
fi

if [ -d "$COMMON_PATH/com/huangdou/exception" ]; then
    echo "Moving exception package..."
    mv "$COMMON_PATH/com/huangdou/exception" "$COMMON_PATH/com/spup/"
fi

# Move any remaining files in huangdou root
if [ -d "$COMMON_PATH/com/huangdou" ]; then
    find "$COMMON_PATH/com/huangdou" -maxdepth 1 -name "*.java" -exec mv {} "$COMMON_PATH/com/spup/" \;
fi

echo "📦 Processing spup-activity module..."

# Create new directory structure for spup-activity
mkdir -p "$ACTIVITY_PATH/com/spup"

# Move huangdou packages to spup in activity module
if [ -d "$ACTIVITY_PATH/com/huangdou/activity" ]; then
    echo "Moving activity packages..."
    mv "$ACTIVITY_PATH/com/huangdou/activity" "$ACTIVITY_PATH/com/spup/"
fi

# Move any remaining files in huangdou root
if [ -d "$ACTIVITY_PATH/com/huangdou" ]; then
    find "$ACTIVITY_PATH/com/huangdou" -maxdepth 1 -name "*.java" -exec mv {} "$ACTIVITY_PATH/com/spup/" \;
fi

echo "✅ Directory structure updated!"

# Update package declarations in spup-common
echo "🔧 Updating package declarations in spup-common..."
find "$COMMON_PATH/com/spup" -name "*.java" -type f -exec sed -i '' 's/^package com\.huangdou\./package com.spup./g' {} \;

# Update package declarations in spup-activity  
echo "🔧 Updating package declarations in spup-activity..."
find "$ACTIVITY_PATH/com/spup" -name "*.java" -type f -exec sed -i '' 's/^package com\.huangdou\./package com.spup./g' {} \;

echo "✅ Package declarations updated!"

echo "🎉 Phase 4 package renaming completed!"
