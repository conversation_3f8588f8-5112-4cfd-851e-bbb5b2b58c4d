#!/bin/bash

# Auto-Sync Script for Remote Hot Reload
# This script automatically syncs compiled classes to remote server when files change

echo "🔄 Auto-Sync for Remote Hot Reload"
echo "=================================="

# Configuration
REMOTE_HOST="supec.douwifi.cn"
REMOTE_USER="your-username"  # Replace with actual username
REMOTE_PATH="/path/to/remote/classes"  # Replace with actual remote path
LOCAL_CLASSES="app-spup-admin/target/classes"
WATCH_PATH="app-spup-admin/src/main/java"

echo ""
echo "📋 Configuration:"
echo "================="
echo "Remote Host: $REMOTE_HOST"
echo "Remote User: $REMOTE_USER"
echo "Remote Path: $REMOTE_PATH"
echo "Local Classes: $LOCAL_CLASSES"
echo "Watch Path: $WATCH_PATH"

echo ""
echo "🛠️ Prerequisites Check:"
echo "======================="

# Check if fswatch is installed (macOS)
if command -v fswatch >/dev/null 2>&1; then
    echo "✅ fswatch is installed"
    WATCHER="fswatch"
elif command -v inotifywait >/dev/null 2>&1; then
    echo "✅ inotifywait is installed"
    WATCHER="inotifywait"
else
    echo "❌ No file watcher found. Please install:"
    echo "   macOS: brew install fswatch"
    echo "   Linux: sudo apt-get install inotify-tools"
    exit 1
fi

# Check if rsync is available
if command -v rsync >/dev/null 2>&1; then
    echo "✅ rsync is available"
else
    echo "❌ rsync not found. Please install rsync"
    exit 1
fi

echo ""
echo "🔧 Setup Instructions:"
echo "======================"

echo ""
echo "1️⃣ **Configure SSH Key Authentication:**"
echo "ssh-keygen -t rsa -b 4096 -C '<EMAIL>'"
echo "ssh-copy-id $REMOTE_USER@$REMOTE_HOST"
echo "# Test: ssh $REMOTE_USER@$REMOTE_HOST 'echo Connected successfully'"

echo ""
echo "2️⃣ **Find Remote Application Path:**"
echo "ssh $REMOTE_USER@$REMOTE_HOST"
echo "# On remote server, find where your app is deployed:"
echo "ps aux | grep java | grep spup-admin"
echo "# Look for -cp or -classpath to find the classes directory"

echo ""
echo "3️⃣ **Update Configuration in this script:**"
echo "# Edit the variables at the top of this script:"
echo "REMOTE_USER=\"your-actual-username\""
echo "REMOTE_PATH=\"/actual/path/to/classes\""

echo ""
echo "🚀 Auto-Sync Functions:"
echo "======================"

# Function to compile and sync
compile_and_sync() {
    echo "📁 File changed detected, compiling..."
    
    # Compile the project
    if mvn compile -q -f app-spup-admin/pom.xml; then
        echo "✅ Compilation successful"
        
        # Sync classes to remote server
        echo "🔄 Syncing classes to remote server..."
        if rsync -avz --delete $LOCAL_CLASSES/ $REMOTE_USER@$REMOTE_HOST:$REMOTE_PATH/; then
            echo "✅ Sync successful at $(date)"
        else
            echo "❌ Sync failed"
        fi
    else
        echo "❌ Compilation failed"
    fi
    echo ""
}

# Function to start watching with fswatch (macOS)
start_fswatch() {
    echo "🔍 Starting file watcher with fswatch..."
    echo "Watching: $WATCH_PATH"
    echo "Press Ctrl+C to stop"
    echo ""
    
    fswatch -o $WATCH_PATH | while read f; do
        compile_and_sync
    done
}

# Function to start watching with inotifywait (Linux)
start_inotifywait() {
    echo "🔍 Starting file watcher with inotifywait..."
    echo "Watching: $WATCH_PATH"
    echo "Press Ctrl+C to stop"
    echo ""
    
    while inotifywait -r -e modify,create,delete $WATCH_PATH; do
        compile_and_sync
    done
}

echo ""
echo "🎯 Usage Options:"
echo "================="

case "$1" in
    "start")
        echo "Starting auto-sync..."
        if [ "$WATCHER" = "fswatch" ]; then
            start_fswatch
        else
            start_inotifywait
        fi
        ;;
    "sync-once")
        echo "Performing one-time sync..."
        compile_and_sync
        ;;
    "test")
        echo "Testing connection and paths..."
        echo "Testing SSH connection..."
        if ssh $REMOTE_USER@$REMOTE_HOST 'echo "SSH connection successful"'; then
            echo "✅ SSH connection works"
        else
            echo "❌ SSH connection failed"
            exit 1
        fi
        
        echo "Testing remote path..."
        if ssh $REMOTE_USER@$REMOTE_HOST "test -d $REMOTE_PATH"; then
            echo "✅ Remote path exists: $REMOTE_PATH"
        else
            echo "❌ Remote path does not exist: $REMOTE_PATH"
            echo "Please create it or update REMOTE_PATH variable"
        fi
        ;;
    *)
        echo ""
        echo "📖 Usage:"
        echo "========="
        echo "$0 start      - Start auto-sync file watcher"
        echo "$0 sync-once  - Perform one-time compile and sync"
        echo "$0 test       - Test SSH connection and remote paths"
        echo ""
        echo "📝 Example Workflow:"
        echo "==================="
        echo "1. Configure SSH keys and remote paths"
        echo "2. Run: $0 test"
        echo "3. Run: $0 start"
        echo "4. In another terminal, start VS Code remote debugging"
        echo "5. Make code changes - they'll auto-sync to remote server"
        echo "6. Hot reload will apply changes during debugging"
        echo ""
        echo "🔥 Hot Reload Workflow:"
        echo "======================"
        echo "1. Start this auto-sync script: $0 start"
        echo "2. Start remote debugging in VS Code"
        echo "3. Set breakpoint in your code"
        echo "4. Trigger the breakpoint with a request"
        echo "5. While paused, modify the method code"
        echo "6. Save file (auto-sync will compile and upload)"
        echo "7. Continue debugging - changes are applied!"
        ;;
esac
