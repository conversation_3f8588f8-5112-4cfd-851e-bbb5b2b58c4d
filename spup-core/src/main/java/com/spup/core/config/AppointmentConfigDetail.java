package com.spup.core.config;

import org.springframework.format.annotation.DateTimeFormat;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Appointment Configuration Detail
 * Unified configuration detail class that replaces module-specific AppointmentConfigDetail classes
 * This consolidates AppointmentConfigDetail from admin and user modules
 */
public class AppointmentConfigDetail {
    
    private List<DayOfWeek> weekdayList;
    
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private List<Date> holidayList;

    public List<DayOfWeek> getWeekdayList() {
        return weekdayList;
    }

    public void setWeekdayList(List<DayOfWeek> weekdayList) {
        this.weekdayList = weekdayList;
    }

    /**
     * Get holiday list as LocalDate objects
     */
    public List<LocalDate> getHolidayList() {
        if (holidayList == null) {
            return new ArrayList<>();
        }
        
        List<LocalDate> holidays = new ArrayList<>();
        for (Date date : holidayList) {
            holidays.add(date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
        }
        return holidays;
    }

    /**
     * Set holiday list as Date objects
     */
    public void setHolidayList(List<Date> holidayList) {
        this.holidayList = holidayList;
    }

    /**
     * Get raw holiday list as Date objects
     */
    public List<Date> getHolidayListAsDate() {
        return holidayList;
    }

    /**
     * Check if a specific day of week is configured as working day
     */
    public boolean isWorkingDay(DayOfWeek dayOfWeek) {
        return weekdayList != null && weekdayList.contains(dayOfWeek);
    }

    /**
     * Check if a specific date is a holiday
     */
    public boolean isHoliday(LocalDate date) {
        List<LocalDate> holidays = getHolidayList();
        return holidays.contains(date);
    }
}
