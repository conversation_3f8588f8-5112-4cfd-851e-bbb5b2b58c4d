package com.spup.core.config;

import com.spup.commons.api.CommonResult;
import com.spup.commons.api.ResultCodeEnum;
import com.spup.core.exception.BaseValidException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * Base Exception Handler
 * Provides common exception handling that can be extended by specific modules
 */
@Slf4j
@RestControllerAdvice
public abstract class BaseExceptionHandler {

    /**
     * Handle validation exceptions from @Valid annotations
     */
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public CommonResult<?> handleValidationExceptions(MethodArgumentNotValidException e, HttpServletRequest request) {
        try {
            log.error("=== VALIDATION EXCEPTION ===");
            log.error("Request URL: {}", request.getRequestURL());
            log.error("Request Method: {}", request.getMethod());
            
            BindingResult bindingResult = e.getBindingResult();
            StringBuilder stringBuffer = new StringBuilder();
            
            for (FieldError fieldError : bindingResult.getFieldErrors()) {
                stringBuffer.append(fieldError.getField())
                           .append(":")
                           .append(fieldError.getDefaultMessage())
                           .append(", ");
            }
            
            String errorMessage = stringBuffer.toString();
            log.error("Validation errors: {}", errorMessage);
            
            return CommonResult.failed(errorMessage);
        } catch (Exception handlerException) {
            log.error("Exception in validation handler:", handlerException);
            return createSafeErrorResponse("Validation failed");
        }
    }

    /**
     * Handle custom validation exceptions
     */
    @ExceptionHandler(value = BaseValidException.class)
    public CommonResult<?> handleBaseValidException(BaseValidException e, HttpServletRequest request) {
        try {
            log.error("=== BASE VALIDATION EXCEPTION ===");
            log.error("Request URL: {}", request.getRequestURL());
            log.error("Request Method: {}", request.getMethod());
            log.error("Validation error:", e);
            
            return CommonResult.failed(e.getMessage());
        } catch (Exception handlerException) {
            log.error("Exception in base validation handler:", handlerException);
            return createSafeErrorResponse("Validation failed");
        }
    }

    /**
     * Handle illegal argument exceptions
     */
    @ExceptionHandler(value = IllegalArgumentException.class)
    public CommonResult<?> handleIllegalArgumentExceptions(IllegalArgumentException e, 
                                                           HttpServletRequest request, 
                                                           HttpServletResponse response) {
        try {
            log.error("=== ILLEGAL ARGUMENT EXCEPTION ===");
            log.error("Request URL: {}", request.getRequestURL());
            log.error("Request Method: {}", request.getMethod());
            log.error("Illegal argument:", e);

            // Check if response is already committed
            if (response.isCommitted()) {
                log.warn("Response already committed, cannot handle illegal argument exception: {}", e.getMessage());
                return null;
            }

            return CommonResult.failed(ResultCodeEnum.VALIDATION_FAILED);
        } catch (Exception handlerException) {
            log.error("Exception in illegal argument handler:", handlerException);
            return createSafeErrorResponse("Invalid arguments");
        }
    }

    /**
     * Handle general exceptions
     */
    @ExceptionHandler(value = Exception.class)
    public CommonResult<?> handleGeneralExceptions(Exception e, 
                                                   HttpServletRequest request, 
                                                   HttpServletResponse response) {
        try {
            log.error("=== GENERAL EXCEPTION: {} ===", e.getClass().getSimpleName());
            log.error("Request URL: {}", request.getRequestURL());
            log.error("Request Method: {}", request.getMethod());
            log.error("Exception message: {}", e.getMessage());
            log.error("Full stack trace:", e);

            // Log the cause chain
            logCauseChain(e);

            // Check if response is already committed
            if (response.isCommitted()) {
                log.warn("Response already committed, cannot handle exception: {}", e.getMessage());
                return null;
            }

            return handleModuleSpecificException(e, request, response);
        } catch (Exception handlerException) {
            log.error("Exception in general exception handler:", handlerException);
            return createSafeErrorResponse("System error");
        }
    }

    /**
     * Handle throwables (most general handler)
     */
    @ExceptionHandler(value = Throwable.class)
    public CommonResult<?> handleThrowables(Throwable throwable, 
                                           HttpServletRequest request, 
                                           HttpServletResponse response) {
        try {
            log.error("=== THROWABLE: {} ===", throwable.getClass().getSimpleName());
            log.error("Request URL: {}", request.getRequestURL());
            log.error("Request Method: {}", request.getMethod());
            log.error("System error - Full details:", throwable);

            // Check if response is already committed
            if (response.isCommitted()) {
                log.warn("Response already committed, cannot handle throwable: {}", throwable.getMessage());
                return null;
            }

            return CommonResult.failed("System error");
        } catch (Exception handlerException) {
            log.error("Exception in throwable handler:", handlerException);
            return createSafeErrorResponse("System error");
        }
    }

    /**
     * Log the exception cause chain
     */
    protected void logCauseChain(Throwable throwable) {
        Throwable cause = throwable.getCause();
        int level = 1;
        while (cause != null && level <= 5) {
            log.error("Caused by (level {}): {} - {}", level, cause.getClass().getSimpleName(), cause.getMessage());
            cause = cause.getCause();
            level++;
        }
    }

    /**
     * Create a safe error response that won't throw exceptions
     */
    protected CommonResult<?> createSafeErrorResponse(String message) {
        try {
            return CommonResult.failed(message);
        } catch (Exception e) {
            log.error("Failed to create error response:", e);
            // Return a minimal response using the public failed method
            return CommonResult.failed("System error");
        }
    }

    /**
     * Allow subclasses to handle module-specific exceptions
     */
    protected abstract CommonResult<?> handleModuleSpecificException(Exception e, 
                                                                     HttpServletRequest request, 
                                                                     HttpServletResponse response);
}
