package com.spup.core.config;

import org.springframework.context.annotation.Bean;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;

/**
 * Base OpenAPI Configuration
 * Provides common OpenAPI configuration that can be extended by modules
 */
public abstract class BaseOpenApiConfig {

    /**
     * Dynamically detect the current server URL from the request context
     * This automatically infers the correct protocol, host, port, and context path
     */
    protected String getCurrentServerUrl() {
        try {
            org.springframework.web.context.request.ServletRequestAttributes attr =
                (org.springframework.web.context.request.ServletRequestAttributes)
                org.springframework.web.context.request.RequestContextHolder.currentRequestAttributes();

            javax.servlet.http.HttpServletRequest request = attr.getRequest();

            String scheme = request.getScheme(); // http or https
            String serverName = request.getServerName(); // hostname
            int serverPort = request.getServerPort(); // port
            String contextPath = request.getContextPath(); // context path

            // Build the base URL
            StringBuilder url = new StringBuilder();
            url.append(scheme).append("://").append(serverName);

            // Add port if it's not the default port for the scheme
            if ((scheme.equals("http") && serverPort != 80) ||
                (scheme.equals("https") && serverPort != 443)) {
                url.append(":").append(serverPort);
            }

            url.append(contextPath);

            return url.toString();
        } catch (Exception e) {
            // Fallback if no request context is available
            System.out.println("DEBUG: getCurrentServerUrl() exception: " + e.getClass().getSimpleName() + " - " + e.getMessage());
            System.out.println("DEBUG: This usually happens during Spring startup when no request context exists");
            return "http://localhost:8080";
        }
    }

    /**
     * Get alternative server URLs for different environments
     */
    protected String getAlternativeServerUrl() {
        String currentUrl = getCurrentServerUrl();

        // If current is HTTPS, provide HTTP alternative for development
        if (currentUrl.startsWith("https://")) {
            return currentUrl.replace("https://", "http://");
        }
        // If current is HTTP, provide HTTPS alternative for production
        else {
            return currentUrl.replace("http://", "https://");
        }
    }

    /**
     * Get the API title for this module
     */
    protected abstract String getApiTitle();
    
    /**
     * Get the API description for this module
     */
    protected abstract String getApiDescription();
    
    /**
     * Get the API version for this module
     */
    protected String getApiVersion() {
        return "2.0";
    }
    
    /**
     * Create OpenAPI configuration
     * 配置API基本信息和JWT认证
     * 访问地址：http://项目实际地址/swagger-ui/index.html
     *
     * JWT Token Usage:
     * 1. Click "Authorize" button in Swagger UI
     * 2. Enter your JWT token directly (no prefix needed)
     * 3. Swagger will include the token directly in Authorization header
     */
    @Bean
    public OpenAPI openAPI() {
        return new OpenAPI()
                .info(apiInfo())
                // Remove static server configuration - let SpringDoc auto-detect
                .addSecurityItem(new SecurityRequirement().addList("JwtAuth"))
                .components(new Components()
                        .addSecuritySchemes("JwtAuth",
                                new SecurityScheme()
                                        .type(SecurityScheme.Type.APIKEY)
                                        .in(SecurityScheme.In.HEADER)
                                        .name("Authorization")
                                        .description("JWT token for API authentication. " +
                                                   "\n\nEnter your JWT token directly in the text input below. " +
                                                   "\n\nExample: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' " +
                                                   "\n\nNote: Enter the token directly without any prefix. " +
                                                   "\n\nFormat: Authorization: your-jwt-token")
                        )
                );
    }

    /**
     * Get server configurations dynamically inferred from current request
     * Automatically detects protocol, host, port, and context path
     */
    protected java.util.List<io.swagger.v3.oas.models.servers.Server> getServers() {
        java.util.List<io.swagger.v3.oas.models.servers.Server> servers = new java.util.ArrayList<>();

        String currentUrl = getCurrentServerUrl();
        String alternativeUrl = getAlternativeServerUrl();

        // Current server (automatically detected from request)
        io.swagger.v3.oas.models.servers.Server currentServer = new io.swagger.v3.oas.models.servers.Server();
        currentServer.setUrl(currentUrl);
        currentServer.setDescription("Current Server (" +
            (currentUrl.startsWith("https") ? "HTTPS" : "HTTP") + ")");
        servers.add(currentServer);

        // Alternative server (opposite protocol)
        if (!currentUrl.equals(alternativeUrl)) {
            io.swagger.v3.oas.models.servers.Server altServer = new io.swagger.v3.oas.models.servers.Server();
            altServer.setUrl(alternativeUrl);
            altServer.setDescription("Alternative Server (" +
                (alternativeUrl.startsWith("https") ? "HTTPS" : "HTTP") + ")");
            servers.add(altServer);
        }

        return servers;
    }

    /**
     * API Information
     * Creates the API info with module-specific details
     */
    private Info apiInfo() {
        return new Info()
                .title(getApiTitle())
                .description(getApiDescription())
                .version(getApiVersion());
    }
}
