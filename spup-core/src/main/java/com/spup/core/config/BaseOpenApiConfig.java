package com.spup.core.config;

import org.springframework.context.annotation.Bean;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;

/**
 * Base OpenAPI Configuration
 * Provides common OpenAPI configuration that can be extended by modules
 */
public abstract class BaseOpenApiConfig {

    @org.springframework.beans.factory.annotation.Value("${app.server.production-url:https://localhost}")
    private String productionUrl;

    @org.springframework.beans.factory.annotation.Value("${app.server.context-path:}")
    private String contextPath;

    /**
     * Get the API title for this module
     */
    protected abstract String getApiTitle();
    
    /**
     * Get the API description for this module
     */
    protected abstract String getApiDescription();
    
    /**
     * Get the API version for this module
     */
    protected String getApiVersion() {
        return "2.0";
    }
    
    /**
     * Create OpenAPI configuration
     * 配置API基本信息和JWT认证
     * 访问地址：http://项目实际地址/swagger-ui/index.html
     *
     * JWT Token Usage:
     * 1. Click "Authorize" button in Swagger UI
     * 2. Enter your JWT token directly (no prefix needed)
     * 3. Swagger will include the token directly in Authorization header
     */
    @Bean
    public OpenAPI openAPI() {
        return new OpenAPI()
                .info(apiInfo())
                .servers(createServerList())
                .addSecurityItem(new SecurityRequirement().addList("JwtAuth"))
                .components(new Components()
                        .addSecuritySchemes("JwtAuth",
                                new SecurityScheme()
                                        .type(SecurityScheme.Type.APIKEY)
                                        .in(SecurityScheme.In.HEADER)
                                        .name("Authorization")
                                        .description("JWT token for API authentication. " +
                                                   "\n\nEnter your JWT token directly in the text input below. " +
                                                   "\n\nExample: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' " +
                                                   "\n\nNote: Enter the token directly without any prefix. " +
                                                   "\n\nFormat: Authorization: your-jwt-token")
                        )
                );
    }

    /**
     * Create server list with production HTTPS and development HTTP options
     */
    private java.util.List<io.swagger.v3.oas.models.servers.Server> createServerList() {
        java.util.List<io.swagger.v3.oas.models.servers.Server> servers = new java.util.ArrayList<>();

        // Production HTTPS server (primary)
        io.swagger.v3.oas.models.servers.Server httpsServer = new io.swagger.v3.oas.models.servers.Server();
        httpsServer.setUrl(productionUrl + contextPath);
        httpsServer.setDescription("Production HTTPS Server");
        servers.add(httpsServer);

        // Development HTTP server (fallback)
        io.swagger.v3.oas.models.servers.Server httpServer = new io.swagger.v3.oas.models.servers.Server();
        httpServer.setUrl("http://localhost:8888" + contextPath);
        httpServer.setDescription("Development HTTP Server");
        servers.add(httpServer);

        return servers;
    }

    /**
     * API Information
     * Creates the API info with module-specific details
     */
    private Info apiInfo() {
        return new Info()
                .title(getApiTitle())
                .description(getApiDescription())
                .version(getApiVersion());
    }
}
