package com.spup.core.config;

import org.springframework.context.annotation.Bean;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;

/**
 * Base OpenAPI Configuration
 * Provides common OpenAPI configuration that can be extended by modules
 */
public abstract class BaseOpenApiConfig {

    @org.springframework.beans.factory.annotation.Value("${swagger.server.url:https://localhost}")
    private String serverUrl;

    @org.springframework.beans.factory.annotation.Value("${swagger.server.description:Production Server}")
    private String serverDescription;

    /**
     * Get the production server URL for this module
     * Configurable through application properties: swagger.server.url
     */
    protected String getProductionServerUrl() {
        return serverUrl;
    }

    /**
     * Get the production server description
     * Configurable through application properties: swagger.server.description
     */
    protected String getProductionServerDescription() {
        return serverDescription;
    }

    /**
     * Get the API title for this module
     */
    protected abstract String getApiTitle();
    
    /**
     * Get the API description for this module
     */
    protected abstract String getApiDescription();
    
    /**
     * Get the API version for this module
     */
    protected String getApiVersion() {
        return "2.0";
    }
    
    /**
     * Create OpenAPI configuration
     * 配置API基本信息和JWT认证
     * 访问地址：http://项目实际地址/swagger-ui/index.html
     *
     * JWT Token Usage:
     * 1. Click "Authorize" button in Swagger UI
     * 2. Enter your JWT token directly (no prefix needed)
     * 3. Swagger will include the token directly in Authorization header
     */
    @Bean
    public OpenAPI openAPI() {
        return new OpenAPI()
                .info(apiInfo())
                .servers(getServers())
                .addSecurityItem(new SecurityRequirement().addList("JwtAuth"))
                .components(new Components()
                        .addSecuritySchemes("JwtAuth",
                                new SecurityScheme()
                                        .type(SecurityScheme.Type.APIKEY)
                                        .in(SecurityScheme.In.HEADER)
                                        .name("Authorization")
                                        .description("JWT token for API authentication. " +
                                                   "\n\nEnter your JWT token directly in the text input below. " +
                                                   "\n\nExample: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' " +
                                                   "\n\nNote: Enter the token directly without any prefix. " +
                                                   "\n\nFormat: Authorization: your-jwt-token")
                        )
                );
    }

    /**
     * Get server configurations for different environments
     * Configures both HTTPS (production) and HTTP (development) servers
     */
    protected java.util.List<io.swagger.v3.oas.models.servers.Server> getServers() {
        java.util.List<io.swagger.v3.oas.models.servers.Server> servers = new java.util.ArrayList<>();

        // Production server (configurable)
        io.swagger.v3.oas.models.servers.Server productionServer = new io.swagger.v3.oas.models.servers.Server();
        productionServer.setUrl(getProductionServerUrl() + getContextPath());
        productionServer.setDescription(getProductionServerDescription());
        servers.add(productionServer);

        // Development HTTP server (fallback)
        io.swagger.v3.oas.models.servers.Server httpServer = new io.swagger.v3.oas.models.servers.Server();
        httpServer.setUrl("http://localhost:" + getLocalPort() + getContextPath());
        httpServer.setDescription("Development HTTP Server");
        servers.add(httpServer);

        return servers;
    }

    /**
     * Get the context path for this module
     * Override in subclasses to provide module-specific context paths
     */
    protected String getContextPath() {
        return "";
    }

    /**
     * Get the local development port
     * Override in subclasses to provide module-specific ports
     */
    protected String getLocalPort() {
        return "8080";
    }

    /**
     * API Information
     * Creates the API info with module-specific details
     */
    private Info apiInfo() {
        return new Info()
                .title(getApiTitle())
                .description(getApiDescription())
                .version(getApiVersion());
    }
}
