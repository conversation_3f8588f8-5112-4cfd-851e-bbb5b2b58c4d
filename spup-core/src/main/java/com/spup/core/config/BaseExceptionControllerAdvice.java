package com.spup.core.config;

import javax.servlet.http.HttpServletRequest;

import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import com.spup.commons.api.CommonResult;
import com.spup.core.exception.ValidException;

import lombok.extern.slf4j.Slf4j;

/**
 * Base Exception Controller Advice
 * Provides common exception handling that can be extended by modules
 */
@Slf4j
@RestControllerAdvice(basePackages = "com.spup")
public abstract class BaseExceptionControllerAdvice {
    
    /**
     * Get the module name for logging purposes
     */
    protected abstract String getModuleName();
    
    /**
     * Handle ValidException - common across all modules
     */
    @ExceptionHandler(value = ValidException.class)
    public CommonResult<?> handleValidException(ValidException e, HttpServletRequest request) {
        try {
            log.error("=== {} VALIDATION EXCEPTION ===", getModuleName().toUpperCase());
            log.error("Request URL: {}", request.getRequestURL());
            log.error("Request Method: {}", request.getMethod());
            log.error("{} validation error:", getModuleName(), e);

            return CommonResult.failed(e.getMessage());
        } catch (Exception handlerException) {
            log.error("Exception in {} validation handler:", getModuleName(), handlerException);
            return createSafeErrorResponse(getModuleName() + " validation failed");
        }
    }
    
    /**
     * Handle general exceptions - can be overridden by modules
     */
    @ExceptionHandler(value = Exception.class)
    public CommonResult<?> handleGeneralException(Exception e, HttpServletRequest request) {
        try {
            // Module-specific exception handling logic
            log.error("{}-specific exception handling for: {}", getModuleName(), e.getClass().getSimpleName());
            
            // Call module-specific handler
            CommonResult<?> moduleResult = handleModuleSpecificException(e);
            if (moduleResult != null) {
                return moduleResult;
            }
            
            // Default error response
            return CommonResult.failed(getModuleName() + " system error");
        } catch (Exception handlerException) {
            log.error("Exception in {} general handler:", getModuleName(), handlerException);
            return createSafeErrorResponse(getModuleName() + " system error");
        }
    }
    
    /**
     * Handle module-specific exceptions - to be implemented by subclasses
     */
    protected CommonResult<?> handleModuleSpecificException(Exception e) {
        // Default implementation - can be overridden
        return null;
    }
    
    /**
     * Create a safe error response when exception handling fails
     */
    protected CommonResult<?> createSafeErrorResponse(String message) {
        try {
            return CommonResult.failed(message);
        } catch (Exception e) {
            log.error("Failed to create error response:", e);
            // Return minimal response to prevent infinite loops
            return CommonResult.failed(message);
        }
    }
}
