package com.spup.core.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Base Appointment Configuration
 * Unified appointment configuration that replaces module-specific AppointmentConfig classes
 * This consolidates AppointmentConfig from admin and user modules
 */
@Component
@ConfigurationProperties(prefix = "appointment")
public class BaseAppointmentConfig {
    
    private Map<String, AppointmentConfigDetail> config;

    /**
     * Get appointment configuration by exhibition ID
     */
    public AppointmentConfigDetail getConfig(String exhibitionId) {
        if (config == null || config.isEmpty()) {
            return null;
        }
        return config.get(exhibitionId);
    }

    /**
     * Check if configuration exists for exhibition ID
     */
    public boolean hasConfig(String exhibitionId) {
        return config != null && config.containsKey(exhibitionId);
    }

    /**
     * Get all configurations
     */
    public Map<String, AppointmentConfigDetail> getAllConfigs() {
        return config;
    }

    public void setConfig(Map<String, AppointmentConfigDetail> config) {
        this.config = config;
    }

    public Map<String, AppointmentConfigDetail> getConfig() {
        return config;
    }
}
