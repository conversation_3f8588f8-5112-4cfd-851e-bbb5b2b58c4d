package com.spup.core.config;

import org.springframework.lang.NonNull;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import com.spup.core.interceptor.BaseTokenInterceptor;

import lombok.extern.slf4j.Slf4j;

/**
 * Base Web Configuration
 * Provides common interceptor setup that can be extended by specific modules
 */
@Slf4j
// @Configuration
public abstract class BaseWebConfig implements WebMvcConfigurer {

    /**
     * Create token interceptor bean
     * Subclasses can override this to provide custom interceptor implementations
     */
    public BaseTokenInterceptor baseTokenInterceptor() {
        return new BaseTokenInterceptor();
    }

    /**
     * Add common interceptors
     * Subclasses should call super.addInterceptors() and then add module-specific patterns
     */
    @Override
    public void addInterceptors(@NonNull InterceptorRegistry registry) {
        log.info("Adding base token interceptor");
        
        // Add base interceptor with common exclusions
        registry.addInterceptor(baseTokenInterceptor())
                .addPathPatterns("/**")
                .excludePathPatterns(getCommonExcludePatterns());
        
        // Allow subclasses to add module-specific interceptor configurations
        addModuleSpecificInterceptors(registry);
    }

    /**
     * Get common exclude patterns that apply to all modules
     */
    protected String[] getCommonExcludePatterns() {
        return new String[]{
            "/*.txt",
            "/swagger*/**", 
            "/webjars/**", 
            "/html/**", 
            "/check/**",
            "/login/**",
            "/actuator/**",
            "/error/**"
        };
    }

    /**
     * Allow subclasses to add module-specific interceptor configurations
     */
    protected abstract void addModuleSpecificInterceptors(InterceptorRegistry registry);

    /**
     * Get module-specific exclude patterns
     * Subclasses should override this to provide their own patterns
     */
    protected abstract String[] getModuleSpecificExcludePatterns();
}