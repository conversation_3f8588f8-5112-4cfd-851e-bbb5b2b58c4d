package com.spup.core.config;

import org.springframework.lang.NonNull;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import com.spup.core.interceptor.BaseTokenInterceptor;

import lombok.extern.slf4j.Slf4j;

/**
 * Base Web Configuration
 * Provides common interceptor setup that can be extended by specific modules
 */
@Slf4j
public abstract class BaseWebConfig implements WebMvcConfigurer {

    /**
     * Create token interceptor bean
     * Subclasses must override this to provide custom interceptor implementations
     */
    public abstract BaseTokenInterceptor baseTokenInterceptor();

    /**
     * Add common interceptors
     * Combines common and module-specific exclusion patterns
     */
    @Override
    public void addInterceptors(@NonNull InterceptorRegistry registry) {
        log.info("Adding base token interceptor");

        // Combine common and module-specific exclusion patterns
        String[] allExcludePatterns = combineExcludePatterns();

        // Add base interceptor with all exclusions
        registry.addInterceptor(baseTokenInterceptor())
                .addPathPatterns("/**")
                .excludePathPatterns(allExcludePatterns);

        // Allow subclasses to add additional interceptors (not the same interceptor)
        addModuleSpecificInterceptors(registry);
    }

    /**
     * Combine common and module-specific exclude patterns
     */
    private String[] combineExcludePatterns() {
        String[] commonPatterns = getCommonExcludePatterns();
        String[] modulePatterns = getModuleSpecificExcludePatterns();

        String[] combined = new String[commonPatterns.length + modulePatterns.length];
        System.arraycopy(commonPatterns, 0, combined, 0, commonPatterns.length);
        System.arraycopy(modulePatterns, 0, combined, commonPatterns.length, modulePatterns.length);

        return combined;
    }

    /**
     * Get common exclude patterns that apply to all modules
     */
    protected String[] getCommonExcludePatterns() {
        return new String[]{
            "/*.txt",
            "/swagger-ui/**",           // SpringDoc OpenAPI 3 UI
            "/swagger-ui.html",         // SpringDoc OpenAPI 3 redirect
            "/v3/api-docs/**",          // SpringDoc OpenAPI 3 spec
            "/webjars/**",              // Swagger UI static resources
            "/html/**",
            "/check/**",
            "/login/**",
            "/actuator/**",
            "/error/**"
        };
    }

    /**
     * Allow subclasses to add module-specific interceptor configurations
     */
    protected abstract void addModuleSpecificInterceptors(InterceptorRegistry registry);

    /**
     * Get module-specific exclude patterns
     * Subclasses should override this to provide their own patterns
     */
    protected abstract String[] getModuleSpecificExcludePatterns();
}