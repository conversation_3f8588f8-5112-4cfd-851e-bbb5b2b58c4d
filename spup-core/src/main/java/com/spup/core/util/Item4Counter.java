package com.spup.core.util;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Item for Counter
 * Common utility class for counting items (moved from duplicated classes)
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Item4Counter {
    
    private String name;
    private Integer count;
    
    /**
     * Increment count by 1
     */
    public void increment() {
        if (count == null) {
            count = 1;
        } else {
            count++;
        }
    }
    
    /**
     * Increment count by specified amount
     */
    public void increment(int amount) {
        if (count == null) {
            count = amount;
        } else {
            count += amount;
        }
    }
    
    /**
     * Reset count to 0
     */
    public void reset() {
        count = 0;
    }
    
    /**
     * Check if count is zero or null
     */
    public boolean isEmpty() {
        return count == null || count == 0;
    }
}
