package com.spup.core.util;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;

/**
 * Common Date/Time Utilities
 * Centralized date/time formatting and manipulation utilities
 */
public class DateTimeUtils {

    // Common date/time formatters
    public static final DateTimeFormatter YYYY_MM_DD = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    public static final DateTimeFormatter YYYY_MM_DD_HH_MM_SS = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    public static final DateTimeFormatter YYYYMMDD = DateTimeFormatter.ofPattern("yyyyMMdd");
    public static final DateTimeFormatter YYYYMMDDHHMMSS = DateTimeFormatter.ofPattern("yyyyMMddhhmmss");
    public static final DateTimeFormatter YYYY = DateTimeFormatter.ofPattern("yyyy");
    public static final DateTimeFormatter MM = DateTimeFormatter.ofPattern("MM");
    public static final DateTimeFormatter DD = DateTimeFormatter.ofPattern("dd");

    /**
     * Format LocalDate to yyyyMMdd string
     */
    public static String formatToYyyyMmDd(LocalDate date) {
        return date != null ? date.format(YYYYMMDD) : null;
    }

    /**
     * Format LocalDateTime to yyyyMMddhhmmss string
     */
    public static String formatToYyyyMmDdHhMmSs(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.format(YYYYMMDDHHMMSS) : null;
    }

    /**
     * Format LocalDate to yyyy-MM-dd string
     */
    public static String formatToStandardDate(LocalDate date) {
        return date != null ? date.format(YYYY_MM_DD) : null;
    }

    /**
     * Format LocalDateTime to yyyy-MM-dd HH:mm:ss string
     */
    public static String formatToStandardDateTime(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.format(YYYY_MM_DD_HH_MM_SS) : null;
    }

    /**
     * Parse yyyyMMdd string to LocalDate
     */
    public static LocalDate parseFromYyyyMmDd(String dateStr) {
        return dateStr != null && !dateStr.trim().isEmpty() ? 
            LocalDate.parse(dateStr, YYYYMMDD) : null;
    }

    /**
     * Parse yyyy-MM-dd string to LocalDate
     */
    public static LocalDate parseFromStandardDate(String dateStr) {
        return dateStr != null && !dateStr.trim().isEmpty() ? 
            LocalDate.parse(dateStr, YYYY_MM_DD) : null;
    }

    /**
     * Parse yyyy-MM-dd HH:mm:ss string to LocalDateTime
     */
    public static LocalDateTime parseFromStandardDateTime(String dateTimeStr) {
        return dateTimeStr != null && !dateTimeStr.trim().isEmpty() ? 
            LocalDateTime.parse(dateTimeStr, YYYY_MM_DD_HH_MM_SS) : null;
    }

    /**
     * Get current date as yyyyMMdd string
     */
    public static String getCurrentDateAsYyyyMmDd() {
        return LocalDate.now().format(YYYYMMDD);
    }

    /**
     * Get current datetime as yyyyMMddhhmmss string
     */
    public static String getCurrentDateTimeAsYyyyMmDdHhMmSs() {
        return LocalDateTime.now().format(YYYYMMDDHHMMSS);
    }

    /**
     * Get current year as string
     */
    public static String getCurrentYear() {
        return LocalDate.now().format(YYYY);
    }

    /**
     * Get current month as string
     */
    public static String getCurrentMonth() {
        return LocalDate.now().format(MM);
    }

    /**
     * Get current day as string
     */
    public static String getCurrentDay() {
        return LocalDate.now().format(DD);
    }

    /**
     * Add days to a date and return formatted string
     */
    public static String addDaysAndFormat(LocalDate date, int days, DateTimeFormatter formatter) {
        return date.plusDays(days).format(formatter);
    }

    /**
     * Calculate days between two dates
     */
    public static long daysBetween(LocalDate startDate, LocalDate endDate) {
        return ChronoUnit.DAYS.between(startDate, endDate);
    }

    /**
     * Check if a date is in the past
     */
    public static boolean isInPast(LocalDate date) {
        return date.isBefore(LocalDate.now());
    }

    /**
     * Check if a datetime is in the past
     */
    public static boolean isInPast(LocalDateTime dateTime) {
        return dateTime.isBefore(LocalDateTime.now());
    }

    /**
     * Check if a date is in the future
     */
    public static boolean isInFuture(LocalDate date) {
        return date.isAfter(LocalDate.now());
    }

    /**
     * Check if a datetime is in the future
     */
    public static boolean isInFuture(LocalDateTime dateTime) {
        return dateTime.isAfter(LocalDateTime.now());
    }

    /**
     * Legacy support: Get time from Calendar (for backward compatibility)
     */
    public static String getTime(String pattern, Calendar calendar) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        LocalDateTime dateTime = LocalDateTime.ofInstant(calendar.toInstant(), calendar.getTimeZone().toZoneId());
        return dateTime.format(formatter);
    }
}
