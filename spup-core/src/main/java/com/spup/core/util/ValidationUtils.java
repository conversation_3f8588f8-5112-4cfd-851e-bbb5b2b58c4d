package com.spup.core.util;

import java.util.regex.Pattern;

/**
 * Common Validation Utilities
 * Centralized validation logic for common data types
 */
public class ValidationUtils {

    // Common regex patterns
    private static final Pattern PHONE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");
    private static final Pattern EMAIL_PATTERN = Pattern.compile("^[A-Za-z0-9+_.-]+@(.+)$");
    private static final Pattern ID_CARD_PATTERN = Pattern.compile("^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$");
    private static final Pattern UNIONID_PATTERN = Pattern.compile("^[a-zA-Z0-9_-]{28}$");
    private static final Pattern OPENID_PATTERN = Pattern.compile("^[a-zA-Z0-9_-]{28}$");

    /**
     * Validate Chinese mobile phone number
     */
    public static boolean isValidPhone(String phone) {
        return phone != null && PHONE_PATTERN.matcher(phone).matches();
    }

    /**
     * Validate email address
     */
    public static boolean isValidEmail(String email) {
        return email != null && EMAIL_PATTERN.matcher(email).matches();
    }

    /**
     * Validate Chinese ID card number
     */
    public static boolean isValidIdCard(String idCard) {
        return idCard != null && ID_CARD_PATTERN.matcher(idCard).matches();
    }

    /**
     * Validate WeChat unionid format
     */
    public static boolean isValidUnionid(String unionid) {
        return unionid != null && UNIONID_PATTERN.matcher(unionid).matches();
    }

    /**
     * Validate WeChat openid format
     */
    public static boolean isValidOpenid(String openid) {
        return openid != null && OPENID_PATTERN.matcher(openid).matches();
    }

    /**
     * Check if string is null or empty
     */
    public static boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }

    /**
     * Check if string is not null and not empty
     */
    public static boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }

    /**
     * Validate string length
     */
    public static boolean isValidLength(String str, int minLength, int maxLength) {
        if (str == null) {
            return minLength == 0;
        }
        int length = str.length();
        return length >= minLength && length <= maxLength;
    }

    /**
     * Validate numeric string
     */
    public static boolean isNumeric(String str) {
        if (isEmpty(str)) {
            return false;
        }
        try {
            Double.parseDouble(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * Validate integer string
     */
    public static boolean isInteger(String str) {
        if (isEmpty(str)) {
            return false;
        }
        try {
            Integer.parseInt(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * Validate positive integer
     */
    public static boolean isPositiveInteger(String str) {
        if (!isInteger(str)) {
            return false;
        }
        return Integer.parseInt(str) > 0;
    }

    /**
     * Validate non-negative integer
     */
    public static boolean isNonNegativeInteger(String str) {
        if (!isInteger(str)) {
            return false;
        }
        return Integer.parseInt(str) >= 0;
    }

    /**
     * Validate date string in yyyyMMdd format
     */
    public static boolean isValidDateYyyyMmDd(String dateStr) {
        if (isEmpty(dateStr) || dateStr.length() != 8) {
            return false;
        }
        try {
            DateTimeUtils.parseFromYyyyMmDd(dateStr);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Validate date string in yyyy-MM-dd format
     */
    public static boolean isValidDateStandard(String dateStr) {
        if (isEmpty(dateStr)) {
            return false;
        }
        try {
            DateTimeUtils.parseFromStandardDate(dateStr);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Validate datetime string in yyyy-MM-dd HH:mm:ss format
     */
    public static boolean isValidDateTimeStandard(String dateTimeStr) {
        if (isEmpty(dateTimeStr)) {
            return false;
        }
        try {
            DateTimeUtils.parseFromStandardDateTime(dateTimeStr);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Validate URL format
     */
    public static boolean isValidUrl(String url) {
        if (isEmpty(url)) {
            return false;
        }
        try {
            new java.net.URL(url);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Validate that a value is within a range
     */
    public static boolean isInRange(int value, int min, int max) {
        return value >= min && value <= max;
    }

    /**
     * Validate that a value is within a range
     */
    public static boolean isInRange(double value, double min, double max) {
        return value >= min && value <= max;
    }

    /**
     * Validate Chinese name (2-10 characters, Chinese characters only)
     */
    public static boolean isValidChineseName(String name) {
        if (isEmpty(name)) {
            return false;
        }
        return name.matches("^[\\u4e00-\\u9fa5]{2,10}$");
    }

    /**
     * Validate password strength (at least 8 characters, contains letters and numbers)
     */
    public static boolean isValidPassword(String password) {
        if (isEmpty(password) || password.length() < 8) {
            return false;
        }
        return password.matches("^(?=.*[A-Za-z])(?=.*\\d)[A-Za-z\\d@$!%*#?&]{8,}$");
    }
}
