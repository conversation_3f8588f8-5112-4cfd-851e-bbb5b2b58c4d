package com.spup.core.interceptor;

import java.io.IOException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.lang.NonNull;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import com.auth0.jwt.exceptions.AlgorithmMismatchException;
import com.auth0.jwt.exceptions.SignatureVerificationException;
import com.auth0.jwt.exceptions.TokenExpiredException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.spup.commons.api.CommonResult;
import com.spup.commons.api.ResultCodeEnum;
import com.spup.commons.utils.JWTUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * Base Token Interceptor
 * Provides common JWT token validation logic that can be extended by specific modules
 */
@Slf4j
public class BaseTokenInterceptor implements HandlerInterceptor {


    private ObjectMapper objectMapper;

    // JWTUtil is now a static utility class - no injection needed

    @Override
    public boolean preHandle(@NonNull HttpServletRequest request, 
                           @NonNull HttpServletResponse response,
                           @NonNull Object handler) throws Exception {

        // Skip non-handler methods
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        // Skip OPTIONS requests
        if ("OPTIONS".equals(request.getMethod())) {
            log.debug("OPTIONS request, skipping token validation");
            return true;
        }

        // Check if development mode bypass is enabled
        if (isDevelopmentMode(request)) {
            log.debug("Development mode, bypassing token validation");
            setDefaultDevelopmentSession(request);
            return true;
        }

        // Check if the url to login is requested
        if (request.getRequestURI().contains("/login")) {
            log.debug("URL to use code to get token of the oauth2.0, skipping token validation");
            return true;
        }

        // Validate JWT token
        String token = extractToken(request);
        if (token == null) {
            log.warn("Missing token in request");
            writeErrorResponse(response, CommonResult.failed(ResultCodeEnum.REQUEST_TOKEN_EMPTY));
            return false;
        }

        try {
            return validateTokenAndSetSession(token, request, response);
        } catch (Exception e) {
            log.error("Token validation failed:", e);
            writeErrorResponse(response, CommonResult.failed(ResultCodeEnum.JWT_TOKEN_FAILED));
            return false;
        }
    }

    /**
     * Extract token from request
     * Can be overridden by subclasses for different token extraction strategies
     */
    protected String extractToken(HttpServletRequest request) {
        String authHeader = request.getHeader("Authorization");
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            return authHeader.substring(7);
        }
        return authHeader;
    }

    /**
     * Check if development mode is enabled
     * Subclasses can override this for different development mode detection
     */
    protected boolean isDevelopmentMode(HttpServletRequest request) {
        // Check for development headers or profiles
        String devMode = request.getHeader("X-Dev-Mode");
        return "true".equals(devMode) || isLocalDevelopment(request);
    }

    /**
     * Check if request is from local development environment
     */
    protected boolean isLocalDevelopment(HttpServletRequest request) {
        String remoteAddr = request.getRemoteAddr();
        return "127.0.0.1".equals(remoteAddr) || "localhost".equals(request.getServerName());
    }

    /**
     * Set default session for development mode
     * Subclasses should override this to set appropriate default values
     */
    protected void setDefaultDevelopmentSession(HttpServletRequest request) {
        request.getSession().setAttribute("unionid", "dev-user-001");
        request.getSession().setAttribute("openid", "dev-openid-001");
        log.debug("Set default development session");
    }

    /**
     * Validate token and set session
     * Subclasses should override this for module-specific validation logic
     */
    protected boolean validateTokenAndSetSession(String token, 
                                                HttpServletRequest request, 
                                                HttpServletResponse response) throws Exception {
        try {
            DecodedJWT decodedJWT = JWTUtil.decodeToken(token);
            String unionid = decodedJWT.getClaim("unionid").asString();
            
            if (unionid == null || unionid.isEmpty()) {
                log.warn("Token missing unionid claim");
                writeErrorResponse(response, CommonResult.failed(ResultCodeEnum.JWT_TOKEN_FAILED));
                return false;
            }

            // Set session attributes
            request.getSession().setAttribute("unionid", unionid);
            
            // Allow subclasses to set additional session attributes
            setAdditionalSessionAttributes(decodedJWT, request);
            
            log.debug("Token validation successful for unionid: {}", unionid);
            return true;

        } catch (TokenExpiredException e) {
            log.warn("Token expired: {}", e.getMessage());
            writeErrorResponse(response, CommonResult.failed(ResultCodeEnum.JWT_TOKEN_EXPIRE));
            return false;
        } catch (SignatureVerificationException e) {
            log.warn("Token signature verification failed: {}", e.getMessage());
            writeErrorResponse(response, CommonResult.failed(ResultCodeEnum.JWT_TOKEN_FAILED));
            return false;
        } catch (AlgorithmMismatchException e) {
            log.warn("Token algorithm mismatch: {}", e.getMessage());
            writeErrorResponse(response, CommonResult.failed(ResultCodeEnum.JWT_TOKEN_FAILED));
            return false;
        }
    }

    /**
     * Allow subclasses to set additional session attributes from JWT claims
     */
    protected void setAdditionalSessionAttributes(DecodedJWT decodedJWT, HttpServletRequest request) {
        // Default implementation does nothing
        // Subclasses can override to set module-specific session attributes
    }

    /**
     * Write error response to client
     */
    protected void writeErrorResponse(HttpServletResponse response, CommonResult<?> result) throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        
        try {
            String jsonResponse = objectMapper.writeValueAsString(result);
            response.getWriter().write(jsonResponse);
        } catch (Exception e) {
            log.error("Failed to write error response:", e);
            response.getWriter().write("{\"status\":false,\"code\":500,\"message\":\"Authentication failed\"}");
        }
    }
}
