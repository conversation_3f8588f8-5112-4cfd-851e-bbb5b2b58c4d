package com.spup.core.ssbapi;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

public class ApiParamsConstant {
    private static Properties p = new Properties();
    static {
        InputStream in = null;
        try {
            in = ApiParamsConstant.class.getResourceAsStream("/ssb_config.properties");
            p.load(in);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static String  getClientId(){
        return p.getProperty("client_id");
    }
    public static String  getSecret(){
        return p.getProperty("client_secret");
    }
    public static String  getRegisteredRedirectUri(){
        return p.getProperty("registered_redirect_uri");
    }
    public static void main(String[] args) {
        System.out.println(ApiParamsConstant.getClientId());
    }
}
