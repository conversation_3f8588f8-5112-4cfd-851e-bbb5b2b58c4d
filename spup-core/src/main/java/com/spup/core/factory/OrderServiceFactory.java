package com.spup.core.factory;

import com.spup.enums.OrderCategoryEnum;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

/**
 * Factory for getting order service instances based on order category
 * Replaces the service class mapping that was previously in OrderCategoryEnum
 */
@Component
public class OrderServiceFactory {
    
    private final ApplicationContext applicationContext;
    
    public OrderServiceFactory(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }
    
    /**
     * Get the appropriate order service for the given category
     * @param category Order category enum
     * @return Service instance
     */
    public Object getOrderService(OrderCategoryEnum category) {
        switch (category) {
            case TICKET:
                return getServiceBean("appAppointmentOrderServiceImpl");
            case TEAM:
                return getServiceBean("appAppointmentTeamOrderServiceImpl");
            case ITEM_FYPD:
                return getServiceBean("appAppointmentItemOrderServiceImpl");
            case EXHIBITION:
            case EXHIBITION_TEAM:
                return getServiceBean("appAppointmentOrderTemporaryExhibitionServiceImpl");
            default:
                throw new IllegalArgumentException("Unsupported order category: " + category);
        }
    }
    
    /**
     * Get service bean by name, handling both admin and user modules
     * @param serviceName Base service name
     * @return Service instance
     */
    private Object getServiceBean(String serviceName) {
        // Try to get the bean directly first
        if (applicationContext.containsBean(serviceName)) {
            return applicationContext.getBean(serviceName);
        }
        
        // If not found, try with different naming conventions
        String[] possibleNames = {
            serviceName,
            serviceName.substring(0, 1).toUpperCase() + serviceName.substring(1), // Capitalize first letter
            "I" + serviceName.substring(0, 1).toUpperCase() + serviceName.substring(1) // Add I prefix
        };
        
        for (String name : possibleNames) {
            if (applicationContext.containsBean(name)) {
                return applicationContext.getBean(name);
            }
        }
        
        throw new IllegalStateException("No service bean found for: " + serviceName);
    }
    
    /**
     * Get order service by category code
     * @param categoryCode Category code
     * @return Service instance
     */
    public Object getOrderServiceByCode(Byte categoryCode) {
        OrderCategoryEnum category = OrderCategoryEnum.getEnum(categoryCode);
        if (category == null) {
            throw new IllegalArgumentException("Invalid order category code: " + categoryCode);
        }
        return getOrderService(category);
    }
}
