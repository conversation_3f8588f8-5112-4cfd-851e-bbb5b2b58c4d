package com.spup.core.service;

import com.spup.data.entity.appointment.AppBatch;

import java.util.List;
import java.util.Map;

/**
 * Base Batch Service Interface
 * Common batch operations that can be implemented by specific modules
 */
public interface BaseBatchService {

    /**
     * Get batches by date range and category
     */
    Map<String, List<AppBatch>> getListByDate(Byte categoryCode, String startDate, String endDate);

    /**
     * Get batch by batch number
     */
    AppBatch getByBatchNo(String batchNo);

    /**
     * Save batch
     */
    AppBatch save(AppBatch batch);

    /**
     * Find batches by category and date
     */
    List<AppBatch> findByCategoryAndDate(Byte categoryCode, String date);
}
