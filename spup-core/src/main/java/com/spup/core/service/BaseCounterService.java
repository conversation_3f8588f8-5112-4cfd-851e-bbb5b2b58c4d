package com.spup.core.service;

import com.spup.data.dao.appointment.AppAppointmentItemSuborderDao;
import com.spup.data.entity.appointment.AppAppointmentItemSuborder;
import com.spup.data.entity.appointment.AppBatch;
import com.spup.enums.BatchCategoryEnum;
import com.spup.enums.OrderStatusEnum;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * Base Counter Service
 * Unified counter service that replaces duplicated Item4Counter classes
 * This consolidates Item4Counter from admin and user modules
 */
@Service
public class BaseCounterService {
    
    @Resource
    protected AppAppointmentItemSuborderDao appAppointmentItemSuborderDao;
    
    private final Map<String, Map<Byte, String>> subOrderNoMap = new HashMap<>();

    /**
     * Initialize the counter map
     * This method should be implemented by subclasses to provide batch service
     */
    public void initMap() {
        synchronized (subOrderNoMap) {
            subOrderNoMap.clear();
            String yyyyMMdd = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            
            // Load regular batches
            loadBatchesForDate(yyyyMMdd, getBatchCategory());
            
            // Load temporary batches
            loadTempBatch();
        }
    }

    /**
     * Load temporary batches
     */
    public void loadTempBatch() {
        synchronized (subOrderNoMap) {
            String yyyyMMdd = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            loadBatchesForDate(yyyyMMdd, BatchCategoryEnum.TEMP_ITEM_FYPD);
        }
    }

    /**
     * Load batches for a specific date and category
     */
    protected void loadBatchesForDate(String yyyyMMdd, BatchCategoryEnum category) {
        Map<String, List<AppBatch>> listByDate = getBatchService()
                .getListByDate(category.getCode(), yyyyMMdd, yyyyMMdd);
        List<AppBatch> list = listByDate.get(yyyyMMdd);
        
        if (list != null) {
            for (AppBatch appBatch : list) {
                String batchNo = appBatch.getBatchNo();
                Integer ticketTotal = appBatch.getTicketTotal();
                Map<Byte, String> map = new HashMap<>();
                for (int i = 0; i < ticketTotal; i++) {
                    map.put((byte) (i + 1), "");
                }
                subOrderNoMap.put(batchNo, map);
            }
        }
    }

    /**
     * Clear and reinitialize the map
     */
    public void clear() {
        initMap();
    }

    /**
     * Load existing bookings
     */
    public void loadHasBook() {
        String yyyyMMdd = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        List<AppAppointmentItemSuborder> subordersByDate = appAppointmentItemSuborderDao.findByBatchDate(yyyyMMdd);
        
        for (AppAppointmentItemSuborder itemSuborder : subordersByDate) {
            if (itemSuborder.getSuborderStatus() == OrderStatusEnum.CANCELED.getCode()
                    || itemSuborder.getSuborderStatus() == OrderStatusEnum.BREAKED.getCode()) {
                continue;
            }
            String batchNo = itemSuborder.getBatchNo();
            Map<Byte, String> map = subOrderNoMap.get(batchNo);
            if (map != null) {
                map.put(itemSuborder.getSeatNo(), itemSuborder.getSuborderNo());
            }
        }
    }

    /**
     * Get next available seat number
     */
    public Byte getNo(String batchNo, String subOrderNo) {
        synchronized (subOrderNoMap) {
            Map<Byte, String> map = subOrderNoMap.get(batchNo);
            if (map == null) {
                return -1;
            }
            
            Iterator<Byte> iterator = map.keySet().iterator();
            while (iterator.hasNext()) {
                Byte key = iterator.next();
                String value = map.get(key);
                if (!StringUtils.hasLength(value)) {
                    map.put(key, subOrderNo);
                    return key;
                }
            }
            return -1;
        }
    }

    /**
     * Release a seat number
     */
    public void releaseNo(String batchNo, byte no) {
        synchronized (subOrderNoMap) {
            Map<Byte, String> map = subOrderNoMap.get(batchNo);
            if (map != null) {
                map.put(no, "");
            }
        }
    }

    /**
     * Get the batch category for this counter
     * Should be overridden by subclasses
     */
    protected BatchCategoryEnum getBatchCategory() {
        return BatchCategoryEnum.ITEM_FYPD; // Default category
    }

    /**
     * Get the batch service
     * Should be implemented by subclasses to provide the appropriate batch service
     */
    protected BaseBatchService getBatchService() {
        throw new UnsupportedOperationException("Subclasses must implement getBatchService()");
    }

    /**
     * Get current map state (for debugging)
     */
    public Map<String, Map<Byte, String>> getCurrentMapState() {
        return new HashMap<>(subOrderNoMap);
    }
}
