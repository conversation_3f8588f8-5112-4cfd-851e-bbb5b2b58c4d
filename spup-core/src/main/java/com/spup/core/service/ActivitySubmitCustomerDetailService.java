package com.spup.core.service;

import com.spup.core.dto.ActivitySubmitCustomerDetailDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

/**
 * Optimized service for handling ActivitySubmitCustomer with essential operations
 */
public interface ActivitySubmitCustomerDetailService {

    /**
     * Get ActivitySubmitCustomer with full details by ID
     */
    Optional<ActivitySubmitCustomerDetailDTO> getDetailById(Long id);

    /**
     * Get ActivitySubmitCustomer with full details by unionid
     */
    List<ActivitySubmitCustomerDetailDTO> getDetailsByUnionid(String unionid);

    /**
     * Get ActivitySubmitCustomer with full details by actRoundId
     */
    List<ActivitySubmitCustomerDetailDTO> getDetailsByActRoundId(String actRoundId);

    /**
     * Get ActivitySubmitCustomer with full details by actRoundId and unionid
     */
    List<ActivitySubmitCustomerDetailDTO> getDetailsByActRoundIdAndUnionid(String actRoundId, String unionid);

    /**
     * Get paginated ActivitySubmitCustomer with full details
     */
    Page<ActivitySubmitCustomerDetailDTO> getDetailsPage(Pageable pageable);

    /**
     * Search ActivitySubmitCustomer with full details by customer name
     */
    List<ActivitySubmitCustomerDetailDTO> searchByCustomerName(String customerName);

    /**
     * Get ActivitySubmitCustomer with full details by status
     */
    List<ActivitySubmitCustomerDetailDTO> getDetailsByStatus(String status);

    /**
     * Get ActivitySubmitCustomer with full details by type
     */
    List<ActivitySubmitCustomerDetailDTO> getDetailsByType(String type);
}
