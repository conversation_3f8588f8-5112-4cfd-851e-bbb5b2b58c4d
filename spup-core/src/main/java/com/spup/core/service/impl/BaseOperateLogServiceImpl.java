package com.spup.core.service.impl;

import com.spup.core.service.BaseOperateLogService;
import com.spup.data.dao.sys.AppOperateLogDao;
import com.spup.data.entity.sys.AppOperateLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.time.LocalDateTime;

/**
 * Base Operate Log Service Implementation
 * Provides common operation logging that can be extended by specific modules
 */
@Slf4j
public abstract class BaseOperateLogServiceImpl implements BaseOperateLogService {

    @Autowired
    protected AppOperateLogDao appOperateLogDao;

    @Override
    public AppOperateLog saveLog(HttpServletRequest request) {
        return saveLog(request, null);
    }

    @Override
    public AppOperateLog saveLog(HttpServletRequest request, String methodParams) {
        HttpSession session = request.getSession(false);
        if (session == null) {
            log.debug("No session found, skipping log save");
            return null;
        }

        String unionid = (String) session.getAttribute("unionid");
        return saveLog(request, methodParams, unionid);
    }

    @Override
    public AppOperateLog saveLog(HttpServletRequest request, String methodParams, String operator) {
        try {
            AppOperateLog operateLog = new AppOperateLog();
            
            // Set basic information
            operateLog.setOperator(operator);
            operateLog.setOperateTime(LocalDateTime.now());
            operateLog.setOperatorBrowser(request.getHeader("User-Agent"));
            operateLog.setOperatorIp(getIpAddress(request));
            operateLog.setOperateUrl(request.getRequestURI());

            // Set parameters
            String queryString = request.getQueryString();
            if (!StringUtils.hasLength(queryString)) {
                queryString = methodParams;
            }
            operateLog.setOperateParams(queryString);

            // Allow subclasses to set additional fields
            setAdditionalLogFields(operateLog, request);

            AppOperateLog savedLog = appOperateLogDao.save(operateLog);
            log.debug("Saved operation log for operator: {}, URL: {}", operator, request.getRequestURI());
            
            return savedLog;
        } catch (Exception e) {
            log.error("Error saving operation log: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Allow subclasses to set additional log fields
     */
    protected void setAdditionalLogFields(AppOperateLog log, HttpServletRequest request) {
        // Default implementation does nothing
        // Subclasses can override to set module-specific fields
    }
}
