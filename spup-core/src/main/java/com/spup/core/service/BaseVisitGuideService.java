package com.spup.core.service;

import com.spup.data.entity.AppVisitGuide;

import java.util.List;

/**
 * Base Visit Guide Service Interface
 * Common visit guide operations that can be implemented by specific modules
 */
public interface BaseVisitGuideService {

    /**
     * Get all visit guides
     */
    List<AppVisitGuide> getAllList();

    /**
     * View visit guide by id
     */
    AppVisitGuide view(long id);

    /**
     * Record visit guide read (increment page views)
     */
    void read(long id);

    /**
     * Find all visit guides sorted by code with limit
     */
    List<AppVisitGuide> findAllSortByCode(int limit);

    /**
     * Find all visit guides sorted by page views with limit
     */
    List<AppVisitGuide> findAllSortByPageViews(int limit);

    /**
     * Create new visit guide (admin operation)
     */
    AppVisitGuide create(AppVisitGuide appVisitGuide, String operatorId);

    /**
     * Update visit guide (admin operation)
     */
    AppVisitGuide update(AppVisitGuide appVisitGuide, String operatorId);

    /**
     * Delete visit guide (admin operation)
     */
    int delete(long id, String operatorId);
}
