package com.spup.core.service;

import com.spup.data.entity.sys.AppConfig;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Base Config Service Interface
 * Common configuration operations that can be implemented by specific modules
 * This unifies IAppConfigService interfaces from admin and user modules
 */
public interface BaseConfigService {

    /**
     * Get configurations by group number
     */
    Map<String, Object> getConfigsByGroup(String groupNo);

    /**
     * Get all configurations
     */
    List<AppConfig> getAll();

    /**
     * Find configuration by id
     */
    Optional<AppConfig> findById(Long id);

    /**
     * Save configuration
     */
    AppConfig save(AppConfig config);

    /**
     * Get configuration value by group and rule name
     */
    default Object getConfigValue(String groupNo, String ruleName) {
        Map<String, Object> configs = getConfigsByGroup(groupNo);
        return configs.get(ruleName);
    }

    /**
     * Get configuration value as string
     */
    default String getConfigValueAsString(String groupNo, String ruleName) {
        Object value = getConfigValue(groupNo, ruleName);
        return value != null ? value.toString() : null;
    }

    /**
     * Get configuration value as integer
     */
    default Integer getConfigValueAsInteger(String groupNo, String ruleName) {
        Object value = getConfigValue(groupNo, ruleName);
        if (value instanceof Integer) {
            return (Integer) value;
        } else if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }

    /**
     * Get configuration value as boolean
     */
    default Boolean getConfigValueAsBoolean(String groupNo, String ruleName) {
        Object value = getConfigValue(groupNo, ruleName);
        if (value instanceof Boolean) {
            return (Boolean) value;
        } else if (value instanceof String) {
            return Boolean.parseBoolean((String) value);
        }
        return null;
    }


}
