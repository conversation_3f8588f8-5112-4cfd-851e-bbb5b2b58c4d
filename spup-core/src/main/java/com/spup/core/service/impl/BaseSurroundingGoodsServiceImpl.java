package com.spup.core.service.impl;

import com.spup.core.service.BaseSurroundingGoodsService;
import com.spup.data.dao.AppSurroundingGoodsDao;
import com.spup.data.entity.AppSurroundingGoods;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Base Surrounding Goods Service Implementation
 * Common implementation that can be extended by specific modules
 */
@Slf4j
@Service
public class BaseSurroundingGoodsServiceImpl implements BaseSurroundingGoodsService {
    
    @Resource
    protected AppSurroundingGoodsDao appSurroundingGoodsDao;

    @Override
    public AppSurroundingGoods view(long id) {
        return appSurroundingGoodsDao.findById(id).orElse(null);
    }

    @Override
    public AppSurroundingGoods create(AppSurroundingGoods goods, String operatorId) {
        // Set creation metadata
        goods.setCreateBy(operatorId);
        goods.setUpdateBy(operatorId);
        
        log.info("Creating goods: {} by operator: {}", goods.getGoodsName(), operatorId);
        return appSurroundingGoodsDao.save(goods);
    }

    @Override
    public AppSurroundingGoods update(AppSurroundingGoods goods, String operatorId) {
        // Set update metadata
        goods.setUpdateBy(operatorId);
        
        log.info("Updating goods: {} by operator: {}", goods.getGoodsName(), operatorId);
        return appSurroundingGoodsDao.save(goods);
    }

    @Override
    public int delete(long id, String operatorId) {
        try {
            AppSurroundingGoods goods = view(id);
            if (goods != null) {
                log.info("Deleting goods: {} by operator: {}", goods.getGoodsName(), operatorId);
                appSurroundingGoodsDao.deleteById(id);
                return 1;
            }
            return 0;
        } catch (Exception e) {
            log.error("Error deleting goods with id: {} by operator: {}", id, operatorId, e);
            return 0;
        }
    }
}
