package com.spup.core.service.impl;

import com.spup.core.service.BaseConfigService;
import com.spup.data.dao.sys.AppConfigDao;
import com.spup.data.entity.sys.AppConfig;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Base Config Service Implementation
 * Common implementation that can be extended by specific modules
 * This unifies AppConfigServiceImpl from admin and user modules
 */
@Service
public class BaseConfigServiceImpl implements BaseConfigService {
    
    @Resource
    protected AppConfigDao appConfigDao;

    @Override
    public Map<String, Object> getConfigsByGroup(String groupNo) {
        List<AppConfig> appConfigs = appConfigDao.findByGroupNo(groupNo);
        Map<String, Object> result = new HashMap<>();
        for (AppConfig config : appConfigs) {
            result.put(config.getRuleName(), config.getRuleValue());
        }
        return result;
    }

    @Override
    public List<AppConfig> getAll() {
        return appConfigDao.findAll();
    }

    @Override
    public Optional<AppConfig> findById(Long id) {
        return appConfigDao.findById(id);
    }

    @Override
    public AppConfig save(AppConfig config) {
        return appConfigDao.save(config);
    }
}
