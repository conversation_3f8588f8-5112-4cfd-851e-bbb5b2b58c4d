package com.spup.core.service.impl;

import com.spup.commons.api.CommonResult;
import com.spup.core.service.BaseCustomerService;
import com.spup.data.dao.authority.AppCustomerDao;
import com.spup.data.entity.authority.AppCustomer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Optional;

/**
 * Base Customer Service Implementation
 * Provides common customer operations that can be extended by specific modules
 */
@Slf4j
public abstract class BaseCustomerServiceImpl implements BaseCustomerService {

    @Autowired
    protected AppCustomerDao appCustomerDao;

    @Override
    public AppCustomer get(String unionid) {
        if (unionid == null || unionid.trim().isEmpty()) {
            log.warn("Unionid is null or empty");
            return null;
        }

        Optional<AppCustomer> customerOptional = appCustomerDao.getByUnionid(unionid);
        if (!customerOptional.isPresent()) {
            log.debug("Customer not found for unionid: {}", unionid);
            return null;
        }
        return customerOptional.get();
    }

    @Override
    public AppCustomer save(String unionid, String openid) {
        if (unionid == null || unionid.trim().isEmpty()) {
            log.warn("Cannot save customer: unionid is null or empty");
            return null;
        }

        AppCustomer existingCustomer = get(unionid);
        if (existingCustomer != null) {
            log.debug("Customer already exists for unionid: {}", unionid);
            return existingCustomer;
        }

        // Create new customer
        AppCustomer newCustomer = new AppCustomer();
        newCustomer.setUnionid(unionid);
        newCustomer.setMiniOpenid(openid);

        // Allow subclasses to set additional fields
        setAdditionalCustomerFields(newCustomer, unionid, openid);

        AppCustomer savedCustomer = appCustomerDao.save(newCustomer);
        log.info("Created new customer with unionid: {}", unionid);
        
        return savedCustomer;
    }

    @Override
    public AppCustomer save(String unionid, String openid, String userName, String avatar, byte gender) {
        AppCustomer customer = save(unionid, openid);
        if (customer != null) {
            // Update additional information
            customer.setUserName(userName);
            customer.setUserAvatarSrc(avatar);
            customer.setUserGender(gender);
            
            // Allow subclasses to set additional fields
            setAdditionalCustomerFields(customer, unionid, openid, userName, avatar, gender);
            
            customer = appCustomerDao.save(customer);
            log.info("Updated customer information for unionid: {}", unionid);
        }
        return customer;
    }

    @Override
    public CommonResult<?> update(AppCustomer customer) {
        if (customer == null || customer.getUnionid() == null) {
            return CommonResult.failed("Customer or unionid cannot be null");
        }

        try {
            // Validate customer exists
            AppCustomer existingCustomer = get(customer.getUnionid());
            if (existingCustomer == null) {
                return CommonResult.failed("Customer not found");
            }

            // Allow subclasses to perform additional validation
            CommonResult<?> validationResult = validateCustomerUpdate(customer, existingCustomer);
            if (!validationResult.getStatus()) {
                return validationResult;
            }

            // Update customer
            AppCustomer updatedCustomer = appCustomerDao.save(customer);
            log.info("Updated customer: {}", customer.getUnionid());
            
            return CommonResult.succeeded(updatedCustomer);
        } catch (Exception e) {
            log.error("Error updating customer: {}", e.getMessage(), e);
            return CommonResult.failed("Failed to update customer");
        }
    }

    @Override
    public boolean exists(String unionid) {
        return get(unionid) != null;
    }

    @Override
    public AppCustomer getOrCreate(String unionid, String openid) {
        AppCustomer customer = get(unionid);
        if (customer == null) {
            customer = save(unionid, openid);
        }
        return customer;
    }

    /**
     * Allow subclasses to set additional customer fields during creation
     */
    protected void setAdditionalCustomerFields(AppCustomer customer, String unionid, String openid) {
        // Default implementation does nothing
        // Subclasses can override to set module-specific fields
    }

    /**
     * Allow subclasses to set additional customer fields during full save
     */
    protected void setAdditionalCustomerFields(AppCustomer customer, String unionid, String openid, 
                                             String userName, String avatar, byte gender) {
        // Default implementation does nothing
        // Subclasses can override to set module-specific fields
    }

    /**
     * Allow subclasses to perform additional validation during update
     */
    protected CommonResult<?> validateCustomerUpdate(AppCustomer newCustomer, AppCustomer existingCustomer) {
        // Default implementation allows all updates
        // Subclasses can override to add validation logic
        return CommonResult.succeeded(null);
    }
}
