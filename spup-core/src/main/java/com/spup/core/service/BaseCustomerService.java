package com.spup.core.service;

import com.spup.commons.api.CommonResult;
import com.spup.data.entity.authority.AppCustomer;

/**
 * Base Customer Service Interface
 * Common customer operations that can be implemented by specific modules
 */
public interface BaseCustomerService {

    /**
     * Get customer by unionid
     */
    AppCustomer get(String unionid);

    /**
     * Save customer with unionid and openid
     */
    AppCustomer save(String unionid, String openid);

    /**
     * Save customer with full information
     */
    AppCustomer save(String unionid, String openid, String userName, String avatar, byte gender);

    /**
     * Update customer information
     */
    CommonResult<?> update(AppCustomer customer);

    /**
     * Check if customer exists
     */
    boolean exists(String unionid);

    /**
     * Get or create customer
     */
    AppCustomer getOrCreate(String unionid, String openid);
}
