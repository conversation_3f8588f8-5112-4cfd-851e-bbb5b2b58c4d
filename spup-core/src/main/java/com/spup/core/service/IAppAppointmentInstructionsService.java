package com.spup.core.service;

import com.spup.data.entity.appointment.AppAppointmentInstructions;

/**
 * Unified Appointment Instructions Service Interface
 * Consolidates the duplicate IAppAppointmentInstructionsService interfaces from admin and user modules
 */
public interface IAppAppointmentInstructionsService {
    
    /**
     * Update appointment instructions
     * @param audienceNotice audience notice text
     * @param visitingInstructions visiting instructions text
     * @param admissionNotice admission notice text
     * @param unionid user ID who is updating
     * @return updated AppAppointmentInstructions entity
     */
    AppAppointmentInstructions update(String audienceNotice, String visitingInstructions, String admissionNotice, String unionid);
    
    /**
     * Get current appointment instructions
     * @return AppAppointmentInstructions entity
     */
    AppAppointmentInstructions get();
}
