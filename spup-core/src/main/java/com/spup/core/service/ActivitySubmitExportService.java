package com.spup.core.service;

import com.spup.core.dto.ActivitySubmitUploadDTO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Service for exporting ActivitySubmit data to CSV format
 */
public interface ActivitySubmitExportService {

    /**
     * Get activity submit data by date range for CSV export
     * @param startDateTime Start date and time
     * @param endDateTime End date and time
     * @return List of ActivitySubmitUploadDTO for CSV export
     */
    List<ActivitySubmitUploadDTO> getActivitySubmitDataForExport(LocalDateTime startDateTime, LocalDateTime endDateTime);

    /**
     * Generate CSV content from ActivitySubmitUploadDTO list
     * @param dataList List of ActivitySubmitUploadDTO
     * @return CSV content as String
     */
    String generateCsvContent(List<ActivitySubmitUploadDTO> dataList);

    /**
     * Generate CSV filename with timestamp
     * @param startDateTime Start date for filename
     * @param endDateTime End date for filename
     * @return Generated filename
     */
    String generateCsvFilename(LocalDateTime startDateTime, LocalDateTime endDateTime);

    /**
     * Get CSV headers in the specified order
     * @return Array of CSV headers
     */
    String[] getCsvHeaders();
}
