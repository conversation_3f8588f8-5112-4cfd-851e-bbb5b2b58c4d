package com.spup.core.service;

import com.spup.data.entity.sys.AppOperateLog;

import javax.servlet.http.HttpServletRequest;

/**
 * Base Operate Log Service Interface
 * Common operation logging that can be implemented by specific modules
 */
public interface BaseOperateLogService {

    /**
     * Save operation log from HTTP request
     */
    AppOperateLog saveLog(HttpServletRequest request);

    /**
     * Save operation log with method parameters
     */
    AppOperateLog saveLog(HttpServletRequest request, String methodParams);

    /**
     * Save operation log with custom operator
     */
    AppOperateLog saveLog(HttpServletRequest request, String methodParams, String operator);

    /**
     * Get IP address from request
     */
    default String getIpAddress(HttpServletRequest request) {
        String ipAddress = request.getHeader("X-Forwarded-For");
        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("Proxy-Client-IP");
        }
        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getRemoteAddr();
        }
        // Handle multiple IPs (take the first one)
        if (ipAddress != null && ipAddress.contains(",")) {
            ipAddress = ipAddress.split(",")[0].trim();
        }
        return ipAddress;
    }
}
