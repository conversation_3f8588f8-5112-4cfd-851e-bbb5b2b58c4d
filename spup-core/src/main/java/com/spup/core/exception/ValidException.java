package com.spup.core.exception;

/**
 * Unified ValidException
 * This replaces all module-specific ValidException classes
 * Use this instead of creating separate ValidException in each module
 */
public class ValidException extends BaseValidException {
    
    private static final long serialVersionUID = 1L;
    
    public ValidException() {
        super();
    }
    
    public ValidException(String message) {
        super(message);
    }
    
    public ValidException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public ValidException(Throwable cause) {
        super(cause);
    }
}
