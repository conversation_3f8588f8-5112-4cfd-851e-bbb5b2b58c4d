package com.spup.core.exception;

/**
 * Base Validation Exception
 * Common validation exception that can be extended by specific modules
 * This replaces the duplicated ValidException classes across modules
 */
public class BaseValidException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    public BaseValidException() {
        super();
    }

    public BaseValidException(String message) {
        super(message);
    }

    public BaseValidException(String message, Throwable cause) {
        super(message, cause);
    }

    public BaseValidException(Throwable cause) {
        super(cause);
    }
}


