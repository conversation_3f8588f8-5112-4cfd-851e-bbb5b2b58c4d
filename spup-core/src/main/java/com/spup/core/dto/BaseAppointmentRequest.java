package com.spup.core.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * Base Appointment Request DTO
 * Common appointment fields that can be extended by specific modules
 */
@Data
public abstract class BaseAppointmentRequest {

    @NotBlank(message = "Customer unionid cannot be blank")
    private String unionid;

    @NotNull(message = "Appointment time cannot be null")
    private LocalDateTime appointmentTime;

    private String remarks;

    private String contactPhone;

    private String contactName;

    /**
     * Get appointment type
     * Subclasses should implement this to specify their appointment type
     */
    public abstract String getAppointmentType();

    /**
     * Validate appointment specific rules
     * Subclasses can override this for custom validation
     */
    public void validate() {
        if (appointmentTime != null && appointmentTime.isBefore(LocalDateTime.now())) {
            throw new IllegalArgumentException("Appointment time cannot be in the past");
        }
    }
}
