package com.spup.core.dto;

import lombok.Data;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import javax.validation.constraints.Min;

/**
 * Base Page Request DTO
 * Common pagination fields that can be extended by specific modules
 */
@Data
public abstract class BasePageRequest {

    @Min(value = 0, message = "Page number must be non-negative")
    private Integer page = 0;

    @Min(value = 1, message = "Page size must be positive")
    private Integer size = 10;

    private String sortBy;

    private String sortDirection = "ASC";

    /**
     * Validate and normalize request parameters
     */
    public void validate() {
        if (page == null || page < 0) {
            page = 0;
        }
        if (size == null || size <= 0) {
            size = 10;
        }
        if (size > 100) {
            size = 100; // Limit maximum page size
        }
        if (sortDirection == null || (!sortDirection.equalsIgnoreCase("ASC") && !sortDirection.equalsIgnoreCase("DESC"))) {
            sortDirection = "ASC";
        }
    }

    /**
     * Get offset for database queries
     */
    public int getOffset() {
        return page * size;
    }

    /**
     * Convert to Spring Data Pageable
     */
    public Pageable toPageable() {
        validate();
        
        if (sortBy != null && !sortBy.trim().isEmpty()) {
            Sort.Direction direction = "DESC".equalsIgnoreCase(sortDirection) ? 
                Sort.Direction.DESC : Sort.Direction.ASC;
            return PageRequest.of(page, size, Sort.by(direction, sortBy.trim()));
        } else {
            return PageRequest.of(page, size);
        }
    }

    /**
     * Convert to Spring Data Pageable with default sort
     */
    public Pageable toPageable(String defaultSortBy) {
        if (sortBy == null || sortBy.trim().isEmpty()) {
            sortBy = defaultSortBy;
        }
        return toPageable();
    }

    /**
     * Convert to Spring Data Pageable with default sort and direction
     */
    public Pageable toPageable(String defaultSortBy, String defaultDirection) {
        if (sortBy == null || sortBy.trim().isEmpty()) {
            sortBy = defaultSortBy;
        }
        if (sortDirection == null || sortDirection.trim().isEmpty()) {
            sortDirection = defaultDirection;
        }
        return toPageable();
    }
}
