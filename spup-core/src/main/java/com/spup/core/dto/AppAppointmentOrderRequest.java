package com.spup.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * Unified Appointment Order Request DTO
 * Consolidates the duplicate AppAppointmentOrderRequest classes from admin, user, and activity modules
 */
@Schema(description = "预约下单对象")
public class AppAppointmentOrderRequest implements Serializable {
    
    @Schema(description = "场次编号，非id，如202301180930", example = "202301180930")
    private String batchNo;
    
    @Schema(description = "联系人列表，数组字符串格式",
            example = "[{\"idcardCategory\": 1, \"idcardNo\": \"string\", \"name\": \"string\", \"phone\": \"string\"}]")
    private String contacts;
    
    @Schema(description = "临展编号，非id，如L20230906T20230923", example = "L20230906T20230923")
    private String exhibitionNo;
    
    @Schema(description = "预约分类，默认为1，1常展，8临展", example = "1")
    private Byte category = 1;

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getContacts() {
        return contacts;
    }

    public void setContacts(String contacts) {
        this.contacts = contacts;
    }

    public String getExhibitionNo() {
        return exhibitionNo;
    }

    public void setExhibitionNo(String exhibitionNo) {
        this.exhibitionNo = exhibitionNo;
    }

    public Byte getCategory() {
        return category;
    }

    public void setCategory(Byte category) {
        this.category = category;
    }

    @Override
    public String toString() {
        return "AppAppointmentOrderRequest{" +
                "batchNo='" + batchNo + '\'' +
                ", contacts='" + contacts + '\'' +
                ", exhibitionNo='" + exhibitionNo + '\'' +
                ", category=" + category +
                '}';
    }
}
