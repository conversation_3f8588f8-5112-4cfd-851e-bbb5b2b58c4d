package com.spup.core.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.spup.data.entity.activity.ActivitySubmitCustomer;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * Enhanced ActivitySubmitCustomer DTO with Activity and ActivityRound information
 * Combines data from activity_submit_customer, activity_info, and activity_round_info tables
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ActivitySubmitUploadDTO {

    // ActivitySubmitCustomer fields
    @JsonProperty("主键")
    private Long id;
    @JsonProperty("报名人姓名")
    private String username;
    @JsonProperty("报名人证件类型")
    private ActivitySubmitCustomer.SubmitCustomerPassTypeEnum passType;
    @JsonProperty("报名人证件号")
    private String passString;
    @JsonProperty("报名人联系方式")
    private String phoneString;
    @JsonProperty("报名人年龄")
    private Integer age;
    @JsonProperty("报名人性别")
    private Integer gender;
    private LocalDateTime checkInDateTime;
    @JsonProperty("场次编号")
    private String actRoundId;
    @JsonProperty("创建时间")
    private LocalDateTime createOn;
    @JsonProperty("修改时间")
    private LocalDateTime updateOn;

    // Activity fields (from activity_info table)
    @JsonProperty("活动编号")
    private String activityId;
    @JsonProperty("活动名称")
    private String activityName;

    // ActivityRound fields (from activity_round_info table)
    @JsonProperty("场次名称")
    private String actRoundInfo;
    @JsonProperty("场次开始时间")
    private LocalDateTime actRoundStartDateTime;
    @JsonProperty("场次结束时间")
    private LocalDateTime actRoundEndDateTime;

    /**
     * Constructor from ActivitySubmitCustomer entity
     */
    public ActivitySubmitUploadDTO(ActivitySubmitCustomer customer) {
        this.id = customer.getId();
        this.username = customer.getUsername();
        this.passType = customer.getPassType();
        this.passString = customer.getPassString();
        this.phoneString = customer.getPhoneString();
        this.age = customer.getAge();
        this.gender = customer.getGender();
        this.checkInDateTime = customer.getCheckInDateTime();
        this.actRoundId = customer.getActRoundId();
        this.createOn = customer.getCreateOn();
        this.updateOn = customer.getUpdateOn();
    }
}
