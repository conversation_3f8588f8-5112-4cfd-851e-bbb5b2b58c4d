package com.spup.core.dto;

import com.spup.data.entity.activity.ActivitySubmitCustomer;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * Enhanced ActivitySubmitCustomer DTO with Activity and ActivityRound information
 * Combines data from activity_submit_customer, activity_info, and activity_round_info tables
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ActivitySubmitCustomerDetailDTO {

    // ActivitySubmitCustomer fields

    private Long id;
    private String submitId;
    private String unionid;
    private String username;
    private ActivitySubmitCustomer.SubmitCustomerPassTypeEnum passType;
    private String passString;
    private String phoneString;
    private Integer age;
    private Integer gender;
    private ActivitySubmitCustomer.SubmitCustomerStatusEnum status;
    private ActivitySubmitCustomer.SubmitCustomerTypeEnum type;
    private LocalDateTime checkInDateTime;
    private String actRoundId;

    // Activity fields (from activity_info table)
    private String activityId;
    private String activityName;
    private String activityPicUrl;
    private String activityIntroductionInfo;
    private LocalDateTime activityStartDateTime;
    private LocalDateTime activityEndDateTime;

    // ActivityRound fields (from activity_round_info table)
    private String actRoundInfo;
    private LocalDateTime actRoundSubmitStartDateTime;
    private LocalDateTime actRoundSubmitEndDateTime;
    private LocalDateTime actRoundStartDateTime;
    private LocalDateTime actRoundEndDateTime;
    private Integer actRoundSubmitNumber;
    private Integer actRoundMaxSubmitNum;

    /**
     * Constructor from ActivitySubmitCustomer entity
     */
    public ActivitySubmitCustomerDetailDTO(ActivitySubmitCustomer customer) {
        this.id = customer.getId();
        this.submitId = customer.getSubmitId();
        this.unionid = customer.getUnionid();
        this.username = customer.getUsername();
        this.passType = customer.getPassType();
        this.passString = customer.getPassString();
        this.phoneString = customer.getPhoneString();
        this.age = customer.getAge();
        this.gender = customer.getGender();
        this.status = customer.getStatus();
        this.type = customer.getType();
        this.checkInDateTime = customer.getCheckInDateTime();
        this.actRoundId = customer.getActRoundId();
    }

    /**
     * Get status display name
     */
    public String getStatusDisplayName() {
        if (status == null) return "Unknown";
        switch (status) {
            case SUBMITTED: return "已报名";
            case CHECKEDIN: return "已签到";
            case CANCELLED: return "已取消";
            default: return status.name();
        }
    }

    /**
     * Get type display name
     */
    public String getTypeDisplayName() {
        if (type == null) return "Unknown";
        switch (type) {
            case ADULT: return "成人";
            case CHILD: return "儿童";
            case SPECIAL: return "特殊";
            default: return type.name();
        }
    }

    /**
     * Get pass type display name
     */
    public String getPassTypeDisplayName() {
        if (passType == null) return "Unknown";
        switch (passType) {
            case IDCARD: return "身份证";
            case PASSPORT: return "护照";
            case OTHER: return "其他";
            default: return passType.name();
        }
    }

    /**
     * Get gender display name
     */
    public String getGenderDisplayName() {
        if (gender == null) return "未知";
        switch (gender) {
            case 1: return "男";
            case 2: return "女";
            default: return "未知";
        }
    }

    /**
     * Check if customer is checked in
     */
    public boolean isCheckedIn() {
        return status == ActivitySubmitCustomer.SubmitCustomerStatusEnum.CHECKEDIN;
    }

    /**
     * Check if customer submission is cancelled
     */
    public boolean isCancelled() {
        return status == ActivitySubmitCustomer.SubmitCustomerStatusEnum.CANCELLED;
    }

    /**
     * Check if customer is adult
     */
    public boolean isAdult() {
        return type == ActivitySubmitCustomer.SubmitCustomerTypeEnum.ADULT;
    }

    /**
     * Check if customer is child
     */
    public boolean isChild() {
        return type == ActivitySubmitCustomer.SubmitCustomerTypeEnum.CHILD;
    }

    /**
     * Check if activity round has started
     */
    public boolean isActivityRoundStarted() {
        return actRoundStartDateTime != null && LocalDateTime.now().isAfter(actRoundStartDateTime);
    }

    /**
     * Check if activity round has ended
     */
    public boolean isActivityRoundEnded() {
        return actRoundEndDateTime != null && LocalDateTime.now().isAfter(actRoundEndDateTime);
    }

    /**
     * Check if submission period is active
     */
    public boolean isSubmissionPeriodActive() {
        LocalDateTime now = LocalDateTime.now();
        return actRoundSubmitStartDateTime != null && actRoundSubmitEndDateTime != null &&
               now.isAfter(actRoundSubmitStartDateTime) && now.isBefore(actRoundSubmitEndDateTime);
    }

    /**
     * Get remaining submission slots
     */
    public Integer getRemainingSlots() {
        if (actRoundMaxSubmitNum == null || actRoundSubmitNumber == null) {
            return null;
        }
        return Math.max(0, actRoundMaxSubmitNum - actRoundSubmitNumber);
    }

    /**
     * Check if activity round is full
     */
    public boolean isActivityRoundFull() {
        Integer remaining = getRemainingSlots();
        return remaining != null && remaining <= 0;
    }
}
