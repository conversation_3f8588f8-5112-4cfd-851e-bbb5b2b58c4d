package com.spup.core.dto;

import com.spup.data.entity.appointment.AppAppointmentOrder;
import com.spup.data.entity.appointment.AppAppointmentSuborder;
import com.spup.data.entity.appointment.AppTemporaryExhibition;

import java.util.List;

/**
 * Unified Appointment Order Response DTO
 * Consolidates the duplicate AppAppointmentOrderResponse classes from admin and user modules
 */
public class AppAppointmentOrderResponse extends AppAppointmentOrder {
    private AppTemporaryExhibition exhibition;
    private List<AppAppointmentSuborder> suborders;

    public List<AppAppointmentSuborder> getSuborders() {
        return suborders;
    }

    public void setSuborders(List<AppAppointmentSuborder> suborders) {
        this.suborders = suborders;
    }

    public AppTemporaryExhibition getExhibition() {
        return exhibition;
    }

    public void setExhibition(AppTemporaryExhibition exhibition) {
        this.exhibition = exhibition;
    }
}
