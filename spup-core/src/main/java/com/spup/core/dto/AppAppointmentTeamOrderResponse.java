package com.spup.core.dto;

import java.util.List;

import com.spup.data.entity.appointment.AppAppointmentOrder;
import com.spup.data.entity.appointment.AppAppointmentSuborder;
import com.spup.data.entity.appointment.AppTemporaryExhibition;

/**
 * Unified Appointment Team Order Response DTO
 * Consolidates the duplicate AppAppointmentTeamOrderResponse classes from admin and user modules
 */
public class AppAppointmentTeamOrderResponse extends AppAppointmentOrder {
    private AppTemporaryExhibition exhibition;
    private List<AppAppointmentSuborder> suborders;

    public AppTemporaryExhibition getExhibition() {
        return exhibition;
    }

    public void setExhibition(AppTemporaryExhibition exhibition) {
        this.exhibition = exhibition;
    }

    public List<AppAppointmentSuborder> getSuborders() {
        return suborders;
    }

    public void setSuborders(List<AppAppointmentSuborder> suborders) {
        this.suborders = suborders;
    }
}
