package com.spup.core.dto;

import lombok.Data;

import javax.validation.constraints.Min;

/**
 * Base Goods List Request DTO
 * Common fields for goods listing requests
 */
@Data
public abstract class BaseGoodsListRequest {

    @Min(value = 0, message = "Page number must be non-negative")
    private Integer page = 0;

    @Min(value = 1, message = "Page size must be positive")
    private Integer size = 10;

    private String keyword;

    private String category;

    private Boolean available;

    /**
     * Validate request parameters
     */
    public void validate() {
        if (page == null || page < 0) {
            page = 0;
        }
        if (size == null || size <= 0) {
            size = 10;
        }
        if (size > 100) {
            size = 100; // Limit maximum page size
        }
    }

    /**
     * Get offset for database queries
     */
    public int getOffset() {
        return page * size;
    }
}
