package com.spup.core.dto;

import java.util.List;

import com.spup.data.entity.appointment.AppAppointmentOrder;
import com.spup.data.entity.appointment.AppAppointmentSuborderTemporaryExhibition;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Unified Appointment Order Temporary Exhibition Response DTO
 * Consolidates the duplicate AppAppointmentOrderTemporaryExhibitionResponse classes from admin and user modules
 */
@Schema(description = "临展预约响应对象")
public class AppAppointmentOrderTemporaryExhibitionResponse extends AppAppointmentOrder {
    private List<AppAppointmentSuborderTemporaryExhibition> suborders;

    public List<AppAppointmentSuborderTemporaryExhibition> getSuborders() {
        return suborders;
    }

    public void setSuborders(List<AppAppointmentSuborderTemporaryExhibition> suborders) {
        this.suborders = suborders;
    }
}
