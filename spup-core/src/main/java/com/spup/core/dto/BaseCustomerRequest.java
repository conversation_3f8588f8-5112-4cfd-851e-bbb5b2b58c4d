package com.spup.core.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * Base Customer Request DTO
 * Common customer fields that can be extended by specific modules
 */
@Data
public abstract class BaseCustomerRequest {

    @NotBlank(message = "Unionid cannot be blank")
    private String unionid;

    private String openid;

    private String userName;

    private String avatar;

    private Byte gender;

    private String phone;

    private String email;

    /**
     * Validate customer specific rules
     * Subclasses can override this for custom validation
     */
    public void validate() {
        if (phone != null && !phone.matches("^1[3-9]\\d{9}$")) {
            throw new IllegalArgumentException("Invalid phone number format");
        }
        
        if (email != null && !email.matches("^[A-Za-z0-9+_.-]+@(.+)$")) {
            throw new IllegalArgumentException("Invalid email format");
        }
    }
}
