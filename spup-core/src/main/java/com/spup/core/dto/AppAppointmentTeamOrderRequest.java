package com.spup.core.dto;

import java.io.Serializable;

import com.spup.enums.OrderCategoryEnum;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Unified Appointment Team Order Request DTO
 * Consolidates the duplicate AppAppointmentTeamOrderRequest classes from admin and user modules
 */
public class AppAppointmentTeamOrderRequest implements Serializable {

    @Schema(description = "场次编号，非id，如202301180930")
    private String batchNo;
    @Schema(description = "预约人数", example = "25")
    private Integer visitorsNum;
    @Schema(description = "团队名称", example = "上海博物馆参观团")
    private String teamName;
    private String owerUnit;
    private String owerUnitCode;
    @Schema(description = "联系人", example = "张三")
    private String ownerName;
    @Schema(description = "联系电话", example = "13800138000")
    private String ownerPhone;
    @Schema(description = "临展编号，非id，如L20230906T20230923")
    private String exhibitionNo;
    private Byte category = OrderCategoryEnum.TEAM.getCode();
    @Schema(description = "联系人", example = "张三")
    private String contactName;
    @Schema(description = "联系电话", example = "13800138000")
    private String contactPhone;
    @Schema(description = "预约日期", example = "2023-12-25")
    private String appointmentDate;
    @Schema(description = "预约时间段", example = "09:00-11:00")
    private String appointmentTime;
    @Schema(description = "预约人数", example = "25")
    private Integer appointmentNumber;

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public Integer getVisitorsNum() {
        return visitorsNum;
    }

    public void setVisitorsNum(Integer visitorsNum) {
        this.visitorsNum = visitorsNum;
    }

    public String getTeamName() {
        return teamName;
    }

    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }

    public String getOwerUnit() {
        return owerUnit;
    }

    public void setOwerUnit(String owerUnit) {
        this.owerUnit = owerUnit;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getOwnerPhone() {
        return ownerPhone;
    }

    public void setOwnerPhone(String ownerPhone) {
        this.ownerPhone = ownerPhone;
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public String getAppointmentDate() {
        return appointmentDate;
    }

    public void setAppointmentDate(String appointmentDate) {
        this.appointmentDate = appointmentDate;
    }

    public String getAppointmentTime() {
        return appointmentTime;
    }

    public void setAppointmentTime(String appointmentTime) {
        this.appointmentTime = appointmentTime;
    }

    public Integer getAppointmentNumber() {
        return appointmentNumber;
    }

    public void setAppointmentNumber(Integer appointmentNumber) {
        this.appointmentNumber = appointmentNumber;
    }

    public String getOwerUnitCode() {
        return owerUnitCode;
    }

    public void setOwerUnitCode(String owerUnitCode) {
        this.owerUnitCode = owerUnitCode;
    }

    public String getExhibitionNo() {
        return exhibitionNo;
    }

    public void setExhibitionNo(String exhibitionNo) {
        this.exhibitionNo = exhibitionNo;
    }

    public Byte getCategory() {
        return category;
    }

    public void setCategory(Byte category) {
        this.category = category;
    }
}
