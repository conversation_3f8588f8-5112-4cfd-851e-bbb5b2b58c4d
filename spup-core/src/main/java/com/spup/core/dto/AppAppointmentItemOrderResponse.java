package com.spup.core.dto;

import com.spup.data.entity.appointment.AppAppointmentItemSuborder;
import com.spup.data.entity.appointment.AppAppointmentOrder;

import java.util.List;

/**
 * Unified Appointment Item Order Response DTO
 * Consolidates the duplicate AppAppointmentItemOrderResponse classes from admin and user modules
 */
public class AppAppointmentItemOrderResponse extends AppAppointmentOrder {
    private List<AppAppointmentItemSuborder> suborders;

    public List<AppAppointmentItemSuborder> getSuborders() {
        return suborders;
    }

    public void setSuborders(List<AppAppointmentItemSuborder> suborders) {
        this.suborders = suborders;
    }
}
