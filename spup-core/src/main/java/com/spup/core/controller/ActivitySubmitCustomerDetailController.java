package com.spup.core.controller;

import com.spup.commons.api.CommonResult;
import com.spup.core.dto.ActivitySubmitCustomerDetailDTO;
import com.spup.core.service.ActivitySubmitCustomerDetailService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * Controller for ActivitySubmitCustomer with enhanced details including Activity and ActivityRound information
 */
@Tag(name = "活动报名详情管理")
@RestController
@RequestMapping("/activity-submit-customer-detail")
public class ActivitySubmitCustomerDetailController extends BaseController {

    @Autowired
    private ActivitySubmitCustomerDetailService activitySubmitCustomerDetailService;

    @Operation(summary = "根据ID获取活动报名详情")
    @GetMapping("/{id}")
    public CommonResult<ActivitySubmitCustomerDetailDTO> getDetailById(
            @Parameter(description = "报名记录ID", required = true) @PathVariable Long id) {
        logRequest("根据ID获取活动报名详情", id);
        
        Optional<ActivitySubmitCustomerDetailDTO> detailOpt = activitySubmitCustomerDetailService.getDetailById(id);
        if (!detailOpt.isPresent()) {
            return failed("报名记录不存在");
        }
        
        return success(detailOpt.get());
    }

    @Operation(summary = "根据用户unionid获取活动报名详情列表")
    @GetMapping("/by-unionid/{unionid}")
    public CommonResult<List<ActivitySubmitCustomerDetailDTO>> getDetailsByUnionid(
            @Parameter(description = "用户unionid", required = true) @PathVariable String unionid) {
        logRequest("根据用户unionid获取活动报名详情列表", unionid);
        
        List<ActivitySubmitCustomerDetailDTO> details = activitySubmitCustomerDetailService.getDetailsByUnionid(unionid);
        return success(details);
    }

    @Operation(summary = "根据活动场次ID获取报名详情列表")
    @GetMapping("/by-round/{actRoundId}")
    public CommonResult<List<ActivitySubmitCustomerDetailDTO>> getDetailsByActRoundId(
            @Parameter(description = "活动场次ID", required = true) @PathVariable String actRoundId) {
        logRequest("根据活动场次ID获取报名详情列表", actRoundId);
        
        List<ActivitySubmitCustomerDetailDTO> details = activitySubmitCustomerDetailService.getDetailsByActRoundId(actRoundId);
        return success(details);
    }

    @Operation(summary = "根据活动场次ID和用户unionid获取报名详情")
    @GetMapping("/by-round-and-unionid/{actRoundId}/{unionid}")
    public CommonResult<List<ActivitySubmitCustomerDetailDTO>> getDetailsByActRoundIdAndUnionid(
            @Parameter(description = "活动场次ID", required = true) @PathVariable String actRoundId,
            @Parameter(description = "用户unionid", required = true) @PathVariable String unionid) {
        logRequest("根据活动场次ID和用户unionid获取报名详情", String.format("actRoundId=%s, unionid=%s", actRoundId, unionid));
        
        List<ActivitySubmitCustomerDetailDTO> details = activitySubmitCustomerDetailService
                .getDetailsByActRoundIdAndUnionid(actRoundId, unionid);
        return success(details);
    }



    @Operation(summary = "分页获取活动报名详情列表")
    @GetMapping("/page")
    public CommonResult<Page<ActivitySubmitCustomerDetailDTO>> getDetailsPage(
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "排序字段") @RequestParam(defaultValue = "createOn") String sortBy,
            @Parameter(description = "排序方向") @RequestParam(defaultValue = "desc") String sortDir) {
        logRequest("分页获取活动报名详情列表", String.format("page=%d, size=%d, sortBy=%s, sortDir=%s", page, size, sortBy, sortDir));
        
        Sort.Direction direction = "asc".equalsIgnoreCase(sortDir) ? Sort.Direction.ASC : Sort.Direction.DESC;
        Pageable pageable = PageRequest.of(page, size, Sort.by(direction, sortBy));
        
        Page<ActivitySubmitCustomerDetailDTO> detailsPage = activitySubmitCustomerDetailService.getDetailsPage(pageable);
        return success(detailsPage);
    }



    @Operation(summary = "根据客户姓名搜索报名详情")
    @GetMapping("/search/by-customer-name")
    public CommonResult<List<ActivitySubmitCustomerDetailDTO>> searchByCustomerName(
            @Parameter(description = "客户姓名关键词", required = true) @RequestParam String customerName) {
        logRequest("根据客户姓名搜索报名详情", customerName);
        
        List<ActivitySubmitCustomerDetailDTO> details = activitySubmitCustomerDetailService.searchByCustomerName(customerName);
        return success(details);
    }

    @Operation(summary = "根据报名状态获取报名详情")
    @GetMapping("/by-status/{status}")
    public CommonResult<List<ActivitySubmitCustomerDetailDTO>> getDetailsByStatus(
            @Parameter(description = "报名状态 (SUBMITTED, CHECKEDIN, CANCELLED)", required = true) @PathVariable String status) {
        logRequest("根据报名状态获取报名详情", status);
        
        List<ActivitySubmitCustomerDetailDTO> details = activitySubmitCustomerDetailService.getDetailsByStatus(status);
        return success(details);
    }

    @Operation(summary = "根据客户类型获取报名详情")
    @GetMapping("/by-type/{type}")
    public CommonResult<List<ActivitySubmitCustomerDetailDTO>> getDetailsByType(
            @Parameter(description = "客户类型 (ADULT, CHILD, SPECIAL)", required = true) @PathVariable String type) {
        logRequest("根据客户类型获取报名详情", type);
        
        List<ActivitySubmitCustomerDetailDTO> details = activitySubmitCustomerDetailService.getDetailsByType(type);
        return success(details);
    }

    @Operation(summary = "获取当前用户的报名详情")
    @GetMapping("/my-submissions")
    public CommonResult<List<ActivitySubmitCustomerDetailDTO>> getMySubmissions() {
        logRequest("获取当前用户的报名详情");
        String unionid = requireAuthentication();
        
        List<ActivitySubmitCustomerDetailDTO> details = activitySubmitCustomerDetailService.getDetailsByUnionid(unionid);
        return success(details);
    }

    @Operation(summary = "分页获取当前用户的报名详情")
    @GetMapping("/my-submissions/page")
    public CommonResult<Page<ActivitySubmitCustomerDetailDTO>> getMySubmissionsPage(
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size) {
        logRequest("分页获取当前用户的报名详情", String.format("page=%d, size=%d", page, size));
        String unionid = requireAuthentication();

        // Get all user submissions and manually paginate
        List<ActivitySubmitCustomerDetailDTO> allDetails = activitySubmitCustomerDetailService.getDetailsByUnionid(unionid);
        int start = page * size;
        int end = Math.min(start + size, allDetails.size());
        List<ActivitySubmitCustomerDetailDTO> pageContent = allDetails.subList(start, end);

        Page<ActivitySubmitCustomerDetailDTO> detailsPage = new PageImpl<>(pageContent,
                PageRequest.of(page, size), allDetails.size());
        return success(detailsPage);
    }
}
