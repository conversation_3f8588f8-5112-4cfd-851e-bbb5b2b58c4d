package com.spup.core.security;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.PostConstruct;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * JWT Security Configuration
 * Provides secure JWT configuration with environment-based secrets
 */
@Configuration
@Slf4j
@Getter
public class JWTSecurityConfig {

    @Value("${jwt.secret:}")
    private String jwtSecret;

    @Value("${jwt.access-token.expiration:${jwt.expires:15}}")
    private int accessTokenExpirationMinutes;

    @Value("${jwt.refresh-token.expiration:${jwt.refresh.expires:10080}}")
    private int refreshTokenExpirationMinutes; // 7 days default

    @Value("${jwt.issuer:spup-system}")
    private String issuer;

    @Value("${jwt.audience:spup-users}")
    private String audience;

    private String effectiveSecret;

    @PostConstruct
    public void init() {
        validateAndSetupSecret();
        logSecurityConfiguration();
    }

    /**
     * Validate and setup JWT secret
     */
    private void validateAndSetupSecret() {
        if (!StringUtils.hasText(jwtSecret)) {
            log.warn("JWT secret not configured in properties. Generating secure random secret.");
            effectiveSecret = generateSecureSecret();
        } else if (jwtSecret.length() < 32) {
            log.warn("JWT secret is too short (< 32 chars). Consider using a longer secret.");
            effectiveSecret = jwtSecret;
        } else {
            effectiveSecret = jwtSecret;
        }
    }

    /**
     * Generate a cryptographically secure random secret
     */
    private String generateSecureSecret() {
        SecureRandom secureRandom = new SecureRandom();
        byte[] secretBytes = new byte[64]; // 512 bits
        secureRandom.nextBytes(secretBytes);
        return Base64.getEncoder().encodeToString(secretBytes);
    }

    /**
     * Log security configuration (without exposing secret)
     */
    private void logSecurityConfiguration() {
        log.info("JWT Security Configuration:");
        log.info("  - Access Token Expiration: {} minutes", accessTokenExpirationMinutes);
        log.info("  - Refresh Token Expiration: {} minutes", refreshTokenExpirationMinutes);
        log.info("  - Issuer: {}", issuer);
        log.info("  - Audience: {}", audience);
        log.info("  - Secret Length: {} characters", effectiveSecret.length());
        log.info("  - Secret Source: {}", StringUtils.hasText(jwtSecret) ? "Configuration" : "Generated");
    }

    /**
     * Get the effective JWT secret
     */
    public String getEffectiveSecret() {
        return effectiveSecret;
    }

    /**
     * Validate token expiration settings
     */
    public boolean isSecureConfiguration() {
        return accessTokenExpirationMinutes <= 60 && // Max 1 hour for access tokens
               refreshTokenExpirationMinutes <= 20160 && // Max 14 days for refresh tokens
               effectiveSecret.length() >= 32; // Minimum 32 characters for secret
    }

    /**
     * Get security recommendations
     */
    public String getSecurityRecommendations() {
        StringBuilder recommendations = new StringBuilder();
        
        if (accessTokenExpirationMinutes > 60) {
            recommendations.append("- Reduce access token expiration to ≤ 60 minutes\n");
        }
        
        if (refreshTokenExpirationMinutes > 20160) {
            recommendations.append("- Reduce refresh token expiration to ≤ 14 days\n");
        }
        
        if (effectiveSecret.length() < 64) {
            recommendations.append("- Use a longer secret (≥ 64 characters)\n");
        }
        
        if (!StringUtils.hasText(jwtSecret)) {
            recommendations.append("- Configure jwt.secret in application properties\n");
        }
        
        return recommendations.toString();
    }
}
