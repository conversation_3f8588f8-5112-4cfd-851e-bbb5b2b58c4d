package com.spup.core.interceptor;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import com.spup.commons.api.CommonResult;
import com.spup.commons.api.ResultCodeEnum;

public class BaseTokenInterceptorTest {

    private ObjectMapper objectMapper;

    private BaseTokenInterceptor interceptor;

    @BeforeEach
    public void setUp() {
        // Create ObjectMapper with JSR310 support
        objectMapper = new ObjectMapper();

        // Configure JSR310 module for Java 8 time support
        JavaTimeModule module = new JavaTimeModule();
        module.addSerializer(new LocalDateTimeSerializer(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        module.addSerializer(new LocalTimeSerializer(DateTimeFormatter.ofPattern("HH:mm:ss")));
        module.addSerializer(new LocalDateSerializer(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        module.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        module.addDeserializer(LocalDate.class, new LocalDateDeserializer(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        module.addDeserializer(LocalTime.class, new LocalTimeDeserializer(DateTimeFormatter.ofPattern("HH:mm:ss")));

        // Register the JSR310 module
        objectMapper.registerModule(module);
        objectMapper.findAndRegisterModules();
        objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        objectMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);

        // Create a concrete implementation for testing
        interceptor = new BaseTokenInterceptor() {
            @Override
            protected ObjectMapper getObjectMapper() {
                return objectMapper;
            }
        };
    }

    @Test
    public void testWriteErrorResponseWithLocalDateTime() throws Exception {
        // Create mock request and response
        HttpServletRequest request = mock(HttpServletRequest.class);
        HttpServletResponse response = mock(HttpServletResponse.class);
        
        // Create a StringWriter to capture the response
        StringWriter stringWriter = new StringWriter();
        PrintWriter printWriter = new PrintWriter(stringWriter);
        
        when(response.getWriter()).thenReturn(printWriter);
        
        // Create a CommonResult that contains LocalDateTime
        CommonResult<?> result = CommonResult.failed(ResultCodeEnum.JWT_TOKEN_FAILED);
        
        // Call the writeErrorResponse method
        interceptor.writeErrorResponse(response, result);
        
        // Get the written JSON
        printWriter.flush();
        String jsonResponse = stringWriter.toString();
        
        // Verify the response contains the expected fields
        assertNotNull(jsonResponse);
        assertTrue(jsonResponse.contains("\"status\":false"));
        assertTrue(jsonResponse.contains("\"code\":"));
        assertTrue(jsonResponse.contains("\"message\":"));
        assertTrue(jsonResponse.contains("\"time\":"));
        
        // Verify that time is serialized as string, not timestamp array
        assertTrue(jsonResponse.contains("\"time\":\""));
        
        System.out.println("Interceptor JSON Response: " + jsonResponse);
        
        // Verify we can deserialize it back
        CommonResult<?> deserializedResult = objectMapper.readValue(jsonResponse, CommonResult.class);
        assertNotNull(deserializedResult);
        assertNotNull(deserializedResult.getTime());
        
        System.out.println("BaseTokenInterceptor serialization test passed!");
    }
}
