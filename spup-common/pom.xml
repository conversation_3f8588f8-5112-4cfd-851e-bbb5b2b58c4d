<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.spup</groupId>
        <artifactId>spup-root</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>spup-common</artifactId>
    <packaging>jar</packaging>

    <name>SPUP Common</name>
    <description>Common utilities, exceptions, and shared components</description>

    <dependencies>
        <!-- Module-specific dependencies (common dependencies inherited from parent) -->

        <!-- JWT - For token handling utilities -->
        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>java-jwt</artifactId>
        </dependency>

        <!-- Spring Web removed - spup-common is now a pure utility module -->
        <!-- No Spring dependencies needed for utility classes -->
    </dependencies>
</project>
