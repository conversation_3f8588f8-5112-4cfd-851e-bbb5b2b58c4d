package com.spup.commons.jackson;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import com.spup.commons.api.CommonResult;

public class JacksonLocalDateTimeTest {

    private ObjectMapper objectMapper;

    @BeforeEach
    public void setUp() {
        objectMapper = new ObjectMapper();

        // Configure JSR310 module for Java 8 time support
        JavaTimeModule module = new JavaTimeModule();
        module.addSerializer(new LocalDateTimeSerializer(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        module.addSerializer(new LocalTimeSerializer(DateTimeFormatter.ofPattern("HH:mm:ss")));
        module.addSerializer(new LocalDateSerializer(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        module.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        module.addDeserializer(LocalDate.class, new LocalDateDeserializer(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        module.addDeserializer(LocalTime.class, new LocalTimeDeserializer(DateTimeFormatter.ofPattern("HH:mm:ss")));

        // Register the JSR310 module
        objectMapper.registerModule(module);

        // Find and register all available modules (including JSR310 if available on classpath)
        objectMapper.findAndRegisterModules();

        // Configure deserialization
        objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);

        // Configure serialization - don't use timestamps for dates
        objectMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
    }

    @Test
    public void testLocalDateTimeSerializationInCommonResult() throws Exception {
        // Create a CommonResult with LocalDateTime
        CommonResult<String> result = CommonResult.succeeded("test data");
        
        // Serialize to JSON
        String json = objectMapper.writeValueAsString(result);
        
        // Verify that the JSON contains the time field and it's properly formatted
        assertNotNull(json);
        assertTrue(json.contains("time"));
        assertTrue(json.contains("\"time\":"));
        
        // The time should be in the format "yyyy-MM-dd HH:mm:ss", not as a timestamp
        assertFalse(json.contains("\"time\":["));  // Array format would indicate timestamp
        
        System.out.println("Serialized JSON: " + json);
        
        // Deserialize back to object
        CommonResult<?> deserializedResult = objectMapper.readValue(json, CommonResult.class);
        
        assertNotNull(deserializedResult);
        assertNotNull(deserializedResult.getTime());
        
        System.out.println("Test passed: LocalDateTime serialization works correctly");
    }

    @Test
    public void testDirectLocalDateTimeSerialization() throws Exception {
        LocalDateTime now = LocalDateTime.now();
        
        // Serialize LocalDateTime directly
        String json = objectMapper.writeValueAsString(now);
        
        // Should be in string format, not timestamp
        assertTrue(json.startsWith("\""));
        assertTrue(json.endsWith("\""));
        
        System.out.println("Direct LocalDateTime serialization: " + json);
        
        // Deserialize back
        LocalDateTime deserialized = objectMapper.readValue(json, LocalDateTime.class);
        
        assertNotNull(deserialized);
        
        System.out.println("Direct LocalDateTime test passed");
    }

    @Test
    public void testJSR310ModuleAvailability() {
        // Test that JSR310 module is available and working
        try {
            LocalDateTime testTime = LocalDateTime.of(2023, 12, 25, 15, 30, 45);
            String json = objectMapper.writeValueAsString(testTime);
            
            // Should serialize to "2023-12-25 15:30:45" format
            assertTrue(json.contains("2023-12-25"));
            assertTrue(json.contains("15:30:45"));
            
            System.out.println("JSR310 module test - serialized: " + json);
            
            // Test deserialization
            LocalDateTime deserialized = objectMapper.readValue(json, LocalDateTime.class);
            assertTrue(deserialized.equals(testTime));
            
            System.out.println("JSR310 module is working correctly");
        } catch (Exception e) {
            throw new RuntimeException("JSR310 module test failed", e);
        }
    }
}
