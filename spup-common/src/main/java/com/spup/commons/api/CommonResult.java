package com.spup.commons.api;

import java.time.LocalDateTime;

public class CommonResult<T> {
    /**
     * 返回状态
     */
    private boolean status;
    /**
     * 请求结果代码
     */
    private long code;
    /**
     * 请求信息
     */
    private String message;
    /**
     * 请求时间
     */
    private LocalDateTime time = LocalDateTime.now();
    /**
     * 返回数据
     */
    private T data;

    protected CommonResult() {
    }

    protected CommonResult(boolean status, long code, String message, T data) {
        this.status = status;
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public static <T> CommonResult<T> succeeded(T data) {
        return new CommonResult<T>(
            ResultCodeEnum.SUCCEEDED.getStatus(), 
            ResultCodeEnum.SUCCEEDED.getCode(),
            ResultCodeEnum.SUCCEEDED.getMessage(),
            data
        );
    }
    public static <T> CommonResult<T> succeeded(T data,String message) {
        return new CommonResult<T>(
                ResultCodeEnum.SUCCEEDED.getStatus(),
                ResultCodeEnum.SUCCEEDED.getCode(),
                message,
                data
        );
    }

    public static <T> CommonResult<T> failed(IErrorCode errCode) {
        return new CommonResult<T>(
            errCode.getStatus(), 
            errCode.getCode(), 
            errCode.getMessage(), 
            null
        );
    }

    public static <T> CommonResult<T> failed(String message) {
        return new CommonResult<T>(
            ResultCodeEnum.FAILED.getStatus(), 
            ResultCodeEnum.FAILED.getCode(), 
            message, 
            null
        );
    }

    public static <T> CommonResult<T> failed(long code, String message) {
        return new CommonResult<T>(
            ResultCodeEnum.FAILED.getStatus(), 
            code, 
            message, 
            null
        );
    }

    public static <T> CommonResult<T> failed(T data, String message) {
        return new CommonResult<T>(
            ResultCodeEnum.FAILED.getStatus(),
            ResultCodeEnum.FAILED.getCode(),
            message,
            data
        );
    }

    public static <T> CommonResult<T> failed() {
        return failed(ResultCodeEnum.FAILED);
    }

    public static <T> CommonResult<T> validationFailed() {
        return failed(ResultCodeEnum.VALIDATION_FAILED);
    }

    public static <T> CommonResult<T> authorizedFailed() {
        return failed(ResultCodeEnum.AUTHORIZED_FAILED);
    }

    public static <T> CommonResult<T> priviledgeForbidden() {
        return failed(ResultCodeEnum.PRIVILEDGE_FORBIDDEN);
    }

    public boolean getStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

    public long getCode() {
        return code;
    }

    public void setCode(long code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public LocalDateTime getTime() {
        return time;
    }

    public void setTime(LocalDateTime time) {
        this.time = time;
    }
}
