package com.spup.commons.api;

public enum ResultCodeEnum implements IErrorCode {
    SUCCEEDED(true, 0, "Operation succeeded"),
    FAILED(false, 500, "Operation failed"),
    VALIDATION_FAILED(false, 404, "Validation failed"),
    AUTHORIZED_FAILED(false, 401, "Unauthorized or token expired"),
    REQUEST_TOKEN_EMPTY(false, 600, "Request token is empty"),
    GET_TOKEN_KEY_FAILED(false, 601, "Getting token key failed"),
    GEN_PUBLIC_KEY_FAILED(false, 602, "Generating public key failed"),
    JWT_TOKEN_EXPIRE(false, 603, "Token expired"),
    JWT_TOKEN_FAILED(false,604, "Token parse failed"),
    TOOMANY_REQUEST_CONTROLLED(false, 429, "Flow control on server"), 
    SERVICE_DEGRADED(false, 604,"Service degraded"),
    BAD_GATEWAY(false, 502,"Bad gateway"),
    PRIVILEDGE_FORBIDDEN(false, 403, "Priviledge forbidden"), 
    USER_NOEXIST(false, 405, "User not exist"),
    USER_EXIST(false, 409, "User already exist"),
    ; // This way prevents expansion when forgetting semicolons

    private boolean status;
    private long code;
    private String message;

    private ResultCodeEnum(boolean status, long code, String message) {
        this.status = status;
        this.code = code;
        this.message = message;
    }

    @Override
    public boolean getStatus() {
        return status;
    }

    @Override
    public long getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }
    
}