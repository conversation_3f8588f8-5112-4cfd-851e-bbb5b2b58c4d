package com.spup.commons.utils;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTDecodeException;
import com.auth0.jwt.exceptions.TokenExpiredException;
import com.auth0.jwt.interfaces.DecodedJWT;

import lombok.extern.slf4j.Slf4j;

import java.io.UnsupportedEncodingException;
import java.util.Calendar;
import java.util.HashMap;

@Slf4j
public class JWTUtil {

    // Default sign string - can be overridden by passing as parameter
    private static final String DEFAULT_SIGN_STRING = "<EMAIL>";

    // Utility class - prevent instantiation
    private JWTUtil() {
        throw new UnsupportedOperationException("Utility class");
    }

    public static String getToken(String unionid, String openid, String signStr) throws UnsupportedEncodingException {
        return getToken(unionid, openid, 120, signStr);
    }

    public static String getToken(String unionid, String openid) throws UnsupportedEncodingException {
        return getToken(unionid, openid, 120, DEFAULT_SIGN_STRING);
    }

    public static String getToken(String unionid, String openid, int validMinute, String signStr) throws UnsupportedEncodingException {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MINUTE, validMinute);
        log.info("JWTUtil getToken with signStr: {}", signStr);

        String token = JWT.create()
                .withHeader(new HashMap<>())  // Header
                .withClaim("unionid", unionid)  // Payload
                .withClaim("openid", openid)
                .withExpiresAt(calendar.getTime())  // 过期时间
                .sign(Algorithm.HMAC384(signStr));  // 签名用的secret
        return token;
    }

    /**
     * 1有效token,0,非法token，-1过期token
     * @param token
     * @param signStr
     * @return
     * @throws UnsupportedEncodingException
     */
    public static Integer verifierToken(String token, String signStr) throws UnsupportedEncodingException {
        JWTVerifier jwtVerifier = JWT.require(Algorithm.HMAC384(signStr)).build();
        try {
            jwtVerifier.verify(token);
        }catch (JWTDecodeException e){
            return -1;
        }catch (TokenExpiredException e){
            return -2;
        }
        return 0;
    }

    public static Integer verifierToken(String token) throws UnsupportedEncodingException {
        return verifierToken(token, DEFAULT_SIGN_STRING);
    }

    public static DecodedJWT decodeToken(String token, String signStr) throws UnsupportedEncodingException {
        JWTVerifier jwtVerifier = JWT.require(Algorithm.HMAC384(signStr)).build();
        DecodedJWT decodedJWT = jwtVerifier.verify(token);
        return decodedJWT;
    }

    public static DecodedJWT decodeToken(String token) throws UnsupportedEncodingException {
        return decodeToken(token, DEFAULT_SIGN_STRING);
    }
}
