package com.spup.commons.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

/**
 * <p>
 * Title: workflow
 * </p>
 * <p>
 * Description:
 * </p>
 * <p>
 * Copyright: Copyright (c) 2022
 * </p>
 * <p>
 * Company: 煌豆
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class DateTimeUtil {
	/*
	 * yyyyMMddHHmmss
	 */
	public static final String PATTERN_0 = "yyyyMMddHHmmss";
	public static final SimpleDateFormat FORMATTER_0 = new SimpleDateFormat(PATTERN_0);
	/*
	 * yyyy-MM-dd HH:mm:ss
	 */
	public static final String PATTERN_1 = "yyyy-MM-dd HH:mm:ss";
	public static final SimpleDateFormat FORMATTER_1 = new SimpleDateFormat(PATTERN_1);
	/*
	 * yyyy-MM-dd
	 */
	public static final String PATTERN_2 = "yyyy-MM-dd";
	/*
	 * yyyy年MM月dd日 HH时mm分ss秒
	 */
	public static final String PATTERN_3 = "yyyy年MM月dd日 HH时mm分ss秒";
	/*
	 * yyyy年MM月dd日
	 */
	public static final String PATTERN_4 = "yyyy年MM月dd日";
	/*
	 * yyyy-MM-dd HH:mm
	 */
	public static final String PATTERN_5 = "yyyy-MM-dd HH:mm";
	/*
	 * yyyy年MM月dd日 HH时mm分
	 */
	public static final String PATTERN_6 = "yyyy年MM月dd日 HH时mm分";
	/**
	 * yyyyMMdd
	 */
	public static final String PATTERN_7 = "yyyyMMdd";
	public static final SimpleDateFormat FORMATTER_7 = new SimpleDateFormat(PATTERN_7);
	
	public static final String PATTERN_8 = "yyyyMMddHHmm";
	public static final SimpleDateFormat FORMATTER_8 = new SimpleDateFormat(PATTERN_8);
	
	public static final String PATTERN_9 = "HHmm";
	public static final SimpleDateFormat FORMATTER_9 = new SimpleDateFormat(PATTERN_9);

	public static String getSysTime() {
		return new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
	}
	
	public static Date parseDate(String str)throws Exception {
	
		return new SimpleDateFormat("yyyyMMddHHmmss").parse(str);
		
	}
	
	public static byte[] getSimpleSysTime() {
		byte[] ret = new byte[3];
		Calendar now = Calendar.getInstance();
		ret[0] = (byte) now.get(Calendar.HOUR_OF_DAY);
		ret[1] = (byte) now.get(Calendar.MINUTE);
		ret[2] = (byte) now.get(Calendar.SECOND);
		return ret;
	}

	
	public static String getTime(String pattern, Calendar cal) {
		return new SimpleDateFormat(pattern).format(cal.getTime());
	}

	public static String getSysTime(String pattern) {
		return new SimpleDateFormat(pattern).format(new Date());
	}

	public static Calendar getDateTime(String dateTime) {
		Calendar date = (Calendar) Calendar.getInstance().clone();
		int year = parseInt(dateTime.substring(0, 4), 1900);
		int Month = parseInt(dateTime.substring(4, 6), 1) - 1;
		int day = parseInt(dateTime.substring(6, 8), 1);
		int hh = parseInt(dateTime.substring(8, 10), 0);
		int mm = parseInt(dateTime.substring(10, 12), 0);
		int ss = parseInt(dateTime.substring(12, 14), 0);
		date.set(year, Month, day, hh, mm, ss);
		date.set(Calendar.MILLISECOND, 0);
		return date;
	}
	
	public static Calendar getDateTime(String dateTime,String pattern) throws ParseException {
		SimpleDateFormat format = new SimpleDateFormat(pattern);
		Calendar date = (Calendar) Calendar.getInstance().clone();
		date.setTime(format.parse(dateTime));
		date.set(Calendar.MILLISECOND, 0);
		return date;
	}

	public static String getDateTimeString(Date t) {
		return getDateTimeString(t,PATTERN_0);
	}
	
	public static String getDateTimeString(Date t,String pattern) {
		SimpleDateFormat formatter = new SimpleDateFormat(pattern);
		String time = formatter.format(t);
		return time;
	}

	public static String reFormatTime(String str, String oldPattern, String newPattern) {
		try {
			SimpleDateFormat oldFormat = new SimpleDateFormat(oldPattern);
			SimpleDateFormat newFormat = new SimpleDateFormat(newPattern);
			return (String) newFormat.format(oldFormat.parse(str));
		} catch (Exception e) {
			System.out.println("format tiem Error (" + e.toString() + ")");
			return null;
		}// try//
	}

	public static String formatTime(String str, String pattern) {
		try {
			if (str == null)
				return null;
			int size = str.length();
			if (size == 0) {
				return "";
			}
			if (size < 14) {
				for (int i = 0; i < 14 - size; i++)
					str = str + "0";
			}
			int nYear = Integer.parseInt(str.substring(0, 4), 10);
			int nMonth = Integer.parseInt(str.substring(4, 6), 10);
			int nDay = Integer.parseInt(str.substring(6, 8), 10);
			int nHour = Integer.parseInt(str.substring(8, 10), 10);
			int nMinute = Integer.parseInt(str.substring(10, 12), 10);
			int nSecond = Integer.parseInt(str.substring(12, 14), 10);
			Calendar tmpCalendar = Calendar.getInstance(TimeZone.getTimeZone("CTT"));
			tmpCalendar.set(nYear, nMonth - 1, nDay, nHour, nMinute, nSecond);
			SimpleDateFormat formatter = new SimpleDateFormat(pattern);
			Date tmpDate = tmpCalendar.getTime();
			return (String) formatter.format(tmpDate);
		} catch (Exception e) {
			return null;
		}
	}

	public static String formatTime(String str) {
		try {
			if (str == null)
				return null;
			int size = str.length();
			if (size < 6) {
				for (int i = 0; i < 6 - size; i++)
					str = "0" + str;
			}
			return str.substring(0, 2) + ":" + str.substring(2, 4) + ":" + str.substring(4, 6);
		} catch (Exception e) {
			return "";
		}
	}

	public static int parseInt(String str, int defaultvalue) {
		try {
			return Integer.parseInt(str);
		} catch (Exception e) {
			e.printStackTrace();
			return defaultvalue;
		}
	}
	
	/** 
     * 时间戳转换成日期格式字符串 
     * @param seconds 精确到秒的字符串 
     * @param formatStr 
     * @return 
     */  
    public static String timeStamp2Date(String seconds,String format) {  
        if(seconds == null || seconds.isEmpty() || seconds.equals("null")){  
            return "";  
        }  
        if(format == null || format.isEmpty()) format = "yyyy-MM-dd HH:mm:ss";  
        SimpleDateFormat sdf = new SimpleDateFormat(format);  
        return sdf.format(new Date(Long.valueOf(seconds+"000")));  
    }  
    /** 
     * 日期格式字符串转换成时间戳 
     * @param date 字符串日期 
     * @param format 如：yyyy-MM-dd HH:mm:ss 
     * @return 
     */  
    public static String date2TimeStamp(String date_str,String format){  
        try {  
            SimpleDateFormat sdf = new SimpleDateFormat(format);  
            return String.valueOf(sdf.parse(date_str).getTime()/1000);  
        } catch (Exception e) {  
            e.printStackTrace();  
        }  
        return "";  
    }  
    
    /** 
     * 取得当前时间戳（精确到秒） 
     * @return 
     */  
    public static String timeStamp(){  
        long time = System.currentTimeMillis();  
        String t = String.valueOf(time/1000);  
        return t;  
    }  

	/**
	 * 计算两点的时间差,单位：秒
	 * 
	 * @param time1
	 * @param time2
	 * @return
	 */
	public static int accountTime(String time1, String time2) {
		Calendar c1 = getDateTime(time1);
		Calendar c2 = getDateTime(time2);
		return (int) ((c2.getTimeInMillis() - c1.getTimeInMillis()) / 1000);
	}
	
	public static int getDays(Calendar c1, Calendar c2) {
		  int elapsed = 0;
		  Calendar temp1, temp2;

		  if (c2.after(c1)) {
		   temp2 = (Calendar) c2.clone();
		   temp1 = (Calendar) c1.clone();
		  } else {
		   temp2 = (Calendar) c1.clone();
		   temp1 = (Calendar) c2.clone();
		  }

		  temp1.clear(Calendar.MILLISECOND);
		  temp1.clear(Calendar.SECOND);
		  temp1.clear(Calendar.MINUTE);
		  temp1.clear(Calendar.HOUR_OF_DAY);

		  temp2.clear(Calendar.MILLISECOND);
		  temp2.clear(Calendar.SECOND);
		  temp2.clear(Calendar.MINUTE);
		  temp2.clear(Calendar.HOUR_OF_DAY);

		  while (temp1.before(temp2)) {
		   temp1.add(Calendar.DATE, 1);
		   elapsed++;
		  }
		  return elapsed;
	}
	
	public static String getTimeStr(long timeInMillis){
		String str = "";
		int millis_to_sec = (int)timeInMillis/1000;
		int second = millis_to_sec%60;
		if(second>0){
			str = second+"秒"+str;
		}
		int sec_to_min = millis_to_sec/60;
		int minute = sec_to_min%60;
		if(minute>0){
			str = minute+"分"+str;
		}
		
		int min_to_ho = sec_to_min/60;
		int hour = min_to_ho%24;
		if(hour>0){
			str = hour+"小时"+str;
		}
		
		int ho_to_day = min_to_ho/24;
		if(ho_to_day>0){
			str = ho_to_day+"天"+str;
		}
		
		return str;
	}

	
	public static void main(String[] args) throws Exception {
		
		Calendar t0  = Calendar.getInstance();		
		t0.setTimeInMillis(1460278800*1000L);
		
		Calendar t1  = Calendar.getInstance();	
		t1.setTimeInMillis(1459906623*1000L);
		
		@SuppressWarnings("unused")
		String strx = DateTimeUtil.getTimeStr(t0.getTimeInMillis()-t1.getTimeInMillis());
		
		
		//Date转换成String
		Date now = new Date();
		@SuppressWarnings("unused")
		String yyyyMMddhhmmss = DateTimeUtil.getDateTimeString(now);
		@SuppressWarnings("unused")
		String yyyyMMdd = DateTimeUtil.getDateTimeString(now, DateTimeUtil.PATTERN_7);
		
		//Calendar 转化成 String
		Calendar now1 = Calendar.getInstance();
		@SuppressWarnings("unused")
		String yyyyMMdd1 = DateTimeUtil.getTime(DateTimeUtil.PATTERN_7, now1);
		
		//String格式变化
		
		String yyyyMMdd2 = "20131212";
		@SuppressWarnings("unused")
		String yyyy_MM_dd = DateTimeUtil.reFormatTime(yyyyMMdd2, DateTimeUtil.PATTERN_7, DateTimeUtil.PATTERN_2);
		
		//String 转化成 Date
		
		@SuppressWarnings("unused")
		Date date = DateTimeUtil.parseDate(DateTimeUtil.reFormatTime(yyyyMMdd2, DateTimeUtil.PATTERN_7, DateTimeUtil.PATTERN_0));
		
		//String 转化成 Calendar
		@SuppressWarnings("unused")
		Calendar ca = DateTimeUtil.getDateTime(DateTimeUtil.reFormatTime(yyyyMMdd2, DateTimeUtil.PATTERN_7, DateTimeUtil.PATTERN_0));
		
		//当前时间
		@SuppressWarnings("unused")
		String now_str = DateTimeUtil.getSysTime(); //默认格式yyyyMMddhhmmss
		@SuppressWarnings("unused")
		String now_str2 = DateTimeUtil.getSysTime(DateTimeUtil.PATTERN_7);//指定格式
		
		Calendar cal = Calendar.getInstance();
		cal.setTimeInMillis(1531894574000L);
		System.out.println(DateTimeUtil.getTime(DateTimeUtil.PATTERN_1, cal));
		
	}
	
	
}
