package com.spup.commons.utils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class NumberGenerator {

    /**
     * 门店编号
     * @return
     */
    public static String getStoreId(){
        return null;
    }

    /**
     * 产品编号
     * @return
     */
    public static String getServiceItemId(){
        return null;
    }

    /**
     * 订单编号
     * @return
     */
    public static String getOrderNo(){
        String _yyyyMMddHHmmssSSS = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyMMddHHmmssSSS"));
        int  random = (int)(Math.random()*9);
        String orderId = _yyyyMMddHHmmssSSS+random;//订单自定义编号，店铺Id-日期（yyyyMMddHHmmssSSS）

        return orderId;
    }

    public static void main(String[] args) {
        int  random = (int)(Math.random()*9);
        System.out.println(random);
    }

}
