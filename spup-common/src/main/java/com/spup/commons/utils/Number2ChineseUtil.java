package com.spup.commons.utils;

public class Number2ChineseUtil {
    public static String transition(String si){
        String []aa={"","十","百","千","万","十万","百万","千万","亿","十亿"};
        String []bb={"零","一","二","三","四","五","六","七","八","九"};
        char[] ch=si.toCharArray();
        int maxindex=ch.length;
        //字符的转换
        //两位数的特殊转换
        String str = "";
        for(int i=maxindex-1,j=0;i>=0;i--,j++){
            if(ch[j]!=48){
                if(j==0&&ch[j]==49){
                    str += aa[i];
                }else{
                    str += bb[ch[j]-48]+aa[i];
                }
            }
        }

        return str;
    }


}
