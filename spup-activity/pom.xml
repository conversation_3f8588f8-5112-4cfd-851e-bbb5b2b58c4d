<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.spup</groupId>
        <artifactId>spup-root</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>spup-activity</artifactId>
    <packaging>jar</packaging>

    <name>SPUP Activity</name>
    <description>Activity management and business logic components</description>
    <dependencies>
        <dependency>
            <groupId>com.spup</groupId>
            <artifactId>spup-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.spup</groupId>
            <artifactId>spup-data</artifactId>
        </dependency>
        <dependency>
            <groupId>com.spup</groupId>
            <artifactId>spup-core</artifactId>
        </dependency>

        <!-- Spring Boot -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- Jackson and Lombok inherited from parent -->

        <!-- Transaction -->
        <dependency>
            <groupId>javax.transaction</groupId>
            <artifactId>javax.transaction-api</artifactId>
        </dependency>

        <!-- XML Binding (JAXB) -->
        <dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.activation</groupId>
            <artifactId>javax.activation-api</artifactId>
        </dependency>

        <!-- javax.annotation-api inherited from parent -->

        <!-- SpringDoc OpenAPI (modern replacement for Swagger) -->
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-ui</artifactId>
        </dependency>
    </dependencies>

</project>