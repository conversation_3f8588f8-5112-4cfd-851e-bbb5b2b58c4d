package com.spup.activity.dto;


import com.spup.data.entity.activity.ActivitySubmitCustomer;

import java.util.List;

public class ActivityRoundDTO {
    private String activityId;
    private String activityName;
    private ActivityRoundVo roundVo;
    private List<ActivitySubmitCustomer> submitCustomers;

    
    public String getActivityId() {
        return activityId;
    }
    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }
    public ActivityRoundVo getRoundVo() {
        return roundVo;
    }
    public void setRoundVo(ActivityRoundVo roundVo) {
        this.roundVo = roundVo;
    }
    public List<ActivitySubmitCustomer> getSubmitCustomers() {
        return submitCustomers;
    }
    public void setSubmitCustomers(List<ActivitySubmitCustomer> submitCustomers) {
        this.submitCustomers = submitCustomers;
    }

    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }
}
