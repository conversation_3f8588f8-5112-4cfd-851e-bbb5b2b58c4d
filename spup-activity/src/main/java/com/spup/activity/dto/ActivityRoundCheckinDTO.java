package com.spup.activity.dto;


import com.spup.data.entity.activity.Activity;
import com.spup.data.entity.activity.ActivitySubmitCustomer;

public class ActivityRoundCheckinDTO {
    private Activity activity;
    private ActivityRoundCheckinVo roundVo;
    private ActivitySubmitCustomer.SubmitCustomerStatusEnum status;

    public void setStatus(ActivitySubmitCustomer.SubmitCustomerStatusEnum status) {
        this.status = status;
    }
    public void setActivity(Activity activity) {
        this.activity = activity;
    }

    public Activity getActivity() {
        return activity;
    }

    public ActivitySubmitCustomer.SubmitCustomerStatusEnum getStatus() {
        return status;
    }

    public ActivityRoundCheckinVo getRoundVo() {
        return roundVo;
    }

    public void setRoundVo(ActivityRoundCheckinVo roundVo) {
        this.roundVo = roundVo;
    }
}
