package com.spup.activity.service;


import com.spup.activity.dto.ActivityRoundCheckinPojo;
import com.spup.commons.api.CommonResult;
import com.spup.data.entity.activity.Activity;
import com.spup.data.entity.activity.ActivityRound;

public interface ManageActivityService {
    CommonResult<?> checkActivityValid(Activity activity);
    CommonResult<?> checkActivityRoundValid(ActivityRound round);
    void checkInAction(ActivityRoundCheckinPojo checkinPojo);
}
