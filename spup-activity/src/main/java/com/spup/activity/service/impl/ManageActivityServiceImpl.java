package com.spup.activity.service.impl;

import com.spup.activity.dto.ActivityRoundCheckinPojo;
import com.spup.activity.service.ManageActivityService;
import com.spup.commons.api.CommonResult;
import com.spup.core.exception.ValidException;
import com.spup.data.dao.activity.ActivityRoundDao;
import com.spup.data.dao.activity.ActivitySubmitCustomerDao;
import com.spup.data.entity.activity.Activity;
import com.spup.data.entity.activity.ActivityRound;
import com.spup.data.entity.activity.ActivitySubmitCustomer;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
public class ManageActivityServiceImpl implements ManageActivityService {
    @Resource
    private ActivityRoundDao actRoundDao;
    @Resource
    private ActivitySubmitCustomerDao actCustomerDao;
    @Override
    public CommonResult<?> checkActivityValid(Activity activity) {
        if(activity.getStatus() == Activity.ActStatusEnum.DEPRECATED){
            return CommonResult.failed("活动已下线");
        }
        return CommonResult.succeeded("校验成功");
    }

    @Override
    public CommonResult<?> checkActivityRoundValid(ActivityRound round) {
        if(round == null) return CommonResult.failed("数据异常");
        LocalDateTime startDateTime = round.getActRoundStartDateTime();
        LocalDateTime endDateTime = round.getActRoundEndDateTime();
        if(startDateTime.isAfter(endDateTime)){
            return CommonResult.failed("场次时间设置错误");
        }
        LocalDate startDate = startDateTime.toLocalDate();
        LocalDate endDate = endDateTime.toLocalDate();
        if(!startDate.equals(endDate)){
            return CommonResult.failed("暂不支持多日的场次");
        }
        return CommonResult.succeeded("");
    }

    @Override
    public void checkInAction(ActivityRoundCheckinPojo checkinPojo) {
        Optional<ActivityRound> actRoundOpt = actRoundDao.findByActRoundId(checkinPojo.getActivityRoundId());
        if (!actRoundOpt.isPresent()) {
            throw new ValidException("活动场次未找到");
        }
        List<ActivitySubmitCustomer> customerList =
                actCustomerDao.findByActRoundIdAndSubmitIdAndUnionid(
                        checkinPojo.getActivityRoundId(),checkinPojo.getSubmitId(),checkinPojo.getUnionid());
        if (CollectionUtils.isEmpty(customerList)) {
            throw new ValidException("报名记录可能已被删除");
        }
        customerList.stream()
                .filter(customer -> customer.getStatus()!= ActivitySubmitCustomer.SubmitCustomerStatusEnum.CANCELLED)
                .filter(customer -> customer.getStatus()!= ActivitySubmitCustomer.SubmitCustomerStatusEnum.CHECKEDIN)
                .forEach(customer -> {
                    customer.setCheckInDateTime(LocalDateTime.now());
                    customer.setStatus(ActivitySubmitCustomer.SubmitCustomerStatusEnum.CHECKEDIN);
                    actCustomerDao.save(customer);
                });

    }
}
