package com.spup.activity.service;



import com.spup.data.entity.appointment.AppBatch;

import java.util.List;
import java.util.Map;

public interface ForActivityBatchService {
    AppBatch getByNo(String batchNo, Byte batchCategory);
    Map<String,List<AppBatch>> getListByDate(Byte category, String startDate, String endDate);
    List<AppBatch> getListByDate(Byte category, String date);
    List<AppBatch> getListByDate(String exhibitionNo,Byte category, String date);
    AppBatch save(AppBatch batch);
    AppBatch update(AppBatch batch);
    AppBatch updateRemaining(String batchNo,Byte batchCategory,int updateNum);
}
