package com.spup.activity.service.impl;

import com.spup.activity.dto.AppActivityEntryRuleDTO;
import com.spup.activity.service.IAppActivityEntryRuleService;
import com.spup.activity.service.IAppActivityEntryUserService;
import com.spup.data.dao.activity.AppActivityEntryRuleDao;
import com.spup.data.entity.activity.AppActivityEntryRule;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

@Service
public class AppActivityEntryRuleServiceImpl implements IAppActivityEntryRuleService {
    @Resource
    private AppActivityEntryRuleDao appActivityEntryRuleDao;

    @Resource
    private IAppActivityEntryUserService iAppActivityEntryUserService;

    @Override
    public AppActivityEntryRuleDTO getRuleByActivityId(Long activityId, String unionid) {

        List<AppActivityEntryRule> appActivityEntryRules = appActivityEntryRuleDao.findByActivityId(activityId);
        if(CollectionUtils.isEmpty(appActivityEntryRules)){
            return null;
        }
        AppActivityEntryRule appActivityEntryRule = appActivityEntryRules.get(0);
        AppActivityEntryRuleDTO dto = new AppActivityEntryRuleDTO();
        BeanUtils.copyProperties(appActivityEntryRule,dto);

        dto.setEntryRemind(dto.getEntryLimit()-iAppActivityEntryUserService.getActivityEntryInfo(activityId));
        dto.setMyEntry(iAppActivityEntryUserService.getMyEntryInfo(activityId,unionid));

        return dto;
    }
}
