<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.spup</groupId>
    <artifactId>spup-root</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>

    <name>SPUP Root</name>
    <description>SPUP Multi-module Project - Parent POM</description>

    <modules>
        <module>spup-common</module>
        <module>spup-data</module>
        <module>spup-core</module>
        <module>spup-activity</module>
        <module>spup-admin-web</module>
        <module>spup-user-web</module>
        <module>contract-tests</module>
    </modules>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.13</version>
        <relativePath /> <!-- lookup parent from repository -->
    </parent>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java8.home>/Library/Java/JavaVirtualMachines/jdk8</java8.home>

        <!-- Dependency versions -->


        <lombok.version>1.18.32</lombok.version>
        <mysql.version>8.0.33</mysql.version>  <!-- Updated to latest stable version -->
        <javax.annotation-api.version>1.3.2</javax.annotation-api.version>

        <hibernate-validator.version>6.2.0.Final</hibernate-validator.version>
        <hypersistence-utils.version>3.5.0</hypersistence-utils.version>
        <springdoc.version>1.7.0</springdoc.version>  <!-- Modern OpenAPI 3 documentation -->
        <jasypt.version>3.0.5</jasypt.version>
        <quartz.version>2.3.1</quartz.version>

        <jwt.version>3.3.0</jwt.version>
        <micrometer.version>1.10.1</micrometer.version>

        <poi.version>5.0.0</poi.version>
        <weixin-java.version>4.6.0</weixin-java.version>


        <!-- Plugin versions -->
        <maven-enforcer-plugin.version>3.0.0-M3</maven-enforcer-plugin.version>
        <maven-checkstyle-plugin.version>3.1.2</maven-checkstyle-plugin.version>
        <maven-surefire-plugin.version>3.0.0-M5</maven-surefire-plugin.version>
        <maven-compiler-plugin.version>3.10.1</maven-compiler-plugin.version>
    </properties>

    <!-- Dependencies that all modules will inherit -->
    <dependencies>
        <!-- Common dependencies for all modules -->

        <!-- Lombok - Code generation (used by all modules) -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- Javax Annotation API - @Resource, @PostConstruct, etc. (used by all modules) -->
        <dependency>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
            <version>${javax.annotation-api.version}</version>
        </dependency>

        <!-- SLF4J Logging API - Used by all modules for logging -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>

        <!-- Jackson dependencies are provided by Spring Boot starters automatically -->
        <!-- JSR310 module is included by default in Spring Boot 2.x -->

        <!-- Spring Context removed - modules get it through Spring Boot starters -->
        <!-- spup-common is now a pure utility module without Spring dependencies -->

        <!-- Common Testing Dependencies (used by all modules) -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <!-- Internal modules -->
            <dependency>
                <groupId>com.spup</groupId>
                <artifactId>spup-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.spup</groupId>
                <artifactId>spup-data</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.spup</groupId>
                <artifactId>spup-activity</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.spup</groupId>
                <artifactId>spup-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.spup</groupId>
                <artifactId>spup-user-web</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.spup</groupId>
                <artifactId>spup-admin-web</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- Spring Boot Dependencies are inherited from spring-boot-starter-parent -->
            <!-- No need to specify versions for Spring Boot starters -->

            <!-- Database (MySQL connector updated to latest stable) -->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>

            <!-- Hibernate (hibernate-core and hibernate-validator managed by Spring Boot) -->
            <dependency>
                <groupId>io.hypersistence</groupId>
                <artifactId>hypersistence-utils-hibernate-52</artifactId>
                <version>${hypersistence-utils.version}</version>
            </dependency>

            <!-- javax APIs removed - provided by Spring Boot starters -->
            <!-- javax.persistence-api: Provided by spring-boot-starter-data-jpa -->
            <!-- javax.transaction-api: Provided by spring-boot-starter-data-jpa -->
            <!-- javax.validation-api: Provided by spring-boot-starter-validation -->
            <!-- javax.servlet-api: Provided by spring-boot-starter-web -->
            <dependency>
                <groupId>javax.xml.bind</groupId>
                <artifactId>jaxb-api</artifactId>
                <version>2.3.1</version>
            </dependency>
            <dependency>
                <groupId>javax.activation</groupId>
                <artifactId>javax.activation-api</artifactId>
                <version>1.2.0</version>
            </dependency>

            <!-- Testing dependencies managed by spring-boot-starter-parent -->
            <!-- JUnit Jupiter and Mockito versions managed by Spring Boot parent -->

            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>${jwt.version}</version>
            </dependency>
            <!-- Apache HttpClient removed - not used, Spring provides RestTemplate/WebClient -->

            <!-- SpringDoc OpenAPI (modern replacement for Swagger) -->
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>

            <!-- thymeleaf version managed by spring-boot-starter-parent -->
            <!-- No explicit version management needed for Spring Boot starters -->

            <!-- Quartz Scheduler -->
            <dependency>
                <groupId>org.quartz-scheduler</groupId>
                <artifactId>quartz</artifactId>
                <version>${quartz.version}</version>
            </dependency>

            <!-- Jasypt Encryption -->
            <dependency>
                <groupId>com.github.ulisesbocchio</groupId>
                <artifactId>jasypt-spring-boot-starter</artifactId>
                <version>${jasypt.version}</version>
            </dependency>

            <!-- Metrics -->
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-core</artifactId>
                <version>${micrometer.version}</version>
            </dependency>

            <!-- Resilience4j removed - not used in codebase -->

            <!-- Apache POI -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <!-- WeChat MP -->
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-mp</artifactId>
                <version>${weixin-java.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <finalName>${project.artifactId}</finalName>
        <resources>
            <resource>
                <filtering>true</filtering>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*</include>
                </includes>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>${maven-enforcer-plugin.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <requireMavenVersion>
                                    <version>3.6.3</version>
                                </requireMavenVersion>
                            </rules>
                            <fail>true</fail>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>3.1.2</version>
                <dependencies>
                    <dependency>
                        <groupId>com.puppycrawl.tools</groupId>
                        <artifactId>checkstyle</artifactId>
                        <version>8.45.1</version>
                    </dependency>
                    <dependency>
                        <groupId>com.github.ngeor</groupId>
                        <artifactId>checkstyle-rules</artifactId>
                        <version>4.9.3</version>
                    </dependency>
                </dependencies>
                <configuration>
                    <configLocation>com/github/ngeor/checkstyle.xml</configLocation>
                    <includeTestSourceDirectory>true</includeTestSourceDirectory>
                    <skip>true</skip>
                </configuration>
                <executions>
                    <execution>
                        <?m2e ignore?>
                        <id>checkstyle</id>
                        <phase>deploy</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven-surefire-plugin.version}</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.10.1</version>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                    <fork>true</fork>
                    <executable>${java8.home}/Contents/Home/bin/javac</executable>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <!-- Development Profiles -->
    <profiles>
        <!-- Fast development profile -->
        <profile>
            <id>dev</id>
            <properties>
                <maven.test.skip>true</maven.test.skip>
                <maven.javadoc.skip>true</maven.javadoc.skip>
                <maven.source.skip>true</maven.source.skip>
                <checkstyle.skip>true</checkstyle.skip>
                <spotbugs.skip>true</spotbugs.skip>
                <jacoco.skip>true</jacoco.skip>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-compiler-plugin</artifactId>
                        <configuration>
                            <source>8</source>
                            <target>8</target>
                            <fork>true</fork>
                            <executable>${java8.home}/Contents/Home/bin/javac</executable>
                            <compilerArgs>
                                <arg>-Xlint:none</arg>
                                <arg>-nowarn</arg>
                            </compilerArgs>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <!-- Quick compile profile -->
        <profile>
            <id>quick</id>
            <properties>
                <maven.test.skip>true</maven.test.skip>
                <maven.javadoc.skip>true</maven.javadoc.skip>
                <maven.source.skip>true</maven.source.skip>
                <maven.install.skip>true</maven.install.skip>
                <maven.deploy.skip>true</maven.deploy.skip>
            </properties>
        </profile>

        <!-- Test-only profile -->
        <profile>
            <id>test-only</id>
            <properties>
                <maven.main.skip>true</maven.main.skip>
                <maven.install.skip>true</maven.install.skip>
                <maven.deploy.skip>true</maven.deploy.skip>
            </properties>
        </profile>
    </profiles>
</project>