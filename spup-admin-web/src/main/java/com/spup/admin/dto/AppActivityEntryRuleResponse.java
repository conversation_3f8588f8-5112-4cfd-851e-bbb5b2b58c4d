package com.spup.admin.dto;


import com.spup.data.entity.activity.AppActivityEntryRule;
import com.spup.data.entity.activity.AppActivityEntryUser;

public class AppActivityEntryRuleResponse extends AppActivityEntryRule {
    private int entryRemind;
    private AppActivityEntryUser myEntry;
    public int getEntryRemind() {
        return entryRemind;
    }

    public void setEntryRemind(int entryRemind) {
        this.entryRemind = entryRemind;
    }

    public AppActivityEntryUser getMyEntry() {
        return myEntry;
    }

    public void setMyEntry(AppActivityEntryUser myEntry) {
        this.myEntry = myEntry;
    }
}
