package com.spup.admin.dto;


import com.spup.data.entity.appointment.AppAppointmentOrder;
import com.spup.data.entity.appointment.AppAppointmentTeamOrder;
import com.spup.data.entity.authority.AppCustomer;

import lombok.Data;

import java.util.List;

@Data
public class CustomerDTO {
    private AppCustomer customer;
    private List<AppAppointmentOrder> orders;
    private List<AppAppointmentTeamOrder> teamOrders;
}
