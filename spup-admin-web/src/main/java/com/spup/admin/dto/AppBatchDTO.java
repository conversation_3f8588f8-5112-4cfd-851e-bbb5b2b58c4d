package com.spup.admin.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.spup.data.entity.appointment.AppBatchSet;

public class AppBatchDTO extends AppBatchSet {
    private String batchNo;
    @JsonIgnore
    private Byte batchCategory = 1;
    private Integer ticketTotal;

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public Integer getTicketTotal() {
        return ticketTotal;
    }

    public void setTicketTotal(Integer ticketTotal) {
        this.ticketTotal = ticketTotal;
    }

    @Override
    public Byte getBatchCategory() {
        return batchCategory;
    }

    @Override
    public void setBatchCategory(Byte batchCategory) {
        this.batchCategory = batchCategory;
    }
}
