package com.spup.admin.dto;


import com.spup.commons.api.PageInfo;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

public class PersonalOfflineListRequest extends PageInfo {
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }
}
