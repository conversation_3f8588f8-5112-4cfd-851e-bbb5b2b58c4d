package com.spup.admin.dto;


import com.spup.commons.api.PageInfo;
import com.spup.data.entity.appointment.AppAppointmentTeamOrder;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;

@Getter
@Setter
@ToString
public class AppTeamOrderListRequest extends PageInfo {
    private String exhibitionNo;
    private String teamName;
    private LocalDate startDate;
    private LocalDate endDate;
    private Short orderStatus;
    private AppAppointmentTeamOrder.MethodEnum method;

    // public String getExhibitionNo() {
    //     return exhibitionNo;
    // }

    // public void setExhibitionNo(String exhibitionNo) {
    //     this.exhibitionNo = exhibitionNo;
    // }

    // public LocalDate getStartDate() {
    //     return startDate;
    // }

    // public void setStartDate(LocalDate startDate) {
    //     this.startDate = startDate;
    // }

    // public LocalDate getEndDate() {
    //     return endDate;
    // }

    // public void setEndDate(LocalDate endDate) {
    //     this.endDate = endDate;
    // }

    // public Short getOrderStatus() {
    //     return orderStatus;
    // }

    // public void setOrderStatus(Short orderStatus) {
    //     this.orderStatus = orderStatus;
    // }

    // public String getTeamName() {
    //     return teamName;
    // }

    // public void setTeamName(String teamName) {
    //     this.teamName = teamName;
    // }

    // public AppAppointmentTeamOrder.MethodEnum getMethod() {
    //     return method;
    // }

    // public void setMethod(AppAppointmentTeamOrder.MethodEnum method) {
    //     this.method = method;
    // }
}
