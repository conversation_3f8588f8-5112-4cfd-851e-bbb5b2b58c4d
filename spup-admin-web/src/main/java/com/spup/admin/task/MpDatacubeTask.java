package com.spup.admin.task;

import com.spup.admin.mp.entity.datacube.Article;
import com.spup.admin.mp.entity.datacube.ArticleDailyDetail;
import com.spup.admin.mp.entity.datacube.UserSummary;
import com.spup.admin.mp.service.IMpDatacubeSerivce;
import com.spup.admin.service.analysis.IMpDatacubeArticleSummaryService;
import com.spup.admin.service.analysis.IMpDatacubeUserSummaryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;


@Configuration      //1.主要用于标记配置类，兼备Component的效果。
@EnableScheduling   // 2.开启定时任务
public class MpDatacubeTask {
    private static final Logger logger = LoggerFactory.getLogger(MpDatacubeTask.class);
    @Resource
    protected IMpDatacubeSerivce iMpDatacubeSerivce;
    @Resource
    private IMpDatacubeArticleSummaryService articleSummaryService;
    @Resource
    private IMpDatacubeUserSummaryService userSummaryService;
    //3.添加定时任务
    @Scheduled(cron = "0 30 8 * * ?")
    protected void configureTasks() throws Exception {
        logger.info("执行公众号每日汇总数据");
        LocalDate date = LocalDate.now();
        date = date.minusDays(1);

        List<Article> newArticleList = iMpDatacubeSerivce.getArticleTotal(date);
        List<ArticleDailyDetail> dailyDetailList = iMpDatacubeSerivce.getArticleSummary(date);
        articleSummaryService.saveList(newArticleList);
        articleSummaryService.updateList(dailyDetailList);

        Integer userCumulate = iMpDatacubeSerivce.getUserCumulate(date);
        List<UserSummary> userSummary1 = iMpDatacubeSerivce.getUserSummary(date);
        userSummaryService.save(date,userCumulate,userSummary1);

    }

}