package com.spup.admin;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

import com.spup.admin.config.WeChatProperties;

@SpringBootApplication
@ComponentScan({"com.spup.admin", "com.spup.core", "com.spup.data", "com.spup.activity"})
@EnableJpaRepositories({"com.spup.data"})
@EntityScan({"com.spup.data"})
@EnableConfigurationProperties(WeChatProperties.class)
public class SpupAdmin extends SpringBootServletInitializer {

    public static void main(String[] args) {
        SpringApplication.run(SpupAdmin.class, args);
    }

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
        return builder.sources(SpupAdmin.class);
    }
}
