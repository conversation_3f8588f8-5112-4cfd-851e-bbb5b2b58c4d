package com.spup.admin.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import lombok.Getter;
import lombok.Setter;

@Configuration
@ConfigurationProperties(prefix = "wx")
@Getter
@Setter
public class WeChatProperties {
    
    private MpConfig mp;
    private OpenConfig open;
    
    @Getter
    @Setter
    public static class MpConfig {
        private String appId;
        private String secret;
    }
    
    @Getter
    @Setter
    public static class OpenConfig {
        private String appId;
        private String secret;
    }
}