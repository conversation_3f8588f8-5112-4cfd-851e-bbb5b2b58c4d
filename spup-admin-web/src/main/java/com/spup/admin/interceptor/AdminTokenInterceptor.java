package com.spup.admin.interceptor;

import com.auth0.jwt.interfaces.DecodedJWT;
import com.spup.commons.api.CommonResult;
import com.spup.commons.api.ResultCodeEnum;
import com.spup.commons.utils.JWTUtil;
import com.spup.core.interceptor.BaseTokenInterceptor;
import com.spup.data.entity.authority.AppManageUser;
import com.spup.admin.service.IAppManageUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * Admin Token Interceptor
 * Extends BaseTokenInterceptor with admin-specific validation logic
 */
@Slf4j
@Component
public class AdminTokenInterceptor extends BaseTokenInterceptor {

    // JW<PERSON>til is now a static utility class - no injection needed

    @Value("${spring.profiles.active:dev}")
    private String activeProfile;

    @Value("${manager.unionid: ojqzL0-hOlek3HMyLjhvKjTfDnnA}")
    private String managerUnionid;

    @Resource
    private IAppManageUserService manageUserService;

    /**
     * Admin-specific development mode detection
     */
    @Override
    protected boolean isDevelopmentMode(HttpServletRequest request) {
        return request.getServerName().contains("localhost") && !"pro".equals(activeProfile);
    }

    /**
     * Set default admin session for development
     */
    @Override
    protected void setDefaultDevelopmentSession(HttpServletRequest request) {
        request.getSession().setAttribute("unionid", managerUnionid);
        log.debug("Set default admin development session with unionid: {}", managerUnionid);
    }

    /**
     * Admin-specific token validation with manage user verification
     */
    @Override
    protected boolean validateTokenAndSetSession(String token, 
                                                HttpServletRequest request, 
                                                HttpServletResponse response) throws Exception {
        try {
            DecodedJWT decodedJWT = JWTUtil.decodeToken(token);

            String unionid = decodedJWT.getClaim("unionid").asString();
            String openid = decodedJWT.getClaim("openid").asString();

            if (unionid == null || unionid.isEmpty()) {
                log.warn("Token missing unionid claim");
                writeErrorResponse(response, CommonResult.failed(ResultCodeEnum.JWT_TOKEN_FAILED));
                return false;
            }

            log.debug("Admin token validation for unionid: {}", unionid);

            // Update session if needed
            String sessionUnionid = (String) request.getSession().getAttribute("unionid");
            if (!unionid.equals(sessionUnionid)) {
                request.getSession().setAttribute("unionid", unionid);
                request.getSession().setAttribute("openid", openid);
            }

            // Admin-specific: Verify user exists in manage user table
            AppManageUser manageUser = manageUserService.getUserByUnionid(unionid);
            if (manageUser == null) {
                log.warn("Admin user not found for unionid: {}", unionid);
                writeErrorResponse(response, CommonResult.failed(ResultCodeEnum.AUTHORIZED_FAILED));
                return false;
            }

            log.debug("Admin user validation successful: {}", manageUser.getName());
            
            // Set additional admin session attributes
            setAdditionalSessionAttributes(decodedJWT, request);
            
            return true;

        } catch (Exception e) {
            log.error("Admin token validation failed:", e);
            writeErrorResponse(response, getAdminErrorResult(e));
            return false;
        }
    }

    /**
     * Set additional admin-specific session attributes
     */
    @Override
    protected void setAdditionalSessionAttributes(DecodedJWT decodedJWT, HttpServletRequest request) {
        // Add admin-specific session attributes if needed
        String openid = decodedJWT.getClaim("openid").asString();
        if (openid != null) {
            request.getSession().setAttribute("openid", openid);
        }
    }

    /**
     * Get admin-specific error result based on exception type
     */
    private CommonResult<?> getAdminErrorResult(Exception e) {
        if (e instanceof com.auth0.jwt.exceptions.SignatureVerificationException) {
            log.warn("Invalid JWT signature");
            return CommonResult.failed(ResultCodeEnum.GET_TOKEN_KEY_FAILED);
        } else if (e instanceof com.auth0.jwt.exceptions.TokenExpiredException) {
            log.warn("JWT token expired");
            return CommonResult.failed(ResultCodeEnum.AUTHORIZED_FAILED);
        } else if (e instanceof com.auth0.jwt.exceptions.AlgorithmMismatchException) {
            log.warn("JWT algorithm mismatch");
            return CommonResult.failed(ResultCodeEnum.JWT_TOKEN_EXPIRE);
        } else {
            log.warn("JWT token validation failed: {}", e.getMessage());
            return CommonResult.failed(ResultCodeEnum.JWT_TOKEN_EXPIRE);
        }
    }
}
