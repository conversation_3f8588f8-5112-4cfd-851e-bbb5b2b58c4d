package com.spup.admin.controller.appointment;

import com.spup.commons.api.CommonResult;
import com.spup.admin.dto.WorkDayTempVo;
import com.spup.admin.service.appointment.IAppWorkdayService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@Tag(name = "展览开闭管理")
@RestController
@RequestMapping(value = {"/workday/temp","/workday/exhibition"})
public class AppWorkDayTempController {
    @Resource
    private IAppWorkdayService iAppWorkdayService;

    @Operation(summary = "查询开放日期情况")
    @GetMapping(value="/getWorkDays/{exhibitionNo}/{startDate}_{endDate}")
    public CommonResult<?> getWorkDays (@PathVariable String exhibitionNo, @PathVariable String startDate, @PathVariable String endDate){
        return CommonResult.succeeded(iAppWorkdayService.getListByDateOfTemp(exhibitionNo, startDate, endDate));
    }

    @Operation(summary = "开启/关闭")
    @PostMapping(value="/setWorkDay")
    public CommonResult<?> setWorkDay (@RequestBody WorkDayTempVo query, HttpServletRequest req) {
        String day = query.getDay();
        Integer status = query.getStatus();
        String remark = query.getRemark();
        String unionid = (String)req.getSession().getAttribute("unionid");
        String exhibitionNo = query.getExhibitionNo();
        return CommonResult.succeeded(iAppWorkdayService.setWorkDayOfTemp(exhibitionNo,day,status,remark,unionid));
    }

    @Operation(summary = "批量开启/关闭日期开放")
    @PostMapping(value="/setWorkDays")
    public CommonResult<?> setWorkDays (@RequestBody WorkDayTempVo query, HttpServletRequest req)  {
        String days = query.getDays();
        Integer status = query.getStatus();
        String remark = query.getRemark();
        String exhibitionNo = query.getExhibitionNo();
        String unionid = (String)req.getSession().getAttribute("unionid");
        String[] day_array = days.split(",");
        for (String day : day_array) {
            iAppWorkdayService.setWorkDayOfTemp(exhibitionNo, day, status, remark, unionid);
        }
        return CommonResult.succeeded("");
    }

    @Operation(summary = "添加临时场次")
    @PostMapping(value="/addTempBatch")
    public CommonResult<?> addTempBatch (@RequestBody WorkDayTempVo query, HttpServletRequest req)  {
        String days = query.getDays();
        Integer status = query.getStatus();
        String remark = query.getRemark();
        String exhibitionNo = query.getExhibitionNo();
        String unionid = (String)req.getSession().getAttribute("unionid");
        String[] day_array = days.split(",");
        for (String day : day_array) {
            iAppWorkdayService.setWorkDayOfTemp(exhibitionNo, day, status, remark, unionid);
        }
        return CommonResult.succeeded("");
    }

    @Operation(summary = "删除临时场次")
    @PostMapping(value="/delTempBatch")
    public CommonResult<?> delTempBatch (@RequestBody WorkDayTempVo query, HttpServletRequest req)  {
        String days = query.getDays();
        Integer status = query.getStatus();
        String remark = query.getRemark();
        String exhibitionNo = query.getExhibitionNo();
        String unionid = (String)req.getSession().getAttribute("unionid");
        String[] day_array = days.split(",");
        for (String day : day_array) {
            iAppWorkdayService.setWorkDayOfTemp(exhibitionNo, day, status, remark, unionid);
        }
        return CommonResult.succeeded("");
    }

}
