package com.spup.admin.controller.analysis;

import com.spup.commons.api.CommonResult;
import com.spup.data.dao.appointment.AppAppointmentTeamOrderDao;
import com.spup.admin.dto.AnalysisListRequest;
import com.spup.admin.dto.AppTeamOrderListRequest;
import com.spup.admin.service.analysis.IAppAppointmentAnalysisService;
import com.spup.admin.service.appointment.IAppAppointmentTeamOrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@Tag(name = "统计")
@RestController
@Slf4j
@RequestMapping(value = "/analysis")
public class AppAnalysisController {
    @Resource
    private IAppAppointmentAnalysisService iAppAppointmentAnalysisService;
    @Resource
    private AppAppointmentTeamOrderDao appAppointmentTeamOrderDao;
    @Resource
    private IAppAppointmentTeamOrderService iAppAppointmentTeamOrderService;

    @Operation(summary = "根据日期全量查询统计数据")
    @GetMapping(value = { "/getAnalysisByDate/{startDate}_{endDate}", "/getAnalysisByDate" })
    public CommonResult<?> getAnalysisByDate(@PathVariable String startDate, @PathVariable String endDate)
            throws UnsupportedEncodingException {
        if (!StringUtils.hasLength(startDate)) {
            startDate = "2023-04-01";
        }
        if (!StringUtils.hasLength(endDate)) {
            endDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
        return CommonResult.succeeded(iAppAppointmentAnalysisService.getAnaDataByDate(startDate, endDate));
    }

    @Operation(summary = "根据日期分页查询统计数据")
    @PostMapping(value = "/list/byPage")
    public CommonResult<?> listByPage(@RequestBody AnalysisListRequest param) {
        return CommonResult.succeeded(iAppAppointmentAnalysisService.listByPage(param));
    }

    @Operation(summary = "根据日期查询团体预约数据")
    @GetMapping(value = { "/getTeamData/{startDate}_{endDate}", "/getTeamData" })
    public CommonResult<?> getTeamData(@PathVariable String startDate, @PathVariable String endDate)
            throws UnsupportedEncodingException {
        if (!StringUtils.hasLength(startDate)) {
            startDate = "2023-04-01";
        }
        if (!StringUtils.hasLength(endDate)) {
            endDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
        return CommonResult.succeeded(appAppointmentTeamOrderDao.findByBatchDateBetween(startDate, endDate));
    }

    @Operation(summary = "根据日期查询团体预约数据(分页)")
    @PostMapping(value = "/getTeamDataByPage")
    public CommonResult<?> getTeamDataByPage(@RequestBody AppTeamOrderListRequest queryParam)
            throws UnsupportedEncodingException {
        log.info("queryParam -> " + queryParam.toString());
        return CommonResult.succeeded(iAppAppointmentTeamOrderService.getList(queryParam));
    }
}
