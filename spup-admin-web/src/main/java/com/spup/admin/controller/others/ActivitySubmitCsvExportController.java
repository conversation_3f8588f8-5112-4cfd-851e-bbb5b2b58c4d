package com.spup.admin.controller.others;

import com.spup.commons.api.CommonResult;
import com.spup.core.controller.BaseController;
import com.spup.data.dao.activity.ActivityDao;
import com.spup.data.dao.activity.ActivityRoundDao;
import com.spup.data.dao.activity.ActivitySubmitCustomerDao;
import com.spup.data.entity.activity.Activity;
import com.spup.data.entity.activity.ActivityRound;
import com.spup.data.entity.activity.ActivitySubmitCustomer;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Admin Controller for exporting ActivitySubmit data to CSV format
 */
@Tag(name = "管理员-活动报名数据CSV导出")
@RestController
@RequestMapping("/admin/activity-submit-csv-export")
@Slf4j
public class ActivitySubmitCsvExportController extends BaseController {

    @Autowired
    private ActivitySubmitCustomerDao activitySubmitCustomerDao;

    @Autowired
    private ActivityDao activityDao;

    @Autowired
    private ActivityRoundDao activityRoundDao;

    private static final DateTimeFormatter CSV_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final DateTimeFormatter FILENAME_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");

    @Operation(summary = "测试控制器是否工作")
    @GetMapping("/test")
    public CommonResult<String> testController() {
        log.info("=== TEST CONTROLLER CALLED ===");
        return success("Controller is working! Time: " + LocalDateTime.now());
    }

    @Operation(summary = "简单导出测试")
    @GetMapping("/simple-export")
    public ResponseEntity<String> simpleExport() {
        log.info("=== SIMPLE EXPORT CALLED ===");
        String csvContent = "主键,活动编号,活动名称\n1,ACT001,测试活动\n";

        return ResponseEntity.ok()
                .header("Content-Type", "text/csv; charset=utf-8")
                .header("Content-Disposition", "attachment; filename=test.csv")
                .body(csvContent);
    }

    @Operation(summary = "导出活动报名数据为CSV文件")
    @GetMapping("/export")
    public ResponseEntity<byte[]> exportActivitySubmitToCsv(
            @Parameter(description = "开始时间", required = true, example = "2024-01-01T00:00:00")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss") LocalDateTime startDateTime,

            @Parameter(description = "结束时间", required = true, example = "2024-12-31T23:59:59")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss") LocalDateTime endDateTime) {

        log.info("=== EXPORT METHOD CALLED ===");
        log.info("Parameters: startDateTime={}, endDateTime={}", startDateTime, endDateTime);

        try {
            logRequest("导出活动报名数据为CSV文件",
                String.format("startDateTime=%s, endDateTime=%s", startDateTime, endDateTime));

            // Validate date range
            if (startDateTime.isAfter(endDateTime)) {
                log.warn("Invalid date range: start {} is after end {}", startDateTime, endDateTime);
                return ResponseEntity.badRequest()
                        .body("开始时间不能晚于结束时间".getBytes(StandardCharsets.UTF_8));
            }

            // Get data for export
            List<ActivitySubmitCustomer> customers = getActivitySubmitDataForExport(startDateTime, endDateTime);

            if (customers.isEmpty()) {
                log.info("No data found for date range {} to {}", startDateTime, endDateTime);
                return ResponseEntity.ok()
                        .header(HttpHeaders.CONTENT_TYPE, "text/plain; charset=utf-8")
                        .body("指定时间范围内没有找到数据".getBytes(StandardCharsets.UTF_8));
            }

            // Generate CSV content
            String csvContent = generateCsvContent(customers);
            
            // Generate filename
            String filename = generateCsvFilename(startDateTime, endDateTime);

            log.info("Generated CSV export with {} records, filename: {}", customers.size(), filename);

            // Set response headers for file download
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", filename);
            headers.add("Content-Encoding", "utf-8");
            headers.add("Cache-Control", "no-cache, no-store, must-revalidate");
            headers.add("Pragma", "no-cache");
            headers.add("Expires", "0");

            // Convert to bytes with UTF-8 encoding (with BOM for Excel compatibility)
            byte[] csvBytes = ("\uFEFF" + csvContent).getBytes(StandardCharsets.UTF_8);

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(csvBytes);

        } catch (Exception e) {
            log.error("Error exporting activity submit data to CSV", e);
            return ResponseEntity.badRequest()
                    .body(("导出失败: " + e.getMessage()).getBytes(StandardCharsets.UTF_8));
        }
    }

    @Operation(summary = "预览活动报名数据")
    @GetMapping("/preview")
    public CommonResult<String> previewActivitySubmitData(
            @Parameter(description = "开始时间", required = true, example = "2024-01-01T00:00:00")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss") LocalDateTime startDateTime,

            @Parameter(description = "结束时间", required = true, example = "2024-12-31T23:59:59")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss") LocalDateTime endDateTime,

            @Parameter(description = "限制返回条数")
            @RequestParam(defaultValue = "10") int limit) {
        
        try {
            logRequest("预览活动报名数据", 
                String.format("startDateTime=%s, endDateTime=%s, limit=%d", startDateTime, endDateTime, limit));

            // Validate date range
            if (startDateTime.isAfter(endDateTime)) {
                return failed("开始时间不能晚于结束时间");
            }

            // Get data for preview
            List<ActivitySubmitCustomer> customers = getActivitySubmitDataForExport(startDateTime, endDateTime);

            // Limit results for preview
            if (customers.size() > limit) {
                customers = customers.subList(0, limit);
            }

            // Generate CSV preview
            String csvPreview = generateCsvContent(customers);

            log.info("Preview returned {} records (limited to {})", customers.size(), limit);
            return success(csvPreview);

        } catch (Exception e) {
            log.error("Error previewing activity submit data", e);
            return failed("预览失败: " + e.getMessage());
        }
    }

    /**
     * Get activity submit data by date range for CSV export
     */
    private List<ActivitySubmitCustomer> getActivitySubmitDataForExport(LocalDateTime startDateTime, LocalDateTime endDateTime) {
        log.info("Fetching activity submit data for export from {} to {}", startDateTime, endDateTime);
        
        // Get all activity submit customers and filter by date range
        List<ActivitySubmitCustomer> customers = activitySubmitCustomerDao.findAll().stream()
                .filter(customer -> customer.getCreateOn() != null)
                .filter(customer -> !customer.getCreateOn().isBefore(startDateTime))
                .filter(customer -> !customer.getCreateOn().isAfter(endDateTime))
                .sorted((c1, c2) -> c2.getCreateOn().compareTo(c1.getCreateOn())) // Sort by createOn desc
                .collect(Collectors.toList());

        log.info("Found {} activity submit records in date range", customers.size());
        return customers;
    }

    /**
     * Generate CSV content from ActivitySubmitCustomer list
     */
    private String generateCsvContent(List<ActivitySubmitCustomer> customers) {
        StringBuilder csvContent = new StringBuilder();
        
        // Add headers in specified order
        String[] headers = {
                "主键", "活动编号", "活动名称", "场次编号", "场次名称", 
                "场次开始时间", "场次结束时间", "报名人姓名", "报名人年龄", "报名人性别",
                "报名人证件类型", "报名人证件号", "报名人联系方式", "创建时间", "修改时间"
        };
        csvContent.append(String.join(",", headers)).append("\n");
        
        // Add data rows
        for (ActivitySubmitCustomer customer : customers) {
            csvContent.append(buildCsvRow(customer)).append("\n");
        }
        
        return csvContent.toString();
    }

    /**
     * Generate CSV filename with timestamp
     */
    private String generateCsvFilename(LocalDateTime startDateTime, LocalDateTime endDateTime) {
        String startStr = startDateTime.format(FILENAME_DATE_FORMATTER);
        String endStr = endDateTime.format(FILENAME_DATE_FORMATTER);
        return String.format("activity_submit_export_%s_to_%s.csv", startStr, endStr);
    }

    /**
     * Build CSV row from ActivitySubmitCustomer
     */
    private String buildCsvRow(ActivitySubmitCustomer customer) {
        // Get activity and round info
        String activityId = "";
        String activityName = "";
        String actRoundInfo = "";
        String actRoundStartDateTime = "";
        String actRoundEndDateTime = "";
        
        if (customer.getActRoundId() != null) {
            Optional<ActivityRound> actRoundOpt = activityRoundDao.findByActRoundId(customer.getActRoundId());
            if (actRoundOpt.isPresent()) {
                ActivityRound actRound = actRoundOpt.get();
                actRoundInfo = actRound.getActRoundInfo() != null ? actRound.getActRoundInfo() : "";
                actRoundStartDateTime = formatDateTime(actRound.getActRoundStartDateTime());
                actRoundEndDateTime = formatDateTime(actRound.getActRoundEndDateTime());
                
                // Get activity info
                Optional<Activity> activityOpt = activityDao.getByActivityId(actRound.getActivityId());
                if (activityOpt.isPresent()) {
                    Activity activity = activityOpt.get();
                    activityId = activity.getActivityId() != null ? activity.getActivityId() : "";
                    activityName = activity.getActivityName() != null ? activity.getActivityName() : "";
                }
            }
        }
        
        return String.join(",",
                escapeCsvValue(customer.getId()),
                escapeCsvValue(activityId),
                escapeCsvValue(activityName),
                escapeCsvValue(customer.getActRoundId()),
                escapeCsvValue(actRoundInfo),
                escapeCsvValue(actRoundStartDateTime),
                escapeCsvValue(actRoundEndDateTime),
                escapeCsvValue(customer.getUsername()),
                escapeCsvValue(customer.getAge()),
                escapeCsvValue(formatGender(customer.getGender())),
                escapeCsvValue(formatPassType(customer.getPassType())),
                escapeCsvValue(customer.getPassString()),
                escapeCsvValue(customer.getPhoneString()),
                escapeCsvValue(formatDateTime(customer.getCreateOn())),
                escapeCsvValue(formatDateTime(customer.getUpdateOn()))
        );
    }

    /**
     * Escape CSV values to handle commas, quotes, and newlines
     */
    private String escapeCsvValue(Object value) {
        if (value == null) {
            return "";
        }
        
        String stringValue = value.toString();
        
        // If the value contains comma, quote, or newline, wrap it in quotes and escape internal quotes
        if (stringValue.contains(",") || stringValue.contains("\"") || stringValue.contains("\n")) {
            stringValue = "\"" + stringValue.replace("\"", "\"\"") + "\"";
        }
        
        return stringValue;
    }

    /**
     * Format LocalDateTime for CSV
     */
    private String formatDateTime(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.format(CSV_DATE_FORMATTER) : "";
    }

    /**
     * Format gender for CSV
     */
    private String formatGender(Integer gender) {
        if (gender == null) return "未知";
        switch (gender) {
            case 1: return "男";
            case 2: return "女";
            default: return "未知";
        }
    }

    /**
     * Format pass type for CSV
     */
    private String formatPassType(ActivitySubmitCustomer.SubmitCustomerPassTypeEnum passType) {
        if (passType == null) return "未知";
        switch (passType) {
            case IDCARD: return "身份证";
            case PASSPORT: return "护照";
            case OTHER: return "其他";
            default: return passType.name();
        }
    }
}
