package com.spup.admin.controller.others;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.spup.commons.api.CommonResult;
import com.spup.data.entity.AppVisitGuide;
import com.spup.admin.dto.VisitGuideListRequest;
import com.spup.admin.service.IAppVisitGuideService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.UnsupportedEncodingException;

@Tag(name = "线上导览管理")
@RestController
@RequestMapping(value = "/visitGuide")
public class AppVisitGuideController {
    @Resource
    private IAppVisitGuideService iAppVisitGuideService;

    @Resource
    private ObjectMapper objectMapper;

    @Operation(summary = "查询列表（分页）")
    @GetMapping(value = "/listByPage")
    public CommonResult<?> listByPage(VisitGuideListRequest param) throws UnsupportedEncodingException {
        return CommonResult.succeeded(iAppVisitGuideService.getListByPage(param));
    }

    @Operation(summary = "创建线上导览点位", description = "showImgs与show_voices采用jsonArray字符串格式")
    @PostMapping(value = "/createSpot")
    public CommonResult<?> createSpot(@RequestBody AppVisitGuide appVisitGuide, HttpServletRequest req)
            throws UnsupportedEncodingException {
        String openid = (String) req.getSession().getAttribute("openid");
        iAppVisitGuideService.create(appVisitGuide, openid);
        return CommonResult.succeeded(appVisitGuide);
    }

    @Operation(summary = "删除线上导览点位")
    @GetMapping(value = "/deleteSpot/{id}")
    public CommonResult<?> deleteSpot(@PathVariable Long id, HttpServletRequest req)
            throws UnsupportedEncodingException {
        String openid = (String) req.getSession().getAttribute("openid");
        return CommonResult.succeeded(iAppVisitGuideService.delete(id, openid));
    }

    @Operation(summary = "更新线上导览点位", description = "id为必传字段")
    @PostMapping(value = "/updateSpot")
    public CommonResult<?> updateSpot(@RequestBody AppVisitGuide appVisitGuide, HttpServletRequest req)
            throws UnsupportedEncodingException {
        String openid = (String) req.getSession().getAttribute("openid");

        return CommonResult.succeeded(iAppVisitGuideService.update(appVisitGuide, openid));
    }

    @Operation(summary = "查看线上导览点位")
    @GetMapping(value = "/viewSpot/{id}")
    public CommonResult<?> viewSpot(@PathVariable Long id, HttpServletRequest reqServletRequest) throws IOException {

        AppVisitGuide view = iAppVisitGuideService.view(id);

        ObjectNode node = (ObjectNode) objectMapper.valueToTree(view);

        /*
         * if(StringUtils.hasLength(view.getShowImgs())){
         * node.put("showImgs",objectMapper.readTree(view.getShowImgs()));
         * }
         * if(StringUtils.hasLength(view.getShowVoices())){
         * node.put("showVoices",objectMapper.readTree(view.getShowVoices()));
         * }
         */
        if (StringUtils.hasLength(view.getTips())) {
            node.set("tips", objectMapper.readTree(view.getTips()));
        }
        return CommonResult.succeeded(node);
    }

}
