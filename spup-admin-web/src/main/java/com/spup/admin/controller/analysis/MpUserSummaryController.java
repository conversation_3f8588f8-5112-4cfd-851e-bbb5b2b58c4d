package com.spup.admin.controller.analysis;

import com.spup.commons.api.CommonResult;
import com.spup.data.entity.mp.MpDatacubeArticleSummary;
import com.spup.data.entity.mp.MpDatacubeUserSummary;
import com.spup.admin.dto.DateQueryRequest;
import com.spup.admin.service.analysis.IMpDatacubeArticleSummaryService;
import com.spup.admin.service.analysis.IMpDatacubeUserSummaryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Tag(name = "公众号数据汇总")
@RestController
@RequestMapping(value = "/summary/mp")
public class MpUserSummaryController {

    @Resource
    private IMpDatacubeArticleSummaryService articleSummaryService;
    @Resource
    private IMpDatacubeUserSummaryService userSummaryService;

    @Operation(summary = "获取用户统计")
    @PostMapping(value = "/getUserSummary")
    public CommonResult<?> getUserSummary(@RequestBody DateQueryRequest queryParam)  {
        Page<MpDatacubeUserSummary> listByPage = userSummaryService.getListByPage(queryParam);
        return CommonResult.succeeded(listByPage);
    }
    @Operation(summary = "获取文章统计")
    @PostMapping(value = "/getArticleSummary")
    public CommonResult<?> getArticleSummary(@RequestBody DateQueryRequest queryParam) {
        Page<MpDatacubeArticleSummary> listByPage = articleSummaryService.getListByPage(queryParam);
        return CommonResult.succeeded(listByPage);
    }


}
