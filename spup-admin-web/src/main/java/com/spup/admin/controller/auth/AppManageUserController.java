package com.spup.admin.controller.auth;

import com.spup.commons.api.CommonResult;
import com.spup.data.entity.authority.AppManageUser;
import com.spup.admin.dto.ManagerUserListRequest;
import com.spup.admin.dto.auth.ManageUserDto;
import com.spup.admin.service.IAppManageUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;

@Tag(name = "统一认证管理")
@RestController
@RequestMapping(value = "/manageUser")
public class AppManageUserController {
    @Resource
    private IAppManageUserService iAppManageUserService;

    @Operation(summary = "查询列表（分页）")
    @GetMapping(value = "/listByPage")
    public CommonResult<?> listByPage(ManagerUserListRequest param) throws UnsupportedEncodingException {
        return CommonResult.succeeded(iAppManageUserService.getListByPage(param));
    }

    @Operation(summary = "创建后台管理员", description = "创建用户时，只传输用户基本信息即可。menuCode，采用英文逗号分隔")
    @PostMapping(value = "/createManageUser")
    public CommonResult<?> createManageUser(@RequestBody ManageUserDto dto) throws UnsupportedEncodingException {
        AppManageUser manageUserInDb = iAppManageUserService.getUserByUnionid(dto.getUnionid());
        if (manageUserInDb != null) {
            return CommonResult.failed("用户已存在");
        }
        AppManageUser manageUser = new AppManageUser();
        BeanUtils.copyProperties(dto, manageUser);
        AppManageUser result = iAppManageUserService.create(manageUser);
        return CommonResult.succeeded(result);
    }

    @Operation(summary = "删除后台管理员")
    @GetMapping(value = "/deleteManageUser/{id}")
    public CommonResult<?> deleteManageUser(@PathVariable Long id) {
        return CommonResult.succeeded(iAppManageUserService.delete(id));
    }

    @Operation(summary = "更新后台管理员", description = "id为必传字段，此接口支持编辑接口，启用，禁用（status：1启用；2禁用）")
    @PostMapping(value = "/updateManageUser")
    public CommonResult<?> updateManageUser(@RequestBody AppManageUser appManageUser) {
        return CommonResult.succeeded(iAppManageUserService.update(appManageUser));
    }

    @Operation(summary = "查看后台管理员")
    @GetMapping(value = "/viewManageUser/{id}")
    public CommonResult<?> viewManageUser(@PathVariable Long id) throws UnsupportedEncodingException {

        return CommonResult.succeeded(iAppManageUserService.view(id));
    }

}
