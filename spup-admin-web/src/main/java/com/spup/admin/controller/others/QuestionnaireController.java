package com.spup.admin.controller.others;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.spup.commons.api.CommonResult;
import com.spup.data.entity.CommQuestionnaireAnswer;
import com.spup.admin.dto.QuestionnaireListRequest;
import com.spup.admin.service.ICommQuestionnaireAnswerService;
import com.spup.admin.service.ICommQuestionnaireService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.data.domain.Page;

import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Tag(name = "问卷管理")
@RestController
@RequestMapping(value = "/questionnaire")
public class QuestionnaireController {
    @Resource
    private ICommQuestionnaireService iCommQuestionnaireService;
    @Resource
    private ICommQuestionnaireAnswerService iCommQuestionnaireAnswerService;
    @Resource
    private ObjectMapper objectMapper;

    @Operation(summary = "问卷列表(分页)")
    @PostMapping(value="/listByPage")
    public CommonResult<?> listByPage (@RequestBody QuestionnaireListRequest param) {
        return CommonResult.succeeded(iCommQuestionnaireService.getPageList(param));
    }

    @Operation(summary = "查看问卷填报（分页）")
    @GetMapping(value={"/viewAnswer/"})
    public CommonResult<?> viewAnswer (@PathVariable Long questionnaireId,QuestionnaireListRequest param) {
        if(questionnaireId==null){
            questionnaireId = 1L;
        }
        if(param.getQuestionnaireId()==null){
            param.setQuestionnaireId(questionnaireId);
        }
        Page<CommQuestionnaireAnswer> pageList = iCommQuestionnaireAnswerService.getPageList(param);
        return CommonResult.succeeded(pageList);
    }
    @Operation(summary = "查看问卷填报（分页）")
    @PostMapping(value={"/viewAnswer/{questionnaireId}"})
    public CommonResult<?> viewAnswer2 (@PathVariable Long questionnaireId, @RequestBody QuestionnaireListRequest param) {
        if(questionnaireId==null){
            questionnaireId = 1L;
        }
        if(param.getQuestionnaireId()==null){
            param.setQuestionnaireId(questionnaireId);
        }
        Page<CommQuestionnaireAnswer> pageList = iCommQuestionnaireAnswerService.getPageList(param);
        return CommonResult.succeeded(pageList);
    }

    @Operation(summary = "查看问卷填报（分页）")
    @GetMapping(value={"/viewAnswer"})
    public CommonResult<?> viewAnswer3 (@RequestBody QuestionnaireListRequest param) {
        Page<CommQuestionnaireAnswer> pageList = iCommQuestionnaireAnswerService.getPageList(param);
        return CommonResult.succeeded(pageList);
    }
    @Operation(summary = "查看问卷填报详情")
    @GetMapping(value="/viewAnswerDetail/{answerId}")
    public CommonResult<?> viewAnswerDetail (@PathVariable long answerId) throws JsonProcessingException {

        return CommonResult.succeeded(iCommQuestionnaireAnswerService.getAnswerDetail(answerId));
    }
}
