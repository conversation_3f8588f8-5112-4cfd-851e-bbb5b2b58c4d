package com.spup.admin.controller.others;

import com.spup.commons.api.CommonResult;
import com.spup.data.entity.ArtCenterInfo;
import com.spup.admin.service.ArtCenterInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Optional;

@Tag(name = "场馆信息管理")
@RestController
@RequestMapping("/info")
public class ArtCenterInfoController {
    @Resource
    private ArtCenterInfoService artCenterInfoService;

    @Operation(summary = "获取信息")
    @RequestMapping(value = "/get/{section}",method = RequestMethod.POST)
    public CommonResult<?> get(@PathVariable ArtCenterInfo.SectionEnum section)  {
        Optional<ArtCenterInfo> infoOpt = artCenterInfoService.findBySection(section);
        ArtCenterInfo info = null;
        if(infoOpt.isPresent()){
            info = infoOpt.get();
        }
        return CommonResult.succeeded(info);
    }

    @Operation(summary = "保存信息")
    @RequestMapping(value = "/save/{section}",method = RequestMethod.POST)
    public CommonResult<?> save(@PathVariable ArtCenterInfo.SectionEnum section,
                                @RequestBody ArtCenterInfo info)  {
        info.setSection(section);
        return CommonResult.succeeded(artCenterInfoService.save(info));
    }
}
