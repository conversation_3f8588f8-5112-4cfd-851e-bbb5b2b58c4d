package com.spup.admin.controller.others;

import com.spup.commons.api.CommonResult;
import com.spup.data.entity.AppMedia;
import com.spup.admin.service.IAppMediaService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.util.DigestUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

@Tag(name = "媒体对象管理")
@RestController
@RequestMapping(value = "/media")
public class AppMediaController {
    @Resource
    private IAppMediaService iAppMediaService;

    @Operation(summary = "上传文件")
    @Parameter(name = "user", description = "{}")
    @PostMapping(value = "/upload")
    public CommonResult<?> upload(MultipartFile file, HttpServletRequest request) throws IOException {
        String md5 = DigestUtils.md5DigestAsHex(file.getInputStream());

        AppMedia old_media = iAppMediaService.getMediaByMd5(md5);
        if(old_media!=null){
            return  CommonResult.succeeded(old_media);
        }

        String openid = (String)request.getSession().getAttribute("openid");

        return iAppMediaService.saveMedia(md5,file,openid);
    }


}
