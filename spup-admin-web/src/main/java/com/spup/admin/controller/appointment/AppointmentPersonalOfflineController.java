package com.spup.admin.controller.appointment;

import com.spup.commons.api.CommonResult;
import com.spup.data.entity.appointment.AppAppointmentPersonalOffline;
import com.spup.admin.dto.PersonalOfflineListRequest;
import com.spup.admin.service.appointment.IAppAppointmentPersonalOfflineService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Tag(name = "个人线下登记")
@RestController
@RequestMapping(value = "/personalOffline")
public class AppointmentPersonalOfflineController {
    @Resource
    private IAppAppointmentPersonalOfflineService iAppAppointmentPersonalOfflineService;

    @Operation(summary = "分页查询")
    @PostMapping(value="/list/byPage")
    public CommonResult<?> listByPage (@RequestBody PersonalOfflineListRequest param)  {
        return CommonResult.succeeded(iAppAppointmentPersonalOfflineService.getList(param));
    }

    @Operation(summary = "新建")
    @PostMapping(value="/save")
    public CommonResult<?> save (@RequestBody AppAppointmentPersonalOffline personalOffline) {
        AppAppointmentPersonalOffline save = iAppAppointmentPersonalOfflineService.save(personalOffline);
        return CommonResult.succeeded(save);
    }

    @Operation(summary = "删除")
    @GetMapping(value="/delete/byId/{id}")
    public CommonResult<?> delete (@PathVariable Long id) {

        return CommonResult.succeeded(iAppAppointmentPersonalOfflineService.delete(id));
    }

    @Operation(summary = "更新")
    @PostMapping(value="/update/byId")
    public CommonResult<?> updateActivity (@RequestBody AppAppointmentPersonalOffline param)  {
        if(param.getId()==null){
            return CommonResult.failed("id未设置");
        }
        AppAppointmentPersonalOffline personalOffline = iAppAppointmentPersonalOfflineService.view(param.getId());
        if(personalOffline==null){
            return CommonResult.failed("数据不存在");
        }
        personalOffline.setPersonNum(param.getPersonNum());
        personalOffline.setRemark(param.getRemark());
        personalOffline.setVisitDate(param.getVisitDate());
        personalOffline.setVisitFypdBatch(param.getVisitFypdBatch());
        return CommonResult.succeeded(iAppAppointmentPersonalOfflineService.save(personalOffline));
    }

    @Operation(summary = "查看")
    @GetMapping(value="/view/byId/{id}")
    public CommonResult<?> viewActivity (@PathVariable Long id)  {
        return CommonResult.succeeded(iAppAppointmentPersonalOfflineService.view(id));
    }



}
