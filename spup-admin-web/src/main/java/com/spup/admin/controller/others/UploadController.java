package com.spup.admin.controller.others;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.spup.commons.api.CommonResult;
import com.spup.admin.dto.UploadRequestParam;
import com.spup.admin.service.IUploadService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;


@Tag(name = "上传")
@RestController
@RequestMapping(value="/upload")
public class UploadController {
    @Resource
    private IUploadService iUploadService;
    @Operation(summary = "图片上传")
    @PostMapping(value = "/pic")
    public CommonResult<?> uploadElement(MultipartFile file
                                        , UploadRequestParam param) {
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null) {
            throw new IllegalArgumentException();
        }
        String fileType = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);

        if (!("jpg".equalsIgnoreCase(fileType)
                || "jpeg".equalsIgnoreCase(fileType)
                || "png".equalsIgnoreCase(fileType)
                || "bmp".equalsIgnoreCase(fileType)
                || "gif".equalsIgnoreCase(fileType)
                || "mp3".equalsIgnoreCase(fileType)
                || "mp4".equalsIgnoreCase(fileType))) {

            return CommonResult.failed("不支持的格式");
        }
        ObjectNode result = iUploadService.save(file,param);
        return CommonResult.succeeded(result);
    }
}
