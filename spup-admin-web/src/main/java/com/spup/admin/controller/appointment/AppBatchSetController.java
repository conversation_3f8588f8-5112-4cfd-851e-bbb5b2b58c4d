package com.spup.admin.controller.appointment;

import com.spup.commons.api.CommonResult;
import com.spup.admin.dto.AppBatchSetDTO;
import com.spup.admin.service.appointment.IAppBatchSetService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;


@Tag(name = "场次时间设定")
@RestController
@RequestMapping(value = "/batchSet")
public class AppBatchSetController {
    @Resource
    private IAppBatchSetService iAppBatchSetService;

    @Operation(summary = "查询最新设定",description = "便于用户修改后续")
    @GetMapping(value="/getLastSet/{batchCategory}")
    public CommonResult<?> listByPage (@PathVariable Byte batchCategory)  {
        return CommonResult.succeeded(iAppBatchSetService.getLastSet(batchCategory));
    }

    @Operation(summary = "创建新设定规则")
    @PostMapping(value="/createSet")
    public CommonResult<?> createSet (@RequestBody AppBatchSetDTO appBatchSetDTO, HttpServletRequest req)  {
        String openid = (String)req.getSession().getAttribute("openid");
        iAppBatchSetService.save(appBatchSetDTO,openid);
        return CommonResult.succeeded(appBatchSetDTO);
    }
}
