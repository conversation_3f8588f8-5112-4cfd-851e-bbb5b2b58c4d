package com.spup.admin.poi;

import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;

/**
 * 支持excel2003和2007
 * <AUTHOR>
 * @version 2.0
 */

public class ExcelStyle {

    private ExcelWriter excelWriter;
    public ExcelStyle( ExcelWriter excelWriter){
        this.excelWriter = excelWriter;
    }

    public CellStyle getDefaultStyle(){
       return excelWriter.getWorkBook() .createCellStyle() ;
    }

    public  CellStyle getTitleStyle(){
        CellStyle style = excelWriter.getWorkBook().createCellStyle() ;
        style.setAlignment(HorizontalAlignment.CENTER  ) ; //ALIGN_CENTER
        style.setVerticalAlignment(VerticalAlignment.CENTER ) ;
        style.setFont( this.excelWriter .getExcelFont() .getTitleFont() ) ;
        return style;
    }

    public CellStyle getNormalStyle(){
        CellStyle style = excelWriter.getWorkBook() .createCellStyle() ;
        style.setAlignment(HorizontalAlignment.CENTER ) ;
        style.setVerticalAlignment(VerticalAlignment.CENTER ) ;
        style.setFont( this.excelWriter .getExcelFont().getDefaultFont()  ) ;
        style.setBorderTop(BorderStyle.THIN) ;
        style.setBorderBottom(BorderStyle.THIN) ;
        style.setBorderLeft(BorderStyle.THIN) ;
        style.setBorderRight(BorderStyle.THIN) ;
        return style;
    }

    public  CellStyle getNormalStyle(short fillColor){
        CellStyle style = getNormalStyle();
        style.setFillForegroundColor(fillColor) ;
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND) ;
        return style;
    }

    // private CellStyle cloneStyle(CellStyle source){
    //     CellStyle obj = this.excelWriter .getWorkBook() .createCellStyle() ;
    //     obj.setAlignment(source.getAlignment() ) ;
    //     obj.setBorderBottom(source.getBorderBottom() );
    //     obj.setBorderLeft(source.getBorderLeft() ) ;
    //     obj.setBorderRight(source.getBorderRight() ) ;
    //     obj.setBorderTop(source.getBorderTop() ) ;
    //     obj.setBottomBorderColor(source.getBottomBorderColor() ) ;
    //     obj.setDataFormat(source.getDataFormat() ) ;
    //     obj.setFillBackgroundColor(source.getFillBackgroundColor() ) ;
    //     obj.setFillForegroundColor(source.getFillForegroundColor() ) ;
    //     obj.setFillPattern(source.getFillPattern() ) ;
    //     obj.setFont(this.excelWriter .getExcelFont() .cloneFont( this.excelWriter .getWorkBook().getFontAt(source.getFontIndex() ) )) ;
    //     obj.setHidden(source.getHidden() ) ;
    //     obj.setIndention(source.getIndention() ) ;
    //     obj.setLeftBorderColor(source.getLeftBorderColor() ) ;
    //     obj.setLocked(source.getLocked() ) ;
    //     obj.setRightBorderColor(source.getRightBorderColor() ) ;
    //     obj.setRotation(source.getRotation() ) ;
    //     obj.setTopBorderColor(source.getTopBorderColor() ) ;
    //     obj.setVerticalAlignment(source.getVerticalAlignment() ) ;
    //     obj.setWrapText(source.getWrapText() ) ;
    //     return obj;
    // }

}