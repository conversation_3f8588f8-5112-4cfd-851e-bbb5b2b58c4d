package com.spup.admin.javaConfig;

import org.springframework.context.annotation.Configuration;

import com.spup.core.config.BaseOpenApiConfig;

/**
 * Admin Module OpenAPI Configuration
 * Extends BaseOpenApiConfig with admin-specific settings
 */
@Configuration
public class OpenApiConfig extends BaseOpenApiConfig {

    @Override
    protected String getApiTitle() {
        return "浦东规划馆后台管理API";
    }

    @Override
    protected String getApiDescription() {
        return "接口文档详情信息 - 基于SpringDoc OpenAPI 3";
    }
}
