package com.spup.admin.javaConfig;

import org.springframework.web.bind.annotation.RestControllerAdvice;

import com.spup.commons.api.CommonResult;
import com.spup.core.config.BaseExceptionControllerAdvice;

/**
 * Admin Module Exception Controller Advice
 * Extends BaseExceptionControllerAdvice with admin-specific exception handling
 */
@RestControllerAdvice(basePackages = "com.spup.admin")
public class ExceptionControllerAdvice extends BaseExceptionControllerAdvice {
    
    @Override
    protected String getModuleName() {
        return "Admin";
    }
    
    @Override
    protected CommonResult<?> handleModuleSpecificException(Exception e) {
        // Add admin-specific exception types here
        if (e instanceof SecurityException) {
            return CommonResult.failed("Admin security error: " + e.getMessage());
        }
        
        return null; // Let base class handle it
    }
}
