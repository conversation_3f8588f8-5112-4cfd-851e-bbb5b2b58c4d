package com.spup.admin.javaConfig;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.web.SecurityFilterChain;

/**
 * Security Configuration to work with existing token interceptor
 * Only activated when explicitly enabled via property
 */
@Configuration
@EnableWebSecurity
@ConditionalOnProperty(name = "spup.security.enabled", havingValue = "true")
public class SecurityConfig {

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            .authorizeHttpRequests(authz -> authz
                .anyRequest().permitAll()  // Allow all requests
            )
            .csrf(csrf -> csrf.disable())  // Disable CSRF
            .headers(headers -> headers.frameOptions().disable())  // Allow frames
            .httpBasic(httpBasic -> httpBasic.disable())  // Disable basic auth
            .formLogin(formLogin -> formLogin.disable());  // Disable form login

        return http.build();
    }
}
