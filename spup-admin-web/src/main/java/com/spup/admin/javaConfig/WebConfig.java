package com.spup.admin.javaConfig;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.lang.NonNull;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;

import com.spup.admin.interceptor.AdminTokenInterceptor;
import com.spup.core.config.BaseWebConfig;
import com.spup.core.interceptor.BaseTokenInterceptor;

@Configuration
public class WebConfig extends BaseWebConfig {

    @Autowired
    private AdminTokenInterceptor adminTokenInterceptor;

    /**
     * Use the Spring-managed admin token interceptor
     */
    @Override
    public BaseTokenInterceptor baseTokenInterceptor() {
        return adminTokenInterceptor;
    }

    /**
     * Add admin-specific interceptor configurations
     * Note: Base interceptor is already registered in BaseWebConfig.addInterceptors()
     * This method is for additional module-specific interceptors only
     */
    @Override
    protected void addModuleSpecificInterceptors(InterceptorRegistry registry) {
        // No additional interceptors needed for admin module
        // The base interceptor with common + module-specific exclusions is sufficient
    }

    /**
     * Get admin-specific exclude patterns
     * Note: Swagger patterns are handled in common exclusions
     */
    @Override
    protected String[] getModuleSpecificExcludePatterns() {
        return new String[]{
            "/mp/**",                   // Mini-program endpoints
            "/summary/**",              // Summary endpoints
            "/manage/**",               // Management endpoints
            "**/activity/**"            // Activity endpoints
        };
    }

    @Override
    public void addResourceHandlers(@NonNull ResourceHandlerRegistry registry) {
        // 解决静态资源无法访问
        registry.addResourceHandler("/html/**")
                .addResourceLocations("classpath:/html/");
        // 解决swagger无法访问
        registry.addResourceHandler("/swagger-ui.html")
                .addResourceLocations("classpath:/META-INF/resources/");
        // 解决swagger的js文件无法访问
        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");

    }
}