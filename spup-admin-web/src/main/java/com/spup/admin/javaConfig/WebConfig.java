package com.spup.admin.javaConfig;

import com.spup.core.config.BaseWebConfig;
import com.spup.core.interceptor.BaseTokenInterceptor;
import com.spup.admin.interceptor.AdminTokenInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.lang.NonNull;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;

@Configuration
public class WebConfig extends BaseWebConfig {

    /**
     * Create admin-specific token interceptor
     */
    @Bean
    @Override
    public BaseTokenInterceptor baseTokenInterceptor() {
        return new AdminTokenInterceptor();
    }

    /**
     * Add admin-specific interceptor configurations
     */
    @Override
    protected void addModuleSpecificInterceptors(InterceptorRegistry registry) {
        // Admin-specific interceptor patterns
        registry.addInterceptor(baseTokenInterceptor())
                .addPathPatterns("/**")
                .excludePathPatterns(getModuleSpecificExcludePatterns());
    }

    /**
     * Get admin-specific exclude patterns
     */
    @Override
    protected String[] getModuleSpecificExcludePatterns() {
        return new String[]{
            "/login/**",
            "/mp/**",
            "/summary/**"
        };
    }

    @Override
    public void addResourceHandlers(@NonNull ResourceHandlerRegistry registry) {
        // 解决静态资源无法访问
        registry.addResourceHandler("/html/**")
                .addResourceLocations("classpath:/html/");
        // 解决swagger无法访问
        registry.addResourceHandler("/swagger-ui.html")
                .addResourceLocations("classpath:/META-INF/resources/");
        // 解决swagger的js文件无法访问
        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");

    }
}