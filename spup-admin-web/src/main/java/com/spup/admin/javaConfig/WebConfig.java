package com.spup.admin.javaConfig;

import org.springframework.context.annotation.Configuration;
import org.springframework.lang.NonNull;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;

import com.spup.admin.interceptor.AdminTokenInterceptor;
import com.spup.core.config.BaseWebConfig;
import com.spup.core.interceptor.BaseTokenInterceptor;

@Configuration
public class WebConfig extends BaseWebConfig {

    /**
     * Create admin-specific token interceptor
     */
    @Override
    public BaseTokenInterceptor baseTokenInterceptor() {
        return new AdminTokenInterceptor();
    }

    /**
     * Add admin-specific interceptor configurations
     */
    @Override
    protected void addModuleSpecificInterceptors(InterceptorRegistry registry) {
        // Admin-specific interceptor patterns
        registry.addInterceptor(baseTokenInterceptor())
                .addPathPatterns("/**")
                .excludePathPatterns(getModuleSpecificExcludePatterns());
    }

    /**
     * Get admin-specific exclude patterns
     */
    @Override
    protected String[] getModuleSpecificExcludePatterns() {
        return new String[]{
            "/login/**",
            "/mp/**",
            "/summary/**",
            "/manage/**",
            "**/activity/**",
            "**/swagger-ui/**"
        };
    }

    @Override
    public void addResourceHandlers(@NonNull ResourceHandlerRegistry registry) {
        // 解决静态资源无法访问
        registry.addResourceHandler("/html/**")
                .addResourceLocations("classpath:/html/");
        // 解决swagger无法访问
        registry.addResourceHandler("/swagger-ui.html")
                .addResourceLocations("classpath:/META-INF/resources/");
        // 解决swagger的js文件无法访问
        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");

    }
}