package com.spup.admin.utils;

import org.jasypt.encryption.StringEncryptor;
import org.jasypt.encryption.pbe.PooledPBEStringEncryptor;
import org.jasypt.encryption.pbe.config.SimpleStringPBEConfig;

/**
 * Jasypt Utility for encrypting and decrypting sensitive properties
 * 
 * Usage examples:
 * 
 * // Encrypt a value
 * String encrypted = JasyptUtil.encrypt("mySecretPassword", "sensitive_data");
 * System.out.println("ENC(" + encrypted + ")");
 * 
 * // Decrypt a value
 * String decrypted = JasyptUtil.decrypt("mySecretPassword", encrypted);
 */
public class JasyptUtil {

    /**
     * Encrypt a plain text value
     * 
     * @param password The encryption password
     * @param plainText The text to encrypt
     * @return Encrypted string (base64 encoded)
     */
    public static String encrypt(String password, String plainText) {
        StringEncryptor encryptor = createEncryptor(password);
        return encryptor.encrypt(plainText);
    }

    /**
     * Decrypt an encrypted value
     * 
     * @param password The encryption password
     * @param encryptedText The encrypted text to decrypt
     * @return Decrypted plain text
     */
    public static String decrypt(String password, String encryptedText) {
        StringEncryptor encryptor = createEncryptor(password);
        return encryptor.decrypt(encryptedText);
    }

    /**
     * Create a StringEncryptor with the same configuration as the application
     */
    private static StringEncryptor createEncryptor(String password) {
        PooledPBEStringEncryptor encryptor = new PooledPBEStringEncryptor();
        SimpleStringPBEConfig config = new SimpleStringPBEConfig();
        
        config.setPassword(password);
        config.setAlgorithm("PBEWITHHMACSHA512ANDAES_256");
        config.setKeyObtentionIterations("1000");
        config.setPoolSize("1");
        config.setSaltGeneratorClassName("org.jasypt.salt.RandomSaltGenerator");
        config.setIvGeneratorClassName("org.jasypt.iv.RandomIvGenerator");
        config.setStringOutputType("base64");
        
        encryptor.setConfig(config);
        return encryptor;
    }

    /**
     * Main method for command-line encryption/decryption
     * 
     * Usage:
     * java -cp ... com.spup.utils.JasyptUtil encrypt myPassword "secret_value"
     * java -cp ... com.spup.utils.JasyptUtil decrypt myPassword "encrypted_value"
     */
    public static void main(String[] args) {
        if (args.length != 3) {
            System.out.println("Usage: JasyptUtil <encrypt|decrypt> <password> <text>");
            System.exit(1);
        }

        String operation = args[0];
        String password = args[1];
        String text = args[2];

        try {
            if ("encrypt".equalsIgnoreCase(operation)) {
                String encrypted = encrypt(password, text);
                System.out.println("Original: " + text);
                System.out.println("Encrypted: ENC(" + encrypted + ")");
            } else if ("decrypt".equalsIgnoreCase(operation)) {
                String decrypted = decrypt(password, text);
                System.out.println("Encrypted: " + text);
                System.out.println("Decrypted: " + decrypted);
            } else {
                System.out.println("Invalid operation. Use 'encrypt' or 'decrypt'");
            }
        } catch (Exception e) {
            System.err.println("Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
