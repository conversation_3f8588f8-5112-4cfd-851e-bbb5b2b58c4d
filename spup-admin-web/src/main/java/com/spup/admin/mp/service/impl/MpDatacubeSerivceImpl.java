package com.spup.admin.mp.service.impl;

import com.spup.admin.mp.entity.datacube.Article;
import com.spup.admin.mp.entity.datacube.ArticleDailyDetail;
import com.spup.admin.mp.entity.datacube.UserSummary;
import com.spup.admin.mp.service.IMpDatacubeSerivce;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.datacube.WxDataCubeArticleResult;
import me.chanjar.weixin.mp.bean.datacube.WxDataCubeArticleTotal;
import me.chanjar.weixin.mp.bean.datacube.WxDataCubeUserCumulate;
import me.chanjar.weixin.mp.bean.datacube.WxDataCubeUserSummary;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 采用第三方的WxMpService进行调佣
 */
@Service
public class MpDatacubeSerivceImpl implements IMpDatacubeSerivce {
    @Resource
    protected WxMpService wxService;
    @Override
    public List<ArticleDailyDetail> getArticleSummary(LocalDate date) throws Exception {
        Date queryDate = Date.from(date.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        String dateStr = date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        List<WxDataCubeArticleResult> articleSummaryList = wxService.getDataCubeService().getArticleSummary(queryDate, queryDate);
        List<ArticleDailyDetail> result =
                articleSummaryList.stream()
                .filter(article -> article.getRefDate().equals(dateStr))
                .map(article -> {
                    ArticleDailyDetail detail = new ArticleDailyDetail();
                    BeanUtils.copyProperties(article,detail);
                    return detail;
                })
                .collect(Collectors.toList());

        return result;
    }

    @Override
    public List<Article> getArticleTotal(LocalDate date) throws Exception {
        Date queryDate = Date.from(date.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String dateStr = date.format(dateTimeFormatter);
        List<WxDataCubeArticleTotal> articleTotalList = wxService.getDataCubeService().getArticleTotal(queryDate, queryDate);
        List<Article> result = articleTotalList.stream()
                .filter(article -> article.getRefDate().equals(dateStr))
                .map(article -> {
                    Article newArticle = new Article();
                    BeanUtils.copyProperties(article, newArticle);
                    LocalDate sendDate = LocalDate.parse(article.getRefDate(), dateTimeFormatter);
                    newArticle.setSendDate(sendDate);
                    return newArticle;
                })
                .collect(Collectors.toList());
        return result;
    }

    @Override
    public Integer getUserCumulate(LocalDate date) throws Exception {
        Date queryDate = Date.from(date.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        List<WxDataCubeUserCumulate> userCumulateList = wxService.getDataCubeService()
                .getUserCumulate(queryDate, queryDate);
        if(CollectionUtils.isEmpty(userCumulateList)){
            return 0;
        }
        WxDataCubeUserCumulate cumulate = userCumulateList.get(0);
        return cumulate.getCumulateUser();
    }

    @Override
    public List<UserSummary> getUserSummary(LocalDate date) throws Exception {
        Date queryDate = Date.from(date.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        List<WxDataCubeUserSummary> userSummaryList = wxService.getDataCubeService()
                .getUserSummary(queryDate,queryDate);
        List<UserSummary> result =
                userSummaryList.stream().map(summary -> {
                    UserSummary userSummary = new UserSummary();
                    BeanUtils.copyProperties(summary, userSummary);
                    Instant instant = summary.getRefDate().toInstant();
                    ZoneId zoneId = ZoneId.systemDefault();
                    LocalDate localDate = instant.atZone(zoneId).toLocalDate();
                    userSummary.setRefDate(localDate);
                    return userSummary;
                }).collect(Collectors.toList());
        return result;
    }
}
