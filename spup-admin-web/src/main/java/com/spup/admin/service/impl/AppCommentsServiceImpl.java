package com.spup.admin.service.impl;

import com.spup.data.dao.AppCommentsDao;
import com.spup.data.entity.AppComments;
import com.spup.admin.dto.DateQueryRequest;
import com.spup.admin.service.IAppCommentsService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class AppCommentsServiceImpl implements IAppCommentsService {
    @Resource
    private AppCommentsDao appCommentsDao;

    @Override
    public AppComments save(AppComments comments) {
        return appCommentsDao.save(comments);
    }

    @Override
    public List<AppComments> findAll(int limit) {
        List<AppComments> all = appCommentsDao.findAll();
        List<AppComments> result = all.stream()
                .sorted((o1, o2) -> o2.getCreateTime().compareTo(o1.getCreateTime()))
                .limit(limit)
                .collect(Collectors.toList());
        return result;
    }

    @Override
    public Page<AppComments> getPageList(DateQueryRequest queryRequest) {
        // 调用分页插件,访问第一页，每页2条数据
        Pageable pageable = PageRequest.of(queryRequest.getPageNum() - 1, queryRequest.getPageSize());
        // 从数据库查询
        Specification<AppComments> spec = new Specification<AppComments>() {
            // Predicate:封装了 单个的查询条件
            /**
             * Root<Users> root:查询对象的属性的封装。
             * CriteriaQuery<?> query：封装了我们要执行的查询中的各个部分的信息，select from order by
             * CriteriaBuilder cb:查询条件的构造器。定义不同的查询条件
             */
            @Override
            public Predicate toPredicate(@NonNull Root<AppComments> root, @NonNull CriteriaQuery<?> query,
                    @NonNull CriteriaBuilder cb) {
                List<Predicate> list = new ArrayList<>();
                if (queryRequest.getStartDate() != null) {
                    list.add(cb.greaterThanOrEqualTo(root.get("createTime"),
                            queryRequest.getStartDate().atTime(0, 0, 0)));// 筛选
                }
                if (queryRequest.getEndDate() != null) {
                    list.add(
                            cb.lessThanOrEqualTo(root.get("createTime"), queryRequest.getEndDate().atTime(23, 59, 59)));// 筛选
                }
                query.orderBy(cb.desc(root.get("createTime")));// 排序
                Predicate[] arr = new Predicate[list.size()];
                return cb.and(list.toArray(arr));
            }
        };
        Page<AppComments> all = appCommentsDao.findAll(spec, pageable);
        return all;
    }
}
