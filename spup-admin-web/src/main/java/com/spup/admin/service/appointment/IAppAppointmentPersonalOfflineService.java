package com.spup.admin.service.appointment;

import com.spup.data.entity.appointment.AppAppointmentPersonalOffline;
import com.spup.admin.dto.PersonalOfflineListRequest;
import org.springframework.data.domain.Page;

public interface IAppAppointmentPersonalOfflineService {
    Page<AppAppointmentPersonalOffline> getList(PersonalOfflineListRequest listParam);
    AppAppointmentPersonalOffline save(AppAppointmentPersonalOffline offline);
    AppAppointmentPersonalOffline view(Long id);
    int delete(Long id);
}
