package com.spup.admin.service.appointment;

import com.spup.data.entity.appointment.AppAppointmentSuborder;
import com.spup.admin.dto.SuborderQueryRequest;

import java.time.LocalDate;
import java.util.List;

public interface IAppAppointmentSuborderService {
    long countByStatus(SuborderQueryRequest queryParam);
    List<AppAppointmentSuborder> findByDateBetween(LocalDate startDate, LocalDate endDate);
}
