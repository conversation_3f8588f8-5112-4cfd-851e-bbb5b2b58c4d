package com.spup.admin.service.analysis;

import com.spup.data.entity.mp.MpDatacubeArticleSummary;
import com.spup.admin.dto.DateQueryRequest;
import com.spup.admin.mp.entity.datacube.Article;
import com.spup.admin.mp.entity.datacube.ArticleDailyDetail;
import org.springframework.data.domain.Page;

import java.time.LocalDate;
import java.util.List;

public interface IMpDatacubeArticleSummaryService {
    Long getArticleTotal();
    Long getArticleTotal(LocalDate start,LocalDate end);
    Long getArticleReadTotal();
    Long getArticleReadTotal(LocalDate start,LocalDate end);
    Page<MpDatacubeArticleSummary> getListByPage(DateQueryRequest queryParam);
    void saveList(List<Article> newArticleList);
    void updateList(List<ArticleDailyDetail> dailyDetailList);
}
