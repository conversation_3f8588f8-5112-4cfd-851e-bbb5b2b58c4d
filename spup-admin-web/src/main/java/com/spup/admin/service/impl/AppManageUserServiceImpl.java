package com.spup.admin.service.impl;

import com.spup.data.dao.authority.AppManageUserDao;
import com.spup.data.entity.authority.AppManageUser;
import com.spup.admin.dto.ManagerUserListRequest;
import com.spup.admin.service.IAppManageUserService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class AppManageUserServiceImpl implements IAppManageUserService {
    @Resource
    private AppManageUserDao appManageUserDao;

    @Override
    public Page<AppManageUser> getListByPage(ManagerUserListRequest listParam) {
        // 调用分页插件,访问第一页，每页2条数据
        Pageable pageable = PageRequest.of(listParam.getPageNum() - 1, listParam.getPageSize());
        // 从数据库查询
        Specification<AppManageUser> spec = new Specification<AppManageUser>() {
            // Predicate:封装了 单个的查询条件
            /**
             * Root<Users> root:查询对象的属性的封装。
             * CriteriaQuery<?> query：封装了我们要执行的查询中的各个部分的信息，select from order by
             * CriteriaBuilder cb:查询条件的构造器。定义不同的查询条件
             */
            @Override
            public Predicate toPredicate(@NonNull Root<AppManageUser> root, @NonNull CriteriaQuery<?> query,
                    @NonNull CriteriaBuilder cb) {
                List<Predicate> list = new ArrayList<>();
                if (StringUtils.hasLength(listParam.getName())) {
                    list.add(cb.like(root.get("name"), "%" + listParam.getName() + "%"));// 筛选
                }
                if (StringUtils.hasLength(listParam.getMobile())) {
                    list.add(cb.like(root.get("mobile"), "%" + listParam.getMobile() + "%"));// 筛选
                }
                if (listParam.getStatus() != null) {
                    list.add(cb.equal(root.get("status"), listParam.getStatus()));// 筛选

                }
                query.orderBy(cb.desc(root.get("id")));// 排序
                Predicate[] arr = new Predicate[list.size()];
                return cb.and(list.toArray(arr));
            }
        };
        Page<AppManageUser> all = appManageUserDao.findAll(spec, pageable);
        return all;
    }

    @Override
    public AppManageUser create(AppManageUser appManageUser) {
        return appManageUserDao.save(appManageUser);
    }

    @Override
    public AppManageUser update(AppManageUser appManageUser) {
        return appManageUserDao.save(appManageUser);
    }

    @Override
    public int delete(long id) {
        appManageUserDao.deleteById(id);
        return 1;
    }

    @Override
    public AppManageUser getUserByUnionid(String unionid) {
        Optional<AppManageUser> manageUserOptional = appManageUserDao.findByUnionid(unionid);
        if (!manageUserOptional.isPresent()) {
            return null;
        }
        return manageUserOptional.get();
    }

    @Override
    public AppManageUser view(long id) {
        Optional<AppManageUser> byId = appManageUserDao.findById(id);
        return byId.get();
    }

}
