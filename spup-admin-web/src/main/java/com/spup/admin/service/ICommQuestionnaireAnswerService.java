package com.spup.admin.service;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.spup.data.entity.CommQuestionnaireAnswer;
import com.spup.admin.dto.QuestionnaireListRequest;
import org.springframework.data.domain.Page;

import java.util.List;

public interface ICommQuestionnaireAnswerService {
    CommQuestionnaireAnswer save(CommQuestionnaireAnswer answer, String unionid);
    List<CommQuestionnaireAnswer> getAllAnswer(Long questionnaireId, String unionid);
    List<CommQuestionnaireAnswer> getAllAnswer(Long questionnaireId, QuestionnaireListRequest listParam);
    Page<CommQuestionnaireAnswer> getPageList(QuestionnaireListRequest listParam);
    ObjectNode getAnswerDetail(Long answerId) throws JsonProcessingException;

}
