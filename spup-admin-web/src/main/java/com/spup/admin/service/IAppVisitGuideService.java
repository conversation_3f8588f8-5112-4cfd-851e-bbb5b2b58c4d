package com.spup.admin.service;


import com.spup.data.entity.AppVisitGuide;
import com.spup.admin.dto.VisitGuideListRequest;
import org.springframework.data.domain.Page;

import java.util.List;

public interface IAppVisitGuideService {
    Page<AppVisitGuide> getListByPage(VisitGuideListRequest listParam);
    List<AppVisitGuide> findAllSortByCode(int limit);
    List<AppVisitGuide> findAllSortByPageViews(int limit);
    AppVisitGuide create(AppVisitGuide appVisitGuide,String openId);
    AppVisitGuide update(AppVisitGuide appVisitGuide,String openId);
    int delete(long id, String openId);

    AppVisitGuide view(long id);
}
