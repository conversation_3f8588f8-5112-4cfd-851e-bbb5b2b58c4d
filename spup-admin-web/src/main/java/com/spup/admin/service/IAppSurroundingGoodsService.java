package com.spup.admin.service;

import com.spup.core.service.BaseSurroundingGoodsService;
import com.spup.data.entity.AppSurroundingGoods;
import com.spup.admin.dto.GoodsListRequest;
import org.springframework.data.domain.Page;

/**
 * Admin Surrounding Goods Service Interface
 * Extends BaseSurroundingGoodsService with admin-specific operations
 */
public interface IAppSurroundingGoodsService extends BaseSurroundingGoodsService {

    /**
     * Get paginated goods list (admin-specific)
     */
    Page<AppSurroundingGoods> getListByPage(GoodsListRequest listParam);
}
