package com.spup.admin.service.appointment;

import com.spup.data.entity.appointment.AppAppointmentTeamOffline;
import com.spup.admin.dto.DateQueryRequest;
import org.springframework.data.domain.Page;

public interface IAppAppointmentTeamOfflineService {
    Page<AppAppointmentTeamOffline> getList(DateQueryRequest queryParam);
    AppAppointmentTeamOffline save(AppAppointmentTeamOffline offline);
    AppAppointmentTeamOffline view(Long id);
    int delete(Long id);
}
