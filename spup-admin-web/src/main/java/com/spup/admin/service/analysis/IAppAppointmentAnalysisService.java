package com.spup.admin.service.analysis;

import com.spup.data.entity.appointment.AppAppointmentAnalysis;
import com.spup.admin.dto.AnalysisListRequest;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Map;

public interface IAppAppointmentAnalysisService {
    Map<String,Object> getAnaDataFromRecord(String yyyyMMdd);
    AppAppointmentAnalysis save(Map<String,Object> map);
    List<AppAppointmentAnalysis> getAnaDataByDate(String startDate, String endDate);
    Page<AppAppointmentAnalysis> listByPage(AnalysisListRequest listParam);
}
