package com.spup.admin.service.impl;

import com.spup.data.dao.VolunteerDao;
import com.spup.data.entity.Volunteer;
import com.spup.admin.service.VolunteerService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
public class VolunteerServiceImpl implements VolunteerService {
    @Resource
    private VolunteerDao volunteerDao;
    @Override
    public  List<Map<String, String>> readXls() {
        /*ExcelReader reader = new ExcelReader(new File());
        Map<String, String> titleMap = reader.getColumnCommentMap();
        List<Map<String, String>> dataList = reader.getRowList();*/
        return null;
    }

    @Override
    public List<Volunteer> list(Volunteer.VolunteerCategoryEnum category) {
        return volunteerDao.findByCategory(category);
    }

    @Override
    public Volunteer save(Volunteer v) {
        Optional<Volunteer> volunteerOpt = volunteerDao.getByVolunteerId(v.getVolunteerId());
        if(volunteerOpt.isPresent()){
            volunteerDao.delete(volunteerOpt.get());
        }
        return volunteerDao.save(v);
    }

}
