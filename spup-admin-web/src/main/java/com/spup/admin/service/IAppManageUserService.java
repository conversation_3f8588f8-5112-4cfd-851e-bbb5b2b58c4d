package com.spup.admin.service;

import com.spup.data.entity.authority.AppManageUser;
import com.spup.admin.dto.ManagerUserListRequest;
import org.springframework.data.domain.Page;


public interface IAppManageUserService {
    Page<AppManageUser> getListByPage(ManagerUserListRequest listParam);
    AppManageUser create(AppManageUser appManageUser);
    AppManageUser update(AppManageUser appManageUse);
    int delete(long id);
    AppManageUser getUserByUnionid(String unionid);
    AppManageUser view(long id);
}
