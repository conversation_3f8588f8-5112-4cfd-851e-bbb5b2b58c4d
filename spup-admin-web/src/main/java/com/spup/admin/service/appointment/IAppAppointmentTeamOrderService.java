package com.spup.admin.service.appointment;

import com.spup.commons.api.CommonResult;
import com.spup.data.entity.appointment.AppAppointmentTeamOrder;
import com.spup.admin.dto.AppTeamOrderListRequest;
import com.spup.admin.service.IOrderService;
import org.springframework.data.domain.Page;

import java.util.List;


public interface IAppAppointmentTeamOrderService extends IOrderService {
    //对外接口
    CommonResult<?> delete(String orderNo,String unionid);
    List<AppAppointmentTeamOrder> getTeamOrderByDate(String startDate, String endDate);
    AppAppointmentTeamOrder update(AppAppointmentTeamOrder modifyOrder);

    AppAppointmentTeamOrder save(AppAppointmentTeamOrder teamOrder);
    AppAppointmentTeamOrder view(Long id);
    Page<AppAppointmentTeamOrder> getList(AppTeamOrderListRequest queryParam);
}
