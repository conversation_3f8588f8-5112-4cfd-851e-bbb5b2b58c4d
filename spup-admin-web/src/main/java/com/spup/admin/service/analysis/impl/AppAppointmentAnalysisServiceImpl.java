package com.spup.admin.service.analysis.impl;

import com.spup.data.dao.appointment.AppAppointmentAnalysisDao;
import com.spup.data.dao.appointment.AppAppointmentItemSuborderDao;
import com.spup.data.dao.appointment.AppAppointmentSuborderDao;
import com.spup.data.entity.appointment.AppAppointmentAnalysis;
import com.spup.data.entity.appointment.AppAppointmentItemSuborder;
import com.spup.data.entity.appointment.AppAppointmentSuborder;
import com.spup.admin.dto.AnalysisListRequest;
import com.spup.enums.OrderStatusEnum;
import com.spup.admin.service.analysis.IAppAppointmentAnalysisService;

import lombok.extern.slf4j.Slf4j;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AppAppointmentAnalysisServiceImpl implements IAppAppointmentAnalysisService {

        @Resource
        private AppAppointmentSuborderDao suborderDao;
        @Resource
        private AppAppointmentItemSuborderDao itemSuborderDao;

        @Resource
        private AppAppointmentAnalysisDao appointmentAnalysisDao;

        @Override
        public Map<String, Object> getAnaDataFromRecord(String yyyyMMdd) {
                List<AppAppointmentSuborder> allSubordersByDate = suborderDao.findByBatchDate(yyyyMMdd);
                List<AppAppointmentItemSuborder> allItemSubordersByDate = itemSuborderDao.findByBatchDate(yyyyMMdd);
                Map<String, Object> map = new HashMap<>();
                if (CollectionUtils.isEmpty(allSubordersByDate)) {
                        map.put("analysis_date", yyyyMMdd);
                        map.put("ticket_reserve_total", 0);
                        map.put("ticket_reserve_am", 0);
                        map.put("ticket_reserve_pm", 0);
                        map.put("ticket_reserve_refund_active", 0);
                        map.put("ticket_reserve_refund_passive", 0);
                        map.put("ticket_checkin_total", 0);
                        map.put("ticket_checkin_am", 0);
                        map.put("ticket_checkin_pm", 0);
                        map.put("item_reserve_total", 0);
                        map.put("item_reserve_refund_active", 0);
                        map.put("item_reserve_refund_passive", 0);
                        map.put("item_checkin_total", 0);
                        map.put("item_checkin_am", 0);
                        map.put("item_checkin_pm", 0);
                        map.put("analysis_date", yyyyMMdd);
                        return map;
                }
                String amSplitPm = "1300";
                // 日期时间分界线
                LocalDateTime localDateTime = LocalDateTime.parse(yyyyMMdd + amSplitPm,
                                DateTimeFormatter.ofPattern("yyyyMMddHHmm"));
                List<AppAppointmentSuborder> ticketReserveTotalList = allSubordersByDate.stream()
                                .filter(suborder -> suborder.getSuborderStatus() != OrderStatusEnum.CANCELED.getCode())
                                .collect(Collectors.toList());
                // 预约总数
                int ticket_reserve_total = ticketReserveTotalList.size();

                // 主动取消预约总数
                List<AppAppointmentSuborder> ticketReserveRefundActiveList = allSubordersByDate.stream()
                                .filter(suborder -> suborder.getSuborderStatus() == OrderStatusEnum.CANCELED.getCode())
                                .collect(Collectors.toList());
                int ticket_reserve_refund_active = ticketReserveRefundActiveList.size();

                // 上午预约总数;
                List<AppAppointmentSuborder> ticketReserveAmList = ticketReserveTotalList.stream()
                                .filter(subOrder -> subOrder.getBatchEndTime().compareTo(localDateTime) < 0)
                                .collect(Collectors.toList());
                // subOrder.getStatus() != OrderStatusEnum.CANCELED
                int ticket_reserve_am = ticketReserveAmList.size();

                int ticket_reserve_pm = ticket_reserve_total - ticket_reserve_am;

                // 爽约总数
                List<AppAppointmentSuborder> ticketReserveRefundPassiveList = allSubordersByDate.stream()
                                .filter(suborder -> suborder.getSuborderStatus() == OrderStatusEnum.BREAKED.getCode())
                                .collect(Collectors.toList());

                int ticket_reserve_refund_passive = ticketReserveRefundPassiveList.size();

                // 核销总数
                List<AppAppointmentSuborder> ticketCheckinTotalList = allSubordersByDate.stream()
                                .filter(suborder -> suborder.getSuborderStatus() == OrderStatusEnum.FINISHED.getCode())
                                .collect(Collectors.toList());

                int ticket_checkin_total = ticketCheckinTotalList.size();

                List<AppAppointmentSuborder> ticketCheckinAmList = ticketCheckinTotalList.stream()
                                .filter(appAppointmentSuborder -> appAppointmentSuborder.getUpdateTime()
                                                .compareTo(localDateTime) < 0)
                                .collect(Collectors.toList());

                int ticket_checkin_am = ticketCheckinAmList.size();
                int ticket_checkin_pm = ticket_checkin_total - ticket_checkin_am;

                // 展项飞阅浦东
                List<AppAppointmentItemSuborder> itemReserveTotalList = allItemSubordersByDate.stream()
                                .filter(suborder -> suborder.getSuborderStatus() != OrderStatusEnum.CANCELED.getCode())
                                .collect(Collectors.toList());

                int item_reserve_total = itemReserveTotalList.size();

                List<AppAppointmentItemSuborder> itemReserveAmList = itemReserveTotalList.stream()
                                .filter(suborder -> suborder.getBatchStartTime().compareTo(localDateTime) <= 0)
                                .collect(Collectors.toList());

                int item_reserve_am = itemReserveAmList.size();
                int item_reserve_pm = item_reserve_total - item_reserve_am;
                // 取消预约总数
                List<AppAppointmentItemSuborder> itemReserveRefundActiveList = allItemSubordersByDate.stream()
                                .filter(suborder -> suborder.getSuborderStatus() == OrderStatusEnum.CANCELED.getCode())
                                .collect(Collectors.toList());

                int item_reserve_refund_active = itemReserveRefundActiveList.size();

                // 爽约总数
                List<AppAppointmentItemSuborder> itemReserveRefundPassiveList = allItemSubordersByDate.stream()
                                .filter(suborder -> suborder.getSuborderStatus() == OrderStatusEnum.BREAKED.getCode())
                                .collect(Collectors.toList());

                int item_reserve_refund_passive = itemReserveRefundPassiveList.size();

                // 核销总数
                List<AppAppointmentItemSuborder> itemCheckinTotalList = allItemSubordersByDate.stream()
                                .filter(suborder -> suborder.getSuborderStatus() == OrderStatusEnum.FINISHED.getCode())
                                .collect(Collectors.toList());

                int item_checkin_total = itemCheckinTotalList.size();

                List<AppAppointmentItemSuborder> itemCheckinAmList = itemCheckinTotalList.stream()
                                .filter(suborder -> suborder.getUpdateTime().compareTo(localDateTime) <= 0)
                                .collect(Collectors.toList());
                int item_checkin_am = itemCheckinAmList.size();
                int item_checkin_pm = item_checkin_total - item_checkin_am;

                map.put("analysis_date", yyyyMMdd);
                map.put("ticket_reserve_total", ticket_reserve_total);
                map.put("ticket_reserve_am", ticket_reserve_am);
                map.put("ticket_reserve_pm", ticket_reserve_pm);

                map.put("ticket_reserve_refund_active", ticket_reserve_refund_active);
                map.put("ticket_reserve_refund_passive", ticket_reserve_refund_passive);

                map.put("ticket_checkin_total", ticket_checkin_total);
                map.put("ticket_checkin_am", ticket_checkin_am);
                map.put("ticket_checkin_pm", ticket_checkin_pm);

                map.put("item_reserve_total", item_reserve_total);
                map.put("item_reserve_am", item_reserve_am);
                map.put("item_reserve_pm", item_reserve_pm);

                map.put("item_reserve_refund_active", item_reserve_refund_active);
                map.put("item_reserve_refund_passive", item_reserve_refund_passive);
                map.put("item_checkin_total", item_checkin_total);
                map.put("item_checkin_am", item_checkin_am);
                map.put("item_checkin_pm", item_checkin_pm);

                return map;
        }

        public List<AppAppointmentAnalysis> getAnaDataByDate(String startDate, String endDate) {
                List<AppAppointmentAnalysis> appAppointmentAnalyses = appointmentAnalysisDao
                                .findByAnalysisDateBetweenOrderByAnalysisDateDesc(startDate, endDate);
                return appAppointmentAnalyses;
        }

        public Page<AppAppointmentAnalysis> listByPage(AnalysisListRequest listParam) {
                // 调用分页插件,访问第一页，每页2条数据
                Pageable pageable = PageRequest.of(listParam.getPageNum() - 1, listParam.getPageSize());
                // 从数据库查询
                Specification<AppAppointmentAnalysis> spec = new Specification<AppAppointmentAnalysis>() {
                        // Predicate:封装了 单个的查询条件
                        /**
                         * Root<Users> root:查询对象的属性的封装。
                         * CriteriaQuery<?> query：封装了我们要执行的查询中的各个部分的信息，select from order by
                         * CriteriaBuilder cb:查询条件的构造器。定义不同的查询条件
                         */
                        @Override
                        public Predicate toPredicate(@NonNull Root<AppAppointmentAnalysis> root,
                                        @NonNull CriteriaQuery<?> query,
                                        @NonNull CriteriaBuilder cb) {
                                List<Predicate> list = new ArrayList<>();
                                if (listParam.getStartDate() != null) {
                                        list.add(cb.greaterThanOrEqualTo(root.get("analysisDate"),
                                                        listParam.getStartDate().format(
                                                                        DateTimeFormatter.ofPattern("yyyyMMdd"))));// 筛选
                                }
                                if (listParam.getEndDate() != null) {
                                        list.add(cb.lessThanOrEqualTo(root.get("analysisDate"), listParam.getEndDate()
                                                        .format(DateTimeFormatter.ofPattern("yyyyMMdd"))));// 筛选
                                }
                                query.orderBy(cb.desc(root.get("analysisDate")));// 排序
                                Predicate[] arr = new Predicate[list.size()];
                                return cb.and(list.toArray(arr));
                        }
                };
                Page<AppAppointmentAnalysis> all = appointmentAnalysisDao.findAll(spec, pageable);
                log.info("🔍 BREAKPOINT TEST: all:{}", all);
                return all;
        }

        @Override
        public AppAppointmentAnalysis save(Map<String, Object> map) {
                AppAppointmentAnalysis analysis = new AppAppointmentAnalysis();
                analysis.setAnalysisDate((String) map.get("analysis_date"));
                analysis.setTicketReserveTotal((Integer) map.get("ticket_reserve_total"));
                analysis.setTicketReserveAm((Integer) map.get("ticket_reserve_am"));
                analysis.setTicketReservePm((Integer) map.get("ticket_reserve_pm"));
                analysis.setTicketReserveRefundActive((Integer) map.get("ticket_reserve_refund_active"));
                analysis.setTicketReserveRefundPassive((Integer) map.get("ticket_reserve_refund_passive"));
                analysis.setTicketCheckinTotal((Integer) map.get("ticket_checkin_total"));
                analysis.setTicketCheckinAm((Integer) map.get("ticket_checkin_am"));
                analysis.setTicketCheckinPm((Integer) map.get("ticket_checkin_pm"));
                analysis.setItemReserveTotal((Integer) map.get("item_reserve_total"));
                analysis.setItemReserveAm((Integer) map.get("item_reserve_am"));
                analysis.setItemReservePm((Integer) map.get("item_reserve_pm"));
                analysis.setItemReserveRefundActive((Integer) map.get("item_reserve_refund_active"));
                analysis.setItemReserveRefundPassive((Integer) map.get("item_reserve_refund_passive"));
                analysis.setItemCheckinTotal((Integer) map.get("item_checkin_total"));
                analysis.setItemCheckinAm((Integer) map.get("item_checkin_am"));
                analysis.setItemCheckinPm((Integer) map.get("item_checkin_pm"));

                LocalDateTime now = LocalDateTime.now();
                analysis.setCreateTime(now);
                analysis.setUpdateTime(now);
                analysis.setCreateBy("AppAppointmentAnalysisServiceImpl");
                analysis.setUpdateBy("AppAppointmentAnalysisServiceImpl");

                return appointmentAnalysisDao.save(analysis);
        }
}
