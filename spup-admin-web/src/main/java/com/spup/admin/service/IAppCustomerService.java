package com.spup.admin.service;

import com.spup.core.service.BaseCustomerService;
import com.spup.data.entity.authority.AppCustomer;
import com.spup.admin.dto.CustomerListRequest;
import org.springframework.data.domain.Page;

/**
 * Admin Customer Service Interface
 * Extends BaseCustomerService with admin-specific operations
 */
public interface IAppCustomerService extends BaseCustomerService {

    /**
     * Get paginated customer list (admin-specific)
     */
    Page<AppCustomer> getPageList(CustomerListRequest param);
}
