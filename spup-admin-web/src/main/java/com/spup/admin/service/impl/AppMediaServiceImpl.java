package com.spup.admin.service.impl;

import com.spup.commons.api.CommonResult;
import com.spup.commons.utils.DateTimeUtil;
import com.spup.data.dao.AppMediaDao;
import com.spup.data.entity.AppMedia;
import com.spup.admin.service.IAppMediaService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.Calendar;
import java.util.Optional;

@Service
@PropertySource(value = "classpath:/fileConfig_${spring.profiles.active}.properties", ignoreResourceNotFound = true)
public class AppMediaServiceImpl implements IAppMediaService {

    @Resource
    private AppMediaDao appMediaDao;
    @Value("${fileTempPath:${user.home}/spup-media/temp/}")
    private String tempPath;
    @Value("${fileSavePath:${user.home}/spup-media/upload/}")
    private String savePath;
    @Value("${fileWebPath:/upload/}")
    private String webPath;

    @Override
    public AppMedia getMediaByMd5(String md5) {
        Optional<AppMedia> mediaOptional = appMediaDao.findByMd5Sum(md5);
        if(!mediaOptional.isPresent()){
            return null;
        }
        return mediaOptional.get();
    }

    @Override
    public CommonResult<?> saveMedia(String md5, MultipartFile file, String openid) {
        Calendar now = Calendar.getInstance();
        String yyyyMMdd = DateTimeUtil.getTime("yyyyMMdd",now);
        String yyyyMMddhhmmss = DateTimeUtil.getTime("yyyyMMddhhmmss",now);
        File directory = new File(tempPath + yyyyMMdd);
        File directory2 =  new File(savePath + yyyyMMdd);
        File localFile = new File(tempPath + yyyyMMdd + File.separator + yyyyMMddhhmmss+"_"+file.getOriginalFilename());

        try {
            if(!directory.exists()){
                directory.mkdirs();
            }
            if(!directory2.exists()){
                directory2.mkdirs();
            }
            file.transferTo(localFile);
        } catch (IOException e) {
            e.printStackTrace();
            return CommonResult.failed(e.getMessage());
        }

        localFile.renameTo(new File(savePath + yyyyMMdd + File.separator +yyyyMMddhhmmss+"_"+file.getOriginalFilename()));

        AppMedia media = new AppMedia();
        String originalFilename = file.getOriginalFilename();
        if(originalFilename==null){
            return CommonResult.failed("文件名为空");
        }
        String fileType = originalFilename.substring(originalFilename.lastIndexOf("."));

        String fileName = originalFilename.substring(0,originalFilename.lastIndexOf("."));

        Long size = file.getSize();

        media.setMediaName(fileName);
        media.setMediaSize(size);
        media.setMediaType(fileType);
        media.setMediaShowname(fileName);
        media.setMd5Sum(md5);
        media.setMediaStatus((byte)1);

        media.setMediaSrc(webPath+yyyyMMdd+File.separator+ yyyyMMddhhmmss+"_"+file.getOriginalFilename());

        AppMedia save = appMediaDao.save(media);

        return CommonResult.succeeded(save);
    }
}
