package com.spup.admin.service;


import com.spup.data.entity.authority.AppCustomerContacts;

import java.util.List;

public interface IAppCustomerContactsService {
    AppCustomerContacts save(AppCustomerContacts contacts);
    public AppCustomerContacts modify(AppCustomerContacts contacts);
    public int delete(Long contactId);
    public List<AppCustomerContacts> getListByOwner(String ownerUnionid);
    public List<AppCustomerContacts> getListByOwner(String yyyyMMdd,String ownerUnionid);
}
