package com.spup.admin.service.impl;

import com.spup.data.dao.CommQuestionnaireDao;
import com.spup.data.entity.CommQuestionnaire;
import com.spup.admin.dto.QuestionnaireListRequest;
import com.spup.admin.service.ICommQuestionnaireService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class CommQuestionnaireServiceImpl implements ICommQuestionnaireService {
    @Resource
    private CommQuestionnaireDao commQuestionnaireDao;

    public CommQuestionnaire getQuestionnaireById(Long id) {
        Optional<CommQuestionnaire> questionnaireOptional = commQuestionnaireDao.findById(id);
        if (!questionnaireOptional.isPresent()) {
            return null;
        }
        return questionnaireOptional.get();
    }

    @Override
    public Page<CommQuestionnaire> getPageList(QuestionnaireListRequest listParam) {
        // 调用分页插件
        Pageable pageable = PageRequest.of(listParam.getPageNum() - 1, listParam.getPageSize());
        // 从数据库查询
        Specification<CommQuestionnaire> spec = new Specification<CommQuestionnaire>() {
            // Predicate:封装了 单个的查询条件
            /**
             * Root<Users> root:查询对象的属性的封装。
             * CriteriaQuery<?> query：封装了我们要执行的查询中的各个部分的信息，select from order by
             * CriteriaBuilder cb:查询条件的构造器。定义不同的查询条件
             */
            @Override
            public Predicate toPredicate(@NonNull Root<CommQuestionnaire> root, @NonNull CriteriaQuery<?> query,
                    @NonNull CriteriaBuilder cb) {
                List<Predicate> list = new ArrayList<>();
                if (StringUtils.hasLength(listParam.getTitle())) {
                    list.add(cb.like(root.get("title"), "%" + listParam.getTitle() + "%"));// 筛选
                }
                if (listParam.getStartDate() != null) {
                    list.add(
                            cb.greaterThanOrEqualTo(root.get("validEndDate"), listParam.getStartDate().atStartOfDay()));// 筛选
                }
                if (listParam.getEndDate() != null) {
                    list.add(cb.lessThanOrEqualTo(root.get("validStartDate"),
                            listParam.getEndDate().plusDays(1).atStartOfDay()));// 筛选
                }
                query.orderBy(cb.desc(root.get("id")));// 排序
                Predicate[] arr = new Predicate[list.size()];
                return cb.and(list.toArray(arr));
            }
        };

        Page<CommQuestionnaire> all = commQuestionnaireDao.findAll(spec, pageable);
        return all;
    }

}
