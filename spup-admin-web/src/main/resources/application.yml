server:
  port: 8080

# Enable Spring Security for admin module
spup:
  security:
    enabled: true

jasypt:
  encryptor:
    password: ${JASYPT_ENCRYPTOR_PASSWORD:myDefaultPassword}
    algorithm: PBEWITHHMACSHA512ANDAES_256
    iv-generator-classname: org.jasypt.iv.RandomIvGenerator

spring:
  jmx:
    enabled: false
  servlet:
    multipart:
      max-request-size: 100MB
      max-file-size: 100MB
  profiles:
    active: server
  mvc:
    pathmatch:
      matching-strategy: ant-path-matcher
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
  jpa:
    show-sql: true
    #关闭懒加载 否则通过id查询有问题
    properties:
      hibernate:
        "[enable_lazy_load_no_trans]": true
        dialect: org.hibernate.dialect.MySQL5InnoDBDialect
  # DevTools configuration for hot reload
  devtools:
    restart:
      enabled: true
      additional-paths: src/main/java
    livereload:
      enabled: true

jwt:
  sign:
    string: '<EMAIL>'
  expires: 120
# appointment:
#   config:
#     all:
#       weekdayList:
#         - MONDAY
#       holidayList:
#         - !!timestamp 2025-01-01
#         - !!timestamp 2025-01-28
#         - !!timestamp 2025-01-29
#         - !!timestamp 2025-01-30
#         - !!timestamp 2025-01-31
#         - !!timestamp 2025-04-04
#         - !!timestamp 2025-05-01
#         - !!timestamp 2025-05-02
#         - !!timestamp 2025-05-31
#         - !!timestamp 2025-10-01
#         - !!timestamp 2025-10-02
#         - !!timestamp 2025-10-03
#         - !!timestamp 2025-10-06
#         - !!timestamp 2026-01-01
#     L20230906T20230915:
#       weekdayList:
#         - SUNDAY
#         - SATURDAY
#       holidayList:
#     fypd:
#       weekdayList:
#       holidayList:
#     team:
#       weekdayList:
#         - SUNDAY
#         - SATURDAY
#         - MONDAY
#       holidayList:
#     L20240716T20241231:
#       weekdayList:
#         - SUNDAY
#         - SATURDAY
#         - FRIDAY
#         - WEDNESDAY
#         - MONDAY
#       holidayList:
#         - !!timestamp 2025-01-01
#         - !!timestamp 2025-01-28
#         - !!timestamp 2025-01-29
#         - !!timestamp 2025-01-30
#         - !!timestamp 2025-01-31
#         - !!timestamp 2025-04-04
#         - !!timestamp 2025-05-01
#         - !!timestamp 2025-05-02
#         - !!timestamp 2025-05-31
#         - !!timestamp 2025-10-01
#         - !!timestamp 2025-10-02
#         - !!timestamp 2025-10-03
#         - !!timestamp 2025-10-06
