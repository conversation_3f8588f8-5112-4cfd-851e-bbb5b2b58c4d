server:
  port: 8080
  servlet:
    context-path: /spup-admin

# spring:
#   datasource:
#     driverClassName: com.mysql.cj.jdbc.Driver
#     url: ENC(KMaps/3VOvTks9Uh8psRq3NJyCTl/uLilUxwWatsKkuetwAHxbh82NDAoze7LK8cS0Vb755Hrrm/Xb3AMCwXrh0pjEvnqSnZ8D5VDMCLCox7ByncVc1PPXd8sWAiWKwjdq2jSePS1xF+05iFe2Q324HvSseTYntqX7zoD2r6htzzSrfkXbkl6/d/gbnB/DsNAdMmkO7VTG8GYRCUC4Pkyw==)
#     username: E<PERSON>(omRqLbdy2HHFc1CFs/Tx32xnKse0WctI2pspDDtqRwmLymmxDNSWupyUqLPqjGpR)
#     password: ENC(Fw7R5/40E7ay4EgRV/68LVCM7qKlCMOB63rwS1ZVrOkHcfOwaLkP225C5k7DBIWMsVq6oNJ5/7L9tICOCC85Rw==)
      
# wx:
#   mp:
#     appId: wxb971efaed01158f4
#     secret: ENC(JSAI2UIpUO9MukuIe4NsMS9pS59TPNtyUckMp+VuRAfY0EGXPKQTfF3cI8OyxVjwY/qvh/Tjm7FKM5NfG2sUM205pNmP15VfiItNcfmRqhk=)
#   open:
#     appId: wx7429408673925274
#     secret: ENC(p97DrPLUlOFg5HVtUrDgUgG7/MMtNYWm8DlEdrRq5urnqGnijAubtGDdB71sDB7alIRqWzRfEI7yvuiS8D1lGqJuQo3RcXvdW+Zwt0jpyOc=)

spring:
  datasource:
    driverClassName: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************************************************************
    username: root
    # For development, you can use plain text or encrypted
    password: '123456'
    # password: ENC(your_encrypted_password_here)
  
  jpa:
    hibernate:
      ddl-auto: update

wx:
  open:
    appId: wx7429408673925274
    secret: af27750a173c3c53a3a69291aa36c407

manager:
  unionid: ojqzL0-hOlek3HMyLjhvKjTfDnnA
