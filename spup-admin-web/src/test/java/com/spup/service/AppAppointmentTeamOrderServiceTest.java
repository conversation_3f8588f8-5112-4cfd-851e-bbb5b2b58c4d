package com.spup.service;

import com.spup.admin.dto.AppTeamOrderListRequest;
import com.spup.data.entity.appointment.AppAppointmentTeamOrder;
import com.spup.admin.service.appointment.IAppAppointmentTeamOrderService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;
import org.springframework.test.context.TestPropertySource;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test for AppAppointmentTeamOrderService to verify it handles empty results correctly
 */
@SpringBootTest
@TestPropertySource(properties = {
    "spring.datasource.url=jdbc:h2:mem:testdb",
    "spring.datasource.driver-class-name=org.h2.Driver",
    "spring.jpa.hibernate.ddl-auto=create-drop"
})
public class AppAppointmentTeamOrderServiceTest {

    @Resource
    private IAppAppointmentTeamOrderService teamOrderService;

    @Test
    public void testGetListWithNoRecords() {
        // Test the exact scenario from your question
        AppTeamOrderListRequest queryParam = new AppTeamOrderListRequest();
        queryParam.setExhibitionNo("1");
        queryParam.setTeamName(null);
        queryParam.setStartDate(null);
        queryParam.setEndDate(null);
        queryParam.setOrderStatus(null);
        queryParam.setMethod(null);
        queryParam.setPageNum(1);
        queryParam.setPageSize(10);

        // This should NOT throw an exception
        assertDoesNotThrow(() -> {
            Page<AppAppointmentTeamOrder> result = teamOrderService.getList(queryParam);
            
            // Verify the result is a valid empty page
            assertNotNull(result, "Result should not be null");
            assertTrue(result.getContent().isEmpty(), "Content should be empty when no records exist");
            assertEquals(0, result.getTotalElements(), "Total elements should be 0");
            assertEquals(0, result.getNumberOfElements(), "Number of elements should be 0");
            assertEquals(1, result.getTotalPages(), "Total pages should be 1 for empty result");
            assertEquals(0, result.getNumber(), "Page number should be 0 (0-based)");
            
            System.out.println("✅ Test passed: No exception thrown with empty database");
            System.out.println("Result: " + result);
        });
    }

    @Test
    public void testGetListWithAllNullParameters() {
        // Test with all parameters null
        AppTeamOrderListRequest queryParam = new AppTeamOrderListRequest();
        queryParam.setPageNum(1);
        queryParam.setPageSize(10);

        // This should also NOT throw an exception
        assertDoesNotThrow(() -> {
            Page<AppAppointmentTeamOrder> result = teamOrderService.getList(queryParam);
            
            assertNotNull(result, "Result should not be null even with all null parameters");
            System.out.println("✅ Test passed: No exception with all null parameters");
        });
    }

    @Test
    public void testGetListWithInvalidExhibitionNo() {
        // Test with non-existent exhibition number
        AppTeamOrderListRequest queryParam = new AppTeamOrderListRequest();
        queryParam.setExhibitionNo("999"); // Non-existent exhibition
        queryParam.setPageNum(1);
        queryParam.setPageSize(10);

        assertDoesNotThrow(() -> {
            Page<AppAppointmentTeamOrder> result = teamOrderService.getList(queryParam);
            
            assertNotNull(result, "Result should not be null for non-existent exhibition");
            assertTrue(result.getContent().isEmpty(), "Should return empty result for non-existent exhibition");
            System.out.println("✅ Test passed: No exception with non-existent exhibition");
        });
    }
}
