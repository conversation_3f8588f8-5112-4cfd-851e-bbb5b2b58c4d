#!/bin/bash

# Local Run Script for app-spup-admin
# This script provides different ways to run the application locally

echo "🚀 app-spup-admin Local Run Options"
echo "==================================="

# Configuration
APP_NAME="spup-admin"
MAIN_CLASS="com.spup.SpupAdmin"
SERVER_PORT="8888"
PROFILE="dev"

echo ""
echo "📋 Configuration:"
echo "================="
echo "App Name: $APP_NAME"
echo "Main Class: $MAIN_CLASS"
echo "Server Port: $SERVER_PORT"
echo "Profile: $PROFILE"

echo ""
echo "🛠️ Available Run Methods:"
echo "========================"

case "$1" in
    "maven")
        echo "🔧 Running with Maven Spring Boot Plugin..."
        mvn spring-boot:run \
            -Dspring-boot.run.profiles=$PROFILE \
            -Dspring-boot.run.arguments="--server.port=$SERVER_PORT"
        ;;
    "jar")
        echo "📦 Building and running JAR..."
        echo "Step 1: Building JAR..."
        mvn clean package -DskipTests
        
        if [ $? -eq 0 ]; then
            echo "Step 2: Running JAR..."
            java -Dspring.profiles.active=$PROFILE \
                 -jar target/$APP_NAME-0.2.war \
                 --server.port=$SERVER_PORT
        else
            echo "❌ Build failed!"
            exit 1
        fi
        ;;
    "debug")
        echo "🐛 Running with debug enabled..."
        mvn spring-boot:run \
            -Dspring-boot.run.profiles=$PROFILE \
            -Dspring-boot.run.arguments="--server.port=$SERVER_PORT" \
            -Dspring-boot.run.jvmArguments="-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005"
        ;;
    "classpath")
        echo "🔍 Running with explicit classpath..."
        
        # Build classpath
        echo "Building classpath..."
        mvn dependency:build-classpath -Dmdep.outputFile=target/classpath.txt -q
        
        if [ -f target/classpath.txt ]; then
            CLASSPATH=$(cat target/classpath.txt):target/classes
            echo "Running with classpath..."
            java -Dspring.profiles.active=$PROFILE \
                 -cp "$CLASSPATH" \
                 $MAIN_CLASS \
                 --server.port=$SERVER_PORT
        else
            echo "❌ Failed to build classpath"
            exit 1
        fi
        ;;
    "check")
        echo "🔍 Checking application setup..."
        
        echo ""
        echo "1️⃣ Checking if main class exists..."
        if [ -f "src/main/java/com/spup/SpupAdmin.java" ]; then
            echo "✅ Main class source file exists"
        else
            echo "❌ Main class source file not found"
        fi
        
        echo ""
        echo "2️⃣ Checking if compiled class exists..."
        if [ -f "target/classes/com/spup/SpupAdmin.class" ]; then
            echo "✅ Compiled class exists"
        else
            echo "❌ Compiled class not found - run 'mvn compile' first"
        fi
        
        echo ""
        echo "3️⃣ Checking dependencies..."
        mvn dependency:tree | head -20
        
        echo ""
        echo "4️⃣ Checking Spring Boot plugin..."
        if grep -q "spring-boot-maven-plugin" pom.xml; then
            echo "✅ Spring Boot Maven plugin configured"
        else
            echo "❌ Spring Boot Maven plugin not found in pom.xml"
        fi
        ;;
    *)
        echo ""
        echo "📖 Usage:"
        echo "========="
        echo "$0 maven      - Run with Maven Spring Boot plugin (recommended)"
        echo "$0 jar        - Build JAR and run"
        echo "$0 debug      - Run with debug port 5005 enabled"
        echo "$0 classpath  - Run with explicit classpath"
        echo "$0 check      - Check application setup"
        echo ""
        echo "🎯 Quick Start:"
        echo "==============="
        echo "For development: $0 maven"
        echo "For debugging:   $0 debug"
        echo "For production:  $0 jar"
        echo ""
        echo "🔧 Troubleshooting:"
        echo "==================="
        echo "If you get 'main class not found' error:"
        echo "1. Run: $0 check"
        echo "2. Run: mvn clean compile"
        echo "3. Try: $0 maven"
        echo ""
        echo "📝 Examples:"
        echo "============"
        echo "# Run normally"
        echo "$0 maven"
        echo ""
        echo "# Run with debugging"
        echo "$0 debug"
        echo "# Then connect debugger to localhost:5005"
        echo ""
        echo "# Check setup"
        echo "$0 check"
        ;;
esac
